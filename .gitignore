# dependencies
node_modules
.pnp
.pnp.js

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# local env files
.env*.local
.env

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# tempo
**/tempobook/dynamic/
**/tempobook/storyboards/
# Ignore node_modules directory
node_modules/

# Ignore build and dist directories
build/
dist/

# Ignore Next.js specific files
.next/
.next/build/

# Ignore TypeScript and JavaScript build artifacts
*.js
*.d.ts
*.tsbuildinfo

# Ignore IDE and editor files
.vscode/
.idea/
.editorconfig

# Ignore operating system files
.DS_Store
Thumbs.db

# Ignore log files
*.log

# Ignore MongoDB data directory
data/

# Ignore environment variables file
.env

# Ignore Next.js configuration files
next.config.js
next-i18next.config.js

# Ignore other miscellaneous files
coverage/
jest/
 lint-staged.config.js
 package-lock.json
 yarn.lock