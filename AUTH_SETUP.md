# Clean NextAuth.js Authentication Setup

This project has been completely rebuilt with a clean NextAuth.js authentication system. All custom JWT authentication and complex state management has been removed.

## 🎯 What's New

### ✅ Clean Implementation
- **NextAuth.js only** - No custom JWT tokens or localStorage
- **MongoDB native driver** - Simple database operations
- **Simple Redux integration** - Only mirrors NextAuth session state
- **Role-based access control** - Two roles: `admin` and `user`
- **Clean components** - No infinite loops or complex state sync

### ❌ Removed
- Custom JWT authentication APIs
- localStorage token storage
- Complex auth caching and admin verification
- AuthProvider, AuthContext, useAuth hook
- Custom middleware and auth utilities
- All conflicting authentication logic

## 🚀 Quick Start

### 1. Environment Variables
Create a `.env.local` file:

```bash
# NextAuth
NEXTAUTH_SECRET=your-secret-key-here
NEXTAUTH_URL=http://localhost:3000

# MongoDB
MONGODB_URI=mongodb://localhost:27017/toolbox
```

### 2. Install Dependencies
```bash
npm install
```

### 3. Create Admin User
```bash
npm run seed-admin
```

This creates an admin user with:
- Email: `<EMAIL>`
- Password: `admin123`
- Role: `admin`

### 4. Start Development Server
```bash
npm run dev
```

## 🔐 Authentication Flow

### Login Process
1. User visits `/login`
2. Enters credentials
3. NextAuth validates against MongoDB
4. Session created with user role
5. Redux state synced with session
6. Redirect based on role:
   - Admin → `/admin`
   - User → `/dashboard`

### Registration Process
1. User visits `/register`
2. Enters details
3. User created in MongoDB with role `user`
4. Redirect to login page

### Role-Based Access
- **Public pages**: Home, tools, blog
- **User pages**: Dashboard (requires login)
- **Admin pages**: Admin panel (requires admin role)

## 🏗️ Architecture

### Core Files
```
src/
├── lib/
│   ├── auth.ts              # NextAuth configuration
│   └── mongo.ts             # MongoDB connection
├── pages/
│   ├── api/auth/
│   │   ├── [...nextauth].ts # NextAuth API route
│   │   └── register.ts      # Registration API
│   ├── login.tsx            # Login page
│   ├── register.tsx         # Registration page
│   ├── admin.tsx            # Admin dashboard
│   └── dashboard.tsx        # User dashboard
├── components/
│   ├── auth/
│   │   └── RequireRole.tsx  # Role protection component
│   ├── providers/
│   │   └── SessionProvider.tsx # NextAuth + Redux integration
│   └── Navbar.tsx           # Role-based navigation
└── redux/
    └── slices/
        └── authSlice.ts     # Simple auth state mirror
```

### Database Schema
```javascript
// Users collection
{
  _id: ObjectId,
  name: String,
  email: String (unique, lowercase),
  password: String (bcrypt hashed),
  role: "admin" | "user",
  createdAt: Date,
  updatedAt: Date
}
```

## 🛡️ Security Features

- **Secure password hashing** with bcryptjs
- **JWT sessions** managed by NextAuth
- **Role-based route protection**
- **CSRF protection** via NextAuth
- **Secure cookies** in production
- **No sensitive data in localStorage**

## 🎨 UI Components

### Protected Routes
```tsx
import { RequireRole } from "@/components/auth/RequireRole";

// Protect admin routes
<RequireRole role="admin">
  <AdminContent />
</RequireRole>

// Protect user routes
<RequireRole role="user">
  <UserContent />
</RequireRole>
```

### Role-Based Rendering
```tsx
import { useSession } from "next-auth/react";

function Navbar() {
  const { data: session } = useSession();
  
  return (
    <nav>
      {session?.user.role === "admin" && (
        <Link href="/admin">Admin Panel</Link>
      )}
    </nav>
  );
}
```

## 🔧 Customization

### Adding New Roles
1. Update type definitions in `lib/auth.ts`
2. Modify database schema
3. Update `RequireRole` component
4. Add role-specific routes

### Custom User Fields
1. Update registration API in `pages/api/auth/register.ts`
2. Modify NextAuth callbacks in `lib/auth.ts`
3. Update Redux state in `redux/slices/authSlice.ts`

## 🐛 Troubleshooting

### Common Issues
1. **"Invalid credentials"** - Check MongoDB connection and user exists
2. **"Unauthorized"** - Verify user role and route protection
3. **Session not persisting** - Check NEXTAUTH_SECRET and cookies

### Debug Mode
Set `NODE_ENV=development` to see detailed logs in:
- NextAuth authentication
- MongoDB operations
- Redux state changes

## 📝 Testing

### Test Accounts
- **Admin**: <EMAIL> / admin123
- **User**: Create via registration form

### Manual Testing
1. Register new user → Should redirect to login
2. Login as user → Should access dashboard only
3. Login as admin → Should access both dashboard and admin panel
4. Logout → Should clear session and redirect to home

This clean implementation provides a secure, maintainable authentication system without the complexity of the previous setup.
