{"name": "pdf-tools", "version": "0.1.0", "private": true, "scripts": {"dev": "node scripts/download-fonts.js && next dev", "build": "node scripts/download-fonts.js && next build", "start": "next start", "lint": "next lint", "seed-admin": "node src/scripts/seed-admin.js", "download-fonts": "node scripts/download-fonts.js"}, "dependencies": {"@hookform/resolvers": "^3.3.2", "@radix-ui/react-avatar": "^1.1.9", "@radix-ui/react-checkbox": "^1.3.1", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-primitive": "^1.0.3", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.6", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.1.6", "@radix-ui/react-slider": "^1.3.4", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.2.14", "@reduxjs/toolkit": "^2.0.1", "@tanstack/react-table": "^8.11.2", "@tiptap/extension-bold": "^2.12.0", "@tiptap/extension-bullet-list": "^2.12.0", "@tiptap/extension-code-block": "^2.12.0", "@tiptap/extension-color": "^2.12.0", "@tiptap/extension-font-family": "^2.12.0", "@tiptap/extension-heading": "^2.12.0", "@tiptap/extension-highlight": "^2.12.0", "@tiptap/extension-horizontal-rule": "^2.12.0", "@tiptap/extension-image": "^2.12.0", "@tiptap/extension-italic": "^2.12.0", "@tiptap/extension-link": "^2.12.0", "@tiptap/extension-ordered-list": "^2.12.0", "@tiptap/extension-placeholder": "^2.12.0", "@tiptap/extension-strike": "^2.12.0", "@tiptap/extension-table": "^2.12.0", "@tiptap/extension-table-cell": "^2.12.0", "@tiptap/extension-table-header": "^2.12.0", "@tiptap/extension-table-row": "^2.12.0", "@tiptap/extension-task-item": "^2.12.0", "@tiptap/extension-task-list": "^2.12.0", "@tiptap/extension-text-align": "^2.12.0", "@tiptap/extension-text-style": "^2.12.0", "@tiptap/extension-underline": "^2.12.0", "@tiptap/react": "^2.12.0", "@tiptap/starter-kit": "^2.12.0", "@types/nprogress": "^0.2.3", "@types/uuid": "^10.0.0", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.0", "cloudinary": "^2.6.1", "clsx": "^2.0.0", "cmdk": "^1.1.1", "dotenv": "^16.5.0", "framer-motion": "^10.16.16", "jose": "^5.1.3", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.294.0", "mongodb": "^6.16.0", "mongoose": "^8.14.2", "next": "^15.3.2", "next-auth": "^4.24.5", "nprogress": "^0.2.0", "pdf-lib": "^1.17.1", "pdfjs-dist": "^5.2.133", "react": "^18.2.0", "react-colorful": "^5.6.1", "react-day-picker": "^9.6.7", "react-dom": "^18.2.0", "react-hook-form": "^7.49.2", "react-icons": "^4.12.0", "react-redux": "^9.0.4", "recharts": "^2.15.3", "tailwind-merge": "^2.1.0", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0", "zod": "^3.22.4"}, "devDependencies": {"@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/jsonwebtoken": "^9.0.5", "@types/mongoose": "^5.11.96", "@types/node": "^20.10.4", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.17", "autoprefixer": "^10.4.16", "eslint": "^8.55.0", "eslint-config-next": "14.0.4", "jest": "^29.7.0", "postcss": "^8.4.32", "tailwindcss": "^3.3.6", "typescript": "^5.3.3"}}