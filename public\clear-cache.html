<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Clear Browser Cache</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            margin-bottom: 20px;
        }
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            display: none;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .instructions {
            background: #e9ecef;
            padding: 15px;
            border-radius: 5px;
            margin-top: 20px;
        }
        .instructions h3 {
            margin-top: 0;
        }
        .instructions ol {
            margin: 10px 0;
            padding-left: 20px;
        }
        .instructions li {
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Fix Infinite Retry Loop Issue</h1>
        
        <div class="warning">
            <strong>Issue Detected:</strong> Your browser has cached malformed URLs with infinite retry parameters. 
            This is causing the application to fail loading properly.
        </div>

        <div class="success" id="success-message">
            <strong>Cache Cleared!</strong> The browser cache has been cleared. You can now return to the application.
        </div>

        <p>Click the button below to automatically clear your browser cache and fix the issue:</p>
        
        <button onclick="clearCache()">🗑️ Clear Browser Cache</button>
        <button onclick="goHome()">🏠 Return to Application</button>

        <div class="instructions">
            <h3>Manual Instructions (if automatic clearing doesn't work):</h3>
            <ol>
                <li><strong>Chrome/Edge:</strong> Press <code>Ctrl+Shift+Delete</code> (Windows) or <code>Cmd+Shift+Delete</code> (Mac)</li>
                <li><strong>Firefox:</strong> Press <code>Ctrl+Shift+Delete</code> (Windows) or <code>Cmd+Shift+Delete</code> (Mac)</li>
                <li><strong>Safari:</strong> Press <code>Cmd+Option+E</code> or go to Develop > Empty Caches</li>
                <li>Select "Cached images and files" or "Everything"</li>
                <li>Click "Clear data" or "Clear"</li>
                <li>Refresh the page or return to the application</li>
            </ol>
            
            <h3>Alternative: Use Incognito/Private Mode</h3>
            <p>Open the application in an incognito/private browsing window to bypass the cache entirely.</p>
        </div>
    </div>

    <script>
        async function clearCache() {
            try {
                // Clear service worker cache if available
                if ('serviceWorker' in navigator) {
                    const registrations = await navigator.serviceWorker.getRegistrations();
                    for (let registration of registrations) {
                        await registration.unregister();
                    }
                }

                // Clear cache storage if available
                if ('caches' in window) {
                    const cacheNames = await caches.keys();
                    await Promise.all(
                        cacheNames.map(cacheName => caches.delete(cacheName))
                    );
                }

                // Clear local storage
                localStorage.clear();
                sessionStorage.clear();

                // Show success message
                document.getElementById('success-message').style.display = 'block';
                
                // Reload the page to clear any remaining cache
                setTimeout(() => {
                    window.location.reload(true);
                }, 2000);

            } catch (error) {
                console.error('Error clearing cache:', error);
                alert('Automatic cache clearing failed. Please use the manual instructions below.');
            }
        }

        function goHome() {
            window.location.href = '/';
        }

        // Auto-clear cache on page load
        window.addEventListener('load', () => {
            // Give user a chance to read the message first
            setTimeout(() => {
                if (confirm('Would you like to automatically clear the browser cache now?')) {
                    clearCache();
                }
            }, 1000);
        });
    </script>
</body>
</html>
