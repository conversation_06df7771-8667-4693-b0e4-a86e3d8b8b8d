'use client';

import { useTheme } from '@/hooks/useTheme';
import { motion } from 'framer-motion';

export default function UnifiedLoading() {
  const { theme } = useTheme();

  const skeletonClass = `animate-pulse ${
    theme === 'dark' ? 'bg-gray-800' : 'bg-gray-200'
  }`;

  return (
    <div className={`min-h-screen ${
      theme === 'dark' 
        ? 'bg-gradient-to-br from-gray-900 to-slate-900 text-white' 
        : 'bg-gradient-to-br from-gray-50 to-slate-50 text-gray-900'
    }`}>
      {/* Header Skeleton */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        className="relative overflow-hidden"
      >
        <div className="container mx-auto px-4 py-12 sm:py-16">
          <div className="max-w-4xl mx-auto text-center">
            {/* Icon Skeleton */}
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ delay: 0.1, type: "spring", stiffness: 200 }}
              className={`inline-flex items-center justify-center w-16 h-16 rounded-2xl ${skeletonClass} mb-6`}
            />

            {/* Title Skeleton */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2, duration: 0.5 }}
              className={`h-12 rounded-lg ${skeletonClass} mb-4 mx-auto max-w-md`}
            />

            {/* Description Skeleton */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3, duration: 0.5 }}
              className="space-y-3 mb-6"
            >
              <div className={`h-6 rounded ${skeletonClass} mx-auto max-w-2xl`} />
              <div className={`h-6 rounded ${skeletonClass} mx-auto max-w-xl`} />
            </motion.div>

            {/* Badges Skeleton */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4, duration: 0.5 }}
              className="flex flex-wrap justify-center gap-3 mb-8"
            >
              {[...Array(3)].map((_, i) => (
                <div
                  key={i}
                  className={`h-8 w-20 rounded-full ${skeletonClass}`}
                />
              ))}
            </motion.div>
          </div>
        </div>

        {/* Decorative background elements */}
        <div className="absolute inset-0 -z-10 overflow-hidden">
          <div className={`absolute -top-40 -right-40 w-80 h-80 rounded-full opacity-20 ${
            theme === 'dark' ? 'bg-white' : 'bg-gray-900'
          }`} />
          <div className={`absolute -bottom-40 -left-40 w-80 h-80 rounded-full opacity-10 ${
            theme === 'dark' ? 'bg-white' : 'bg-gray-900'
          }`} />
        </div>
      </motion.div>

      {/* Content Skeleton */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.5, duration: 0.5 }}
        className="container mx-auto px-4 pb-12"
      >
        <div className="max-w-6xl mx-auto">
          {/* Main content area skeleton */}
          <div className="space-y-6">
            {/* Large content block */}
            <div className={`h-64 rounded-lg ${skeletonClass}`} />
            
            {/* Grid layout for tools/calculators */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className={`h-32 rounded-lg ${skeletonClass}`} />
              <div className={`h-32 rounded-lg ${skeletonClass}`} />
            </div>
            
            {/* Text content skeleton */}
            <div className="space-y-4">
              {[...Array(6)].map((_, i) => (
                <div
                  key={i}
                  className={`h-4 rounded ${skeletonClass} ${
                    i % 3 === 0 ? 'w-full' : i % 3 === 1 ? 'w-3/4' : 'w-5/6'
                  }`}
                />
              ))}
            </div>
          </div>
        </div>
      </motion.div>

      {/* Loading indicator */}
      <div className="fixed top-4 right-4 z-50">
        <motion.div
          animate={{ rotate: 360 }}
          transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
          className={`w-8 h-8 border-2 border-t-transparent rounded-full ${
            theme === 'dark' ? 'border-white' : 'border-gray-900'
          }`}
        />
      </div>
    </div>
  );
}
