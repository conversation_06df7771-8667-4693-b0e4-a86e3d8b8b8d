import { notFound, redirect } from "next/navigation";
import { Metadata } from "next";
import {
  ContentType,
  getPageData,
  getDynamicMetadata,
  generateUnifiedStaticParams,
  getRedirectSlug,
  isValidRoute
} from "@/lib/routing";
import DynamicLayout from "@/components/layout/DynamicLayout";
import DynamicContent from "@/components/layout/DynamicContent";

interface UnifiedPageProps {
  params: Promise<{
    type: string;
    slug: string;
  }>;
}

// Generate static paths for all content types
export async function generateStaticParams() {
  try {
    return generateUnifiedStaticParams();
  } catch (error) {
    console.error('Error generating static params:', error);
    return [];
  }
}

// Static generation with ISR for better performance
export const dynamic = 'force-static';
export const revalidate = 3600; // Revalidate every hour

// Generate metadata for SEO
export async function generateMetadata({ params }: UnifiedPageProps): Promise<Metadata> {
  const { type, slug } = await params;

  // Validate content type
  if (!['calculators', 'tools', 'blogs'].includes(type)) {
    return {
      title: "Page Not Found - ToolCrush",
      description: "The requested page could not be found.",
      robots: { index: false, follow: false }
    };
  }

  const contentType = type as ContentType;

  // Check for slug redirects
  const redirectSlug = getRedirectSlug(slug);
  if (redirectSlug) {
    // Return metadata for the redirect target
    return getDynamicMetadata(contentType, redirectSlug);
  }

  // Validate route
  if (!isValidRoute(contentType, slug)) {
    return {
      title: "Page Not Found - ToolCrush",
      description: "The requested page could not be found.",
      robots: { index: false, follow: false }
    };
  }

  // Generate dynamic metadata
  return getDynamicMetadata(contentType, slug);
}

export default async function UnifiedDynamicPage({ params }: UnifiedPageProps) {
  const { type, slug } = await params;

  // Validate content type
  if (!['calculators', 'tools', 'blogs'].includes(type)) {
    notFound();
  }

  const contentType = type as ContentType;

  // Handle slug redirects
  const redirectSlug = getRedirectSlug(slug);
  if (redirectSlug) {
    redirect(`/${type}/${redirectSlug}`);
  }

  // Validate route and get data
  const data = getPageData(contentType, slug);
  if (!data) {
    notFound();
  }

  return (
    <DynamicLayout type={contentType} data={data}>
      <DynamicContent type={contentType} data={data} slug={slug} />
    </DynamicLayout>
  );
}
