"use client";

import { useParams } from "next/navigation";
import DashboardLayout from "@/components/admin/DashboardLayout";
import { Card, CardContent } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Eye, MessageSquare } from "lucide-react";
import { useEffect, useState } from "react";

export default function BlogPostDetailPage() {
  const params = useParams();
  const postId = params.id as string;
  const [post, setPost] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Fetch post data
    // This is a placeholder - replace with actual API call
    setTimeout(() => {
      setPost({
        id: postId,
        title: "Sample Blog Post",
        viewCount: 1234,
        commentCount: 56,
        // Other post data
      });
      setLoading(false);
    }, 500);
  }, [postId]);

  if (loading) {
    return (
      <DashboardLayout>
        <div className="space-y-4">
          <div>
            <h1 className="text-2xl font-bold">Loading...</h1>
            <p className="text-muted-foreground">Fetching post details</p>
          </div>
          <div className="flex justify-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="space-y-4">
        <div>
          <h1 className="text-2xl font-bold">{post.title}</h1>
          <p className="text-muted-foreground">View and manage post details</p>
        </div>
        <Tabs defaultValue="overview">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
          <TabsTrigger value="comments">Comments</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardContent className="pt-6 text-center">
                <Eye className="h-8 w-8 mx-auto mb-2 text-[rgb(var(--primary))]" />
                <p className="text-2xl font-bold text-adaptive">{post.viewCount}</p>
                <p className="text-sm text-adaptive-muted">Views</p>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="pt-6 text-center">
                <MessageSquare className="h-8 w-8 mx-auto mb-2 text-[rgb(var(--primary))]" />
                <p className="text-2xl font-bold text-adaptive">{post.commentCount}</p>
                <p className="text-sm text-adaptive-muted">Comments</p>
              </CardContent>
            </Card>
            {/* Additional cards */}
          </div>
        </TabsContent>

        {/* Other tab contents */}
        </Tabs>
      </div>
    </DashboardLayout>
  );
}

