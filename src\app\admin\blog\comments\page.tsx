"use client";

import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { useRouter } from "next/navigation";
import { BlogAdminLayout } from "@/components/admin/BlogAdminLayout";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { <PERSON><PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  MoreHorizontal,
  Search,
  CheckCircle,
  XCircle,
  MessageSquare,
  ChevronDown,
  Eye,
  Reply,
  Trash2,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>
} from "lucide-react";
import Link from "next/link";
import { toast } from "@/hooks/use-toast";
import { formatDistanceToNow } from "date-fns";

interface Comment {
  _id: string;
  content: string;
  authorName: string;
  authorEmail: string;
  authorWebsite?: string;
  status: "pending" | "approved" | "spam" | "trash";
  isAdmin: boolean;
  createdAt: string;
  post: {
    _id: string;
    title: string;
    slug: string;
  };
}

export default function CommentsPage() {
  const router = useRouter();
  const [comments, setComments] = useState<Comment[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("approved");
  const [processingId, setProcessingId] = useState<string | null>(null);

  // Fetch comments on component mount
  useEffect(() => {
    fetchComments();
  }, []);

  const fetchComments = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/blog/comments');
      
      if (!response.ok) {
        throw new Error(`Failed to fetch comments: ${response.status}`);
      }
      
      const data = await response.json();
      setComments(data.comments || []);
    } catch (error) {
      console.error('Error fetching comments:', error);
      toast({
        title: 'Error',
        description: 'Failed to load comments. Please try again later.',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  // Filter comments based on search term and status
  const filteredComments = comments.filter(comment => {
    const matchesSearch = 
      comment.content.toLowerCase().includes(searchTerm.toLowerCase()) ||
      comment.authorName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      comment.authorEmail.toLowerCase().includes(searchTerm.toLowerCase()) ||
      comment.post.title.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = statusFilter === "all" || comment.status === statusFilter;
    
    return matchesSearch && matchesStatus;
  });

  // Handle comment status change
  const handleStatusChange = async (commentId: string, newStatus: string) => {
    try {
      setProcessingId(commentId);
      
      const response = await fetch(`/api/blog/comments/${commentId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status: newStatus }),
      });
      
      if (!response.ok) {
        throw new Error(`Failed to update comment: ${response.status}`);
      }
      
      // Update local state
      setComments(prevComments => 
        prevComments.map(comment => 
          comment._id === commentId 
            ? { ...comment, status: newStatus as "pending" | "approved" | "spam" | "trash" } 
            : comment
        )
      );
      
      toast({
        title: 'Success',
        description: `Comment ${newStatus === 'approved' ? 'approved' : newStatus === 'spam' ? 'marked as spam' : 'updated'}.`,
      });
    } catch (error) {
      console.error('Error updating comment:', error);
      toast({
        title: 'Error',
        description: 'Failed to update comment. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setProcessingId(null);
    }
  };

  // Handle comment deletion
  const handleDelete = async (commentId: string) => {
    if (!confirm('Are you sure you want to delete this comment? This action cannot be undone.')) {
      return;
    }
    
    try {
      setProcessingId(commentId);
      
      const response = await fetch(`/api/blog/comments/${commentId}`, {
        method: 'DELETE',
      });
      
      if (!response.ok) {
        throw new Error(`Failed to delete comment: ${response.status}`);
      }
      
      // Remove from local state
      setComments(prevComments => prevComments.filter(comment => comment._id !== commentId));
      
      toast({
        title: 'Success',
        description: 'Comment deleted successfully.',
      });
    } catch (error) {
      console.error('Error deleting comment:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete comment. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setProcessingId(null);
    }
  };

  // Helper function to get status badge
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'approved':
        return <Badge className="bg-green-500">Approved</Badge>;
      case 'pending':
        return <Badge className="bg-yellow-500">Pending</Badge>;
      case 'spam':
        return <Badge className="bg-red-500">Spam</Badge>;
      default:
        return <Badge className="bg-gray-500">Unknown</Badge>;
    }
  };

  return (
    <BlogAdminLayout title="Comments" subtitle="Manage comments on your blog posts">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
      >
        <Card>
          <CardHeader>
            <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
              <div>
                <CardTitle>All Comments</CardTitle>
                <CardDescription>
                  {filteredComments.length} comment{filteredComments.length !== 1 ? 's' : ''}
                </CardDescription>
              </div>
              <div className="flex items-center gap-2">
                <div className="relative">
                  <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    type="search"
                    placeholder="Search comments..."
                    className="pl-8 w-[200px]"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>
                <Button onClick={fetchComments}>Refresh</Button>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="approved" onValueChange={setStatusFilter}>
              <TabsList className="mb-4">
                <TabsTrigger value="approved">Approved</TabsTrigger>
                <TabsTrigger value="pending">Pending</TabsTrigger>
                <TabsTrigger value="spam">Spam</TabsTrigger>
                <TabsTrigger value="all">All</TabsTrigger>
              </TabsList>

              {loading ? (
                <div className="flex justify-center py-8">
                  <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
                </div>
              ) : filteredComments.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  <MessageSquare className="h-12 w-12 mx-auto mb-2 opacity-20" />
                  <p>No comments found</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {filteredComments.map((comment) => (
                    <CommentCard 
                      key={comment._id} 
                      comment={comment} 
                      onStatusChange={handleStatusChange}
                      onDelete={handleDelete}
                      isProcessing={processingId === comment._id}
                    />
                  ))}
                </div>
              )}
            </Tabs>
          </CardContent>
        </Card>
      </motion.div>
    </BlogAdminLayout>
  );
}

function CommentCard({ 
  comment, 
  onStatusChange, 
  onDelete,
  isProcessing 
}: { 
  comment: Comment; 
  onStatusChange: (id: string, status: string) => void;
  onDelete: (id: string) => void;
  isProcessing: boolean;
}) {
  return (
    <Card className="overflow-hidden">
      <div className="flex items-start p-4 gap-4">
        <Avatar className="h-10 w-10">
          <AvatarImage src={`https://ui-avatars.com/api/?name=${encodeURIComponent(comment.authorName)}&background=random`} alt={comment.authorName} />
          <AvatarFallback>{comment.authorName.charAt(0)}</AvatarFallback>
        </Avatar>
        <div className="flex-1 min-w-0">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
            <div>
              <h3 className="font-medium">{comment.authorName}</h3>
              <p className="text-xs text-muted-foreground">{comment.authorEmail}</p>
            </div>
            <div className="flex items-center gap-2">
              <Badge className={`${
                comment.status === 'approved' ? 'bg-green-500' : 
                comment.status === 'pending' ? 'bg-yellow-500' : 
                comment.status === 'spam' ? 'bg-red-500' : 'bg-gray-500'
              }`}>
                {comment.status.charAt(0).toUpperCase() + comment.status.slice(1)}
              </Badge>
              <span className="text-xs text-muted-foreground">
                {formatDistanceToNow(new Date(comment.createdAt), { addSuffix: true })}
              </span>
            </div>
          </div>
          <p className="mt-2">
            {comment.content}
          </p>
          <div className="mt-2 flex items-center gap-2 text-xs text-muted-foreground">
            <span>On post:</span>
            <Link href={`/blog/${comment.post.slug}`} className="text-primary hover:underline">
              {comment.post.title}
            </Link>
          </div>
        </div>
        <div>
          {isProcessing ? (
            <Loader2 className="h-4 w-4 animate-spin" />
          ) : (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="icon">
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuLabel>Actions</DropdownMenuLabel>
                {comment.status !== "approved" && (
                  <DropdownMenuItem onClick={() => onStatusChange(comment._id, "approved")}>
                    <CheckCircle className="mr-2 h-4 w-4 text-green-500" />
                    Approve
                  </DropdownMenuItem>
                )}
                {comment.status !== "pending" && (
                  <DropdownMenuItem onClick={() => onStatusChange(comment._id, "pending")}>
                    <AlertTriangle className="mr-2 h-4 w-4 text-yellow-500" />
                    Mark as Pending
                  </DropdownMenuItem>
                )}
                {comment.status !== "spam" && (
                  <DropdownMenuItem onClick={() => onStatusChange(comment._id, "spam")}>
                    <XCircle className="mr-2 h-4 w-4 text-red-500" />
                    Mark as Spam
                  </DropdownMenuItem>
                )}
                <DropdownMenuItem onClick={() => onDelete(comment._id)}>
                  <Trash2 className="mr-2 h-4 w-4 text-red-500" />
                  Delete
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          )}
        </div>
      </div>
    </Card>
  );
}

