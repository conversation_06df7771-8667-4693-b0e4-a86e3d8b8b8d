"use client";

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { toast } from "@/hooks/use-toast";
import { useRouter, useParams } from "next/navigation";
import { RequireRole } from "@/components/auth/RequireRole";
import { motion } from "framer-motion";

// Mock blog post data for demonstration
const mockPosts = [
  {
    _id: "1",
    title: "Top 10 PDF Tools for Professionals",
    slug: "top-10-pdf-tools-professionals",
    content: "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.",
    excerpt: "Discover the best PDF tools that every professional should have in their toolkit.",
    status: "published",
    publishedAt: "2023-10-15",
    scheduledAt: null,
    authorId: "admin1",
    authorName: "William Mason",
    authorRole: "admin",
    image: "https://images.unsplash.com/photo-1606857521015-7f9fcf423740?w=800&q=80",
  },
  {
    _id: "2",
    title: "How to Compress PDF Files Without Losing Quality",
    slug: "compress-pdf-without-losing-quality",
    content: "Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.",
    excerpt: "Learn how to reduce PDF file size while maintaining document quality.",
    status: "published",
    publishedAt: "2023-09-28",
    scheduledAt: null,
    authorId: "admin1",
    authorName: "William Mason",
    authorRole: "admin",
    image: "https://images.unsplash.com/photo-1586281380349-632531db7ed4?w=800&q=80",
  },
  {
    _id: "3",
    title: "The Ultimate Guide to PDF to Word Conversion",
    slug: "ultimate-guide-pdf-to-word",
    content: "Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.",
    excerpt: "Everything you need to know about converting PDF documents to editable Word files.",
    status: "draft",
    publishedAt: null,
    scheduledAt: null,
    authorId: "user1",
    authorName: "Regular User",
    authorRole: "user",
    image: "https://images.unsplash.com/photo-1568667256549-094345857637?w=800&q=80",
  },
  {
    _id: "4",
    title: "5 Ways to Secure Your PDF Documents",
    slug: "5-ways-secure-pdf-documents",
    content: "Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.",
    excerpt: "Protect your sensitive PDF documents with these security measures.",
    status: "scheduled",
    publishedAt: null,
    scheduledAt: "2023-11-05",
    authorId: "admin1",
    authorName: "William Mason",
    authorRole: "admin",
    image: "https://images.unsplash.com/photo-1563013544-824ae1b704d3?w=800&q=80",
  },
];

// Mock current user
const mockUsers = {
  admin: {
    id: "admin1",
    name: "William Mason",
    email: "<EMAIL>",
    role: "admin",
  },
  user: {
    id: "user1",
    name: "Regular User",
    email: "<EMAIL>",
    role: "user",
  }
};

export default function EditBlogPostPage() {
  const router = useRouter();
  const params = useParams();
  const postId = params.id as string;

  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formData, setFormData] = useState({
    title: "",
    content: "",
    excerpt: "",
    slug: "",
    status: "draft",
    tags: "",
    categories: ""
  });

  useEffect(() => {
    const fetchPost = async () => {
      setIsLoading(true);
      try {
        // In a real app, fetch from API
        // const response = await fetch(`/api/blog/${postId}`);
        // const data = await response.json();

        // For now, use mock data
        const post = mockPosts.find(p => p._id === postId);

        if (post) {
          setFormData({
            title: post.title,
            content: post.content,
            excerpt: post.excerpt,
            slug: post.slug,
            status: post.status,
            tags: post.tags || "",
            categories: post.categories || ""
          });
        } else {
          toast({
            title: "Error",
            description: "Post not found",
            variant: "destructive",
          });
          router.push("/admin/blog/posts");
        }
      } catch (error) {
        toast({
          title: "Error",
          description: "Failed to load post data",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchPost();
  }, [postId, router]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;

    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSelectChange = (name: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.title) {
      toast({
        title: "Error",
        description: "Please enter a title for your post.",
        variant: "destructive",
      });
      return;
    }

    setIsSubmitting(true);

    try {
      // In a real app, this would send data to the API
      const response = await fetch(`/api/blog/${postId}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(formData),
      });

      if (!response.ok) {
        throw new Error("Failed to update blog post");
      }

      toast({
        title: "Success",
        description: "Blog post updated successfully.",
      });

      // Redirect to blog management page
      router.push("/admin/blog/posts");
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update blog post. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoading) {
    return (
      <RequireRole role="admin">
        <div className="container py-6 space-y-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold tracking-tight">Edit Blog Post</h1>
              <p className="text-muted-foreground">Loading post data...</p>
            </div>
          </div>
          <div className="w-full h-96 flex items-center justify-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
          </div>
        </div>
      </RequireRole>
    );
  }

  return (
    <RequireRole role="admin">
      <div className="container py-6 space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold tracking-tight">Edit Blog Post</h1>
            <p className="text-muted-foreground">Update your blog post content and settings</p>
          </div>
          <Button
            variant="outline"
            onClick={() => router.push("/admin/blog/posts")}
          >
            Cancel
          </Button>
        </div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          <form onSubmit={handleSubmit} className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Post Details</CardTitle>
                <CardDescription>Update information about your blog post</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="title">Title</Label>
                  <Input
                    id="title"
                    name="title"
                    value={formData.title}
                    onChange={handleChange}
                    placeholder="Enter post title"
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="slug">Slug</Label>
                  <Input
                    id="slug"
                    name="slug"
                    value={formData.slug}
                    onChange={handleChange}
                    placeholder="post-url-slug"
                  />
                  <p className="text-xs text-muted-foreground">
                    This will be used in the URL of your post
                  </p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="excerpt">Excerpt</Label>
                  <Textarea
                    id="excerpt"
                    name="excerpt"
                    value={formData.excerpt}
                    onChange={handleChange}
                    placeholder="Brief description of your post"
                    rows={3}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="content">Content</Label>
                  <Textarea
                    id="content"
                    name="content"
                    value={formData.content}
                    onChange={handleChange}
                    placeholder="Write your post content here..."
                    rows={10}
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="status">Status</Label>
                    <Select
                      value={formData.status}
                      onValueChange={(value) => handleSelectChange("status", value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select status" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="draft">Draft</SelectItem>
                        <SelectItem value="published">Published</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="categories">Categories</Label>
                    <Input
                      id="categories"
                      name="categories"
                      value={formData.categories}
                      onChange={handleChange}
                      placeholder="Comma-separated categories"
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="tags">Tags</Label>
                  <Input
                    id="tags"
                    name="tags"
                    value={formData.tags}
                    onChange={handleChange}
                    placeholder="Comma-separated tags"
                  />
                </div>
              </CardContent>
            </Card>

            <div className="flex justify-end gap-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => router.push("/admin/blog/posts")}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={isSubmitting}
              >
                {isSubmitting ? "Saving..." : "Save Changes"}
              </Button>
            </div>
          </form>
        </motion.div>
      </div>
    </RequireRole>
  );
}
