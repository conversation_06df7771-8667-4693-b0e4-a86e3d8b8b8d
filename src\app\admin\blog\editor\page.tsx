"use client";

import { useEffect } from "react";
import { BlogPostEditor } from "@/components/blog/editor/BlogPostEditor";
import { useSearchParams } from "next/navigation";
import { RequireRole } from "@/components/auth/RequireRole";

export default function BlogEditorPage() {
  const searchParams = useSearchParams();
  const postId = searchParams.get("id");

  // Set page title based on whether we're editing or creating
  useEffect(() => {
    document.title = postId ? "Edit Blog Post" : "Create New Blog Post";
  }, [postId]);

  return (
    <RequireRole role="admin">
      <div className="w-full mx-auto">
        <BlogPostEditor postId={postId} />
      </div>
    </RequireRole>
  );
}

