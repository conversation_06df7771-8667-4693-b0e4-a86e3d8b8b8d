"use client";

import { useEffect } from "react";
import { useRouter } from "next/navigation";

export default function NewBlogPostPage() {
  const router = useRouter();

  // Redirect to the new rich editor page
  useEffect(() => {
    router.replace("/admin/blog/editor");
  }, [router]);

  // Show loading state while redirecting
  return (
    <div className="flex items-center justify-center min-h-screen">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
        <p className="text-lg">Redirecting to rich editor...</p>
      </div>
    </div>
  );
}