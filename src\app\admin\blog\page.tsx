"use client";

import { useEffect } from "react";
import { useRouter } from "next/navigation";

export default function BlogRedirectPage() {
  const router = useRouter();

  // Redirect to the unified blog posts management page
  useEffect(() => {
    router.replace("/admin/blog/posts");
  }, [router]);

  // Show loading state while redirecting
  return (
    <div className="flex items-center justify-center min-h-screen">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
        <p className="text-lg">Redirecting to blog management...</p>
      </div>
    </div>
  );
}