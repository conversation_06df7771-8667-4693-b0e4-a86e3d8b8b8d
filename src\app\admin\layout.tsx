"use client";

import { usePathname } from "next/navigation";
import { ProtectedRoute } from "@/components/auth/ProtectedRoute";
import { UserRole } from "@/lib/auth/roles";
import AdminLayout from "@/components/admin/AdminLayout";

interface AdminLayoutProps {
  children: React.ReactNode;
}

export default function AdminRootLayout({ children }: AdminLayoutProps) {
  const pathname = usePathname();

  // Determine the title based on the current path
  const getTitle = () => {
    if (pathname.includes("/admin/blog")) return "Blog Management";
    if (pathname.includes("/admin/users")) return "User Management";
    if (pathname.includes("/admin/tools")) return "Tools";
    if (pathname.includes("/admin/settings")) return "Settings";
    return "Admin Panel";
  };

  return (
    <ProtectedRoute requiredRole={UserRole.ADMIN}>
      <AdminLayout>
        {children}
      </AdminLayout>
    </ProtectedRoute>
  );
}

