// import React from "react";
// import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
// import { Button } from "@/components/ui/Button";
// import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@radix-ui/react-tabs";
// import Sidebar from "@components/newAdmin/Sidebar";
// import AnalyticsCard from "@components/newAdmin/AnalyticsCard";
// import PopularToolsChart from "@components/newAdmin/PopularToolsChart";
// import { ArrowUpRight, Users, FileText, Clock, Activity } from "lucide-react";

// interface HomeProps {
//   isAdmin?: boolean;
//   userName?: string;
// }

// const Home = ({ isAdmin = true, userName = "Admin User" }: HomeProps) => {
//   // Mock data for analytics
//   const analyticsData = {
//     visitors: {
//       title: "Total Visitors",
//       value: "24.3K",
//       change: "+12%",
//       icon: <Activity className="h-4 w-4" />,
//     },
//     timeSpent: {
//       title: "Avg. Time Spent",
//       value: "4m 38s",
//       change: "+7%",
//       icon: <Clock className="h-4 w-4" />,
//     },
//     users: {
//       title: "Total Users",
//       value: "1,429",
//       change: "+18%",
//       icon: <Users className="h-4 w-4" />,
//     },
//     posts: {
//       title: "Blog Posts",
//       value: "48",
//       change: "+4%",
//       icon: <FileText className="h-4 w-4" />,
//     },
//   };

//   // If not admin, show access denied
//   if (!isAdmin) {
//     return (
//       <div className="flex h-screen items-center justify-center bg-background">
//         <Card className="w-[400px]">
//           <CardHeader>
//             <CardTitle className="text-center text-destructive">
//               Access Denied
//             </CardTitle>
//           </CardHeader>
//           <CardContent>
//             <p className="text-center text-muted-foreground">
//               You don't have permission to access the admin dashboard.
//             </p>
//             <div className="mt-6 flex justify-center">
//               <Button>Return to Home</Button>
//             </div>
//           </CardContent>
//         </Card>
//       </div>
//     );
//   }

//   return (
//     <div className="flex h-screen bg-background">
//       <Sidebar />
//       <div className="flex-1 overflow-auto p-8">
//         <div className="flex items-center justify-between">
//           <div>
//             <h1 className="text-3xl font-bold tracking-tight">Dashboard</h1>
//             <p className="text-muted-foreground">Welcome back, {userName}</p>
//           </div>
//           <div className="flex items-center gap-2">
//             <Button variant="outline" size="sm">
//               <ArrowUpRight className="mr-2 h-4 w-4" />
//               Export
//             </Button>
//             <Button size="sm">Refresh Data</Button>
//           </div>
//         </div>

//         <Tabs defaultValue="overview" className="mt-6">
//           <TabsList>
//             <TabsTrigger value="overview">Overview</TabsTrigger>
//             <TabsTrigger value="analytics">Analytics</TabsTrigger>
//             <TabsTrigger value="reports">Reports</TabsTrigger>
//           </TabsList>
//           <TabsContent value="overview" className="space-y-6">
//             <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
//               <AnalyticsCard
//                 title={analyticsData.visitors.title}
//                 value={analyticsData.visitors.value}
//                 change={analyticsData.visitors.change}
//                 icon={analyticsData.visitors.icon}
//               />
//               <AnalyticsCard
//                 title={analyticsData.timeSpent.title}
//                 value={analyticsData.timeSpent.value}
//                 change={analyticsData.timeSpent.change}
//                 icon={analyticsData.timeSpent.icon}
//               />
//               <AnalyticsCard
//                 title={analyticsData.users.title}
//                 value={analyticsData.users.value}
//                 // percentageChange={18}
//                 description="from last month"
//                 icon={analyticsData.users.icon}
//               />
//               <AnalyticsCard
//                 title={analyticsData.posts.title}
//                 value={analyticsData.posts.value}
//                 // percentageChange={4}
//                 description="from last month"
//                 icon={analyticsData.posts.icon}
//               />
//             </div>

//             <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
//               <Card className="col-span-4">
//                 <CardHeader>
//                   <CardTitle>Popular Tools</CardTitle>
//                 </CardHeader>
//                 <CardContent>
//                   {/* <PopularToolsChart /> */}
//                 </CardContent>
//               </Card>
//               <Card className="col-span-3">
//                 <CardHeader>
//                   <CardTitle>Recent Activity</CardTitle>
//                 </CardHeader>
//                 <CardContent>
//                   <div className="space-y-4">
//                     {[1, 2, 3, 4, 5].map((i) => (
//                       <div
//                         key={i}
//                         className="flex items-center gap-4 rounded-lg border p-3"
//                       >
//                         <div className="h-9 w-9 rounded-full bg-primary/10 flex items-center justify-center">
//                           <Users className="h-5 w-5 text-primary" />
//                         </div>
//                         <div className="flex-1">
//                           <p className="text-sm font-medium">
//                             New user registered
//                           </p>
//                           <p className="text-xs text-muted-foreground">
//                             {i} hour{i !== 1 ? "s" : ""} ago
//                           </p>
//                         </div>
//                       </div>
//                     ))}
//                   </div>
//                 </CardContent>
//               </Card>
//             </div>
//           </TabsContent>
//           <TabsContent value="analytics">
//             <Card>
//               <CardHeader>
//                 <CardTitle>Detailed Analytics</CardTitle>
//               </CardHeader>
//               <CardContent>
//                 <p className="text-muted-foreground">
//                   Detailed analytics content will appear here.
//                 </p>
//               </CardContent>
//             </Card>
//           </TabsContent>
//           <TabsContent value="reports">
//             <Card>
//               <CardHeader>
//                 <CardTitle>Generated Reports</CardTitle>
//               </CardHeader>
//               <CardContent>
//                 <p className="text-muted-foreground">
//                   Reports content will appear here.
//                 </p>
//               </CardContent>
//             </Card>
//           </TabsContent>
//         </Tabs>
//       </div>
//     </div>
//   );
// };

// export default Home;
