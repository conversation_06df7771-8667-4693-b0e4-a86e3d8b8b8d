"use client";

import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Separator } from "@/components/ui/separator";
import { Save, RotateCcw, Palette, Type, Layout, Eye } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

interface ThemeSettings {
  primaryColor: string;
  secondaryColor: string;
  accentColor: string;
  backgroundColor: string;
  textColor: string;
  fontFamily: string;
  fontSize: string;
  borderRadius: string;
  darkMode: boolean;
  customCss: string;
}

const defaultTheme: ThemeSettings = {
  primaryColor: "#3b82f6",
  secondaryColor: "#10b981",
  accentColor: "#8b5cf6",
  backgroundColor: "#ffffff",
  textColor: "#1f2937",
  fontFamily: "Inter, sans-serif",
  fontSize: "16px",
  borderRadius: "8px",
  darkMode: false,
  customCss: ""
};

const colorPresets = [
  { name: "Blue", primary: "#3b82f6", secondary: "#10b981", accent: "#8b5cf6" },
  { name: "Purple", primary: "#8b5cf6", secondary: "#ec4899", accent: "#f59e0b" },
  { name: "Green", primary: "#10b981", secondary: "#3b82f6", accent: "#f59e0b" },
  { name: "Orange", primary: "#f59e0b", secondary: "#ef4444", accent: "#8b5cf6" },
  { name: "Red", primary: "#ef4444", secondary: "#f59e0b", accent: "#10b981" },
  { name: "Teal", primary: "#14b8a6", secondary: "#3b82f6", accent: "#f59e0b" }
];

const fontOptions = [
  "Inter, sans-serif",
  "Roboto, sans-serif",
  "Open Sans, sans-serif",
  "Lato, sans-serif",
  "Montserrat, sans-serif",
  "Poppins, sans-serif",
  "Source Sans Pro, sans-serif",
  "Nunito, sans-serif"
];

export default function ThemePage() {
  const [theme, setTheme] = useState<ThemeSettings>(defaultTheme);
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const { toast } = useToast();

  // Load theme on component mount
  useEffect(() => {
    loadTheme();
  }, []);

  const loadTheme = async () => {
    setIsLoading(true);
    try {
      const response = await fetch("/api/theme");
      if (response.ok) {
        const data = await response.json();
        setTheme({ ...defaultTheme, ...data });
      }
    } catch (error) {
      console.error("Failed to load theme:", error);
      toast({
        title: "Error",
        description: "Failed to load theme settings",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const saveTheme = async () => {
    setIsSaving(true);
    try {
      const response = await fetch("/api/theme", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(theme),
      });

      if (response.ok) {
        toast({
          title: "Success",
          description: "Theme settings saved successfully",
        });
      } else {
        throw new Error("Failed to save theme");
      }
    } catch (error) {
      console.error("Failed to save theme:", error);
      toast({
        title: "Error",
        description: "Failed to save theme settings",
        variant: "destructive",
      });
    } finally {
      setIsSaving(false);
    }
  };

  const resetTheme = () => {
    setTheme(defaultTheme);
    toast({
      title: "Theme Reset",
      description: "Theme has been reset to defaults",
    });
  };

  const handleInputChange = (field: keyof ThemeSettings, value: string | boolean) => {
    setTheme(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const applyColorPreset = (preset: typeof colorPresets[0]) => {
    setTheme(prev => ({
      ...prev,
      primaryColor: preset.primary,
      secondaryColor: preset.secondary,
      accentColor: preset.accent
    }));
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 max-w-4xl">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="flex items-center justify-between mb-8"
      >
        <div>
          <h1 className="text-3xl font-bold text-foreground flex items-center gap-2">
            <Palette className="h-8 w-8" />
            Theme Settings
          </h1>
          <p className="text-muted-foreground mt-2">
            Customize your website's appearance and styling
          </p>
        </div>

        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={resetTheme}
            disabled={isSaving}
          >
            <RotateCcw className="h-4 w-4 mr-2" />
            Reset
          </Button>
          <Button
            onClick={saveTheme}
            disabled={isSaving}
          >
            {isSaving ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Saving...
              </>
            ) : (
              <>
                <Save className="h-4 w-4 mr-2" />
                Save Changes
              </>
            )}
          </Button>
        </div>
      </motion.div>

      {/* Theme Preview */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.1 }}
        className="mb-8"
      >
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Eye className="h-5 w-5" />
              Theme Preview
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div 
              className="border rounded-lg p-6"
              style={{
                backgroundColor: theme.backgroundColor,
                color: theme.textColor,
                fontFamily: theme.fontFamily,
                fontSize: theme.fontSize,
                borderRadius: theme.borderRadius
              }}
            >
              <h3 
                className="text-xl font-bold mb-2"
                style={{ color: theme.primaryColor }}
              >
                Sample Heading
              </h3>
              <p className="mb-4">
                This is how your content will look with the current theme settings.
              </p>
              <div className="flex gap-2">
                <button
                  className="px-4 py-2 rounded text-white"
                  style={{ 
                    backgroundColor: theme.primaryColor,
                    borderRadius: theme.borderRadius
                  }}
                >
                  Primary Button
                </button>
                <button
                  className="px-4 py-2 rounded text-white"
                  style={{ 
                    backgroundColor: theme.secondaryColor,
                    borderRadius: theme.borderRadius
                  }}
                >
                  Secondary Button
                </button>
                <button
                  className="px-4 py-2 rounded text-white"
                  style={{ 
                    backgroundColor: theme.accentColor,
                    borderRadius: theme.borderRadius
                  }}
                >
                  Accent Button
                </button>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Theme Settings Tabs */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.2 }}
      >
        <Tabs defaultValue="colors" className="space-y-6">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="colors" className="flex items-center gap-2">
              <Palette className="h-4 w-4" />
              Colors
            </TabsTrigger>
            <TabsTrigger value="typography" className="flex items-center gap-2">
              <Type className="h-4 w-4" />
              Typography
            </TabsTrigger>
            <TabsTrigger value="layout" className="flex items-center gap-2">
              <Layout className="h-4 w-4" />
              Layout
            </TabsTrigger>
          </TabsList>

          {/* Colors Tab */}
          <TabsContent value="colors" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Color Presets</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                  {colorPresets.map((preset) => (
                    <button
                      key={preset.name}
                      onClick={() => applyColorPreset(preset)}
                      className="p-4 border rounded-lg hover:shadow-md transition-shadow"
                    >
                      <div className="flex gap-2 mb-2">
                        <div 
                          className="w-6 h-6 rounded"
                          style={{ backgroundColor: preset.primary }}
                        />
                        <div 
                          className="w-6 h-6 rounded"
                          style={{ backgroundColor: preset.secondary }}
                        />
                        <div 
                          className="w-6 h-6 rounded"
                          style={{ backgroundColor: preset.accent }}
                        />
                      </div>
                      <p className="text-sm font-medium">{preset.name}</p>
                    </button>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Custom Colors</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="primaryColor">Primary Color</Label>
                    <div className="flex gap-2">
                      <Input
                        id="primaryColor"
                        type="color"
                        value={theme.primaryColor}
                        onChange={(e) => handleInputChange('primaryColor', e.target.value)}
                        className="w-16 h-10 p-1"
                      />
                      <Input
                        value={theme.primaryColor}
                        onChange={(e) => handleInputChange('primaryColor', e.target.value)}
                        placeholder="#3b82f6"
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="secondaryColor">Secondary Color</Label>
                    <div className="flex gap-2">
                      <Input
                        id="secondaryColor"
                        type="color"
                        value={theme.secondaryColor}
                        onChange={(e) => handleInputChange('secondaryColor', e.target.value)}
                        className="w-16 h-10 p-1"
                      />
                      <Input
                        value={theme.secondaryColor}
                        onChange={(e) => handleInputChange('secondaryColor', e.target.value)}
                        placeholder="#10b981"
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="accentColor">Accent Color</Label>
                    <div className="flex gap-2">
                      <Input
                        id="accentColor"
                        type="color"
                        value={theme.accentColor}
                        onChange={(e) => handleInputChange('accentColor', e.target.value)}
                        className="w-16 h-10 p-1"
                      />
                      <Input
                        value={theme.accentColor}
                        onChange={(e) => handleInputChange('accentColor', e.target.value)}
                        placeholder="#8b5cf6"
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="backgroundColor">Background Color</Label>
                    <div className="flex gap-2">
                      <Input
                        id="backgroundColor"
                        type="color"
                        value={theme.backgroundColor}
                        onChange={(e) => handleInputChange('backgroundColor', e.target.value)}
                        className="w-16 h-10 p-1"
                      />
                      <Input
                        value={theme.backgroundColor}
                        onChange={(e) => handleInputChange('backgroundColor', e.target.value)}
                        placeholder="#ffffff"
                      />
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Typography Tab */}
          <TabsContent value="typography" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Font Settings</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="fontFamily">Font Family</Label>
                  <select
                    id="fontFamily"
                    value={theme.fontFamily}
                    onChange={(e) => handleInputChange('fontFamily', e.target.value)}
                    className="w-full p-2 border rounded-md"
                  >
                    {fontOptions.map((font) => (
                      <option key={font} value={font}>
                        {font.split(',')[0]}
                      </option>
                    ))}
                  </select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="fontSize">Base Font Size</Label>
                  <Input
                    id="fontSize"
                    value={theme.fontSize}
                    onChange={(e) => handleInputChange('fontSize', e.target.value)}
                    placeholder="16px"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="textColor">Text Color</Label>
                  <div className="flex gap-2">
                    <Input
                      id="textColor"
                      type="color"
                      value={theme.textColor}
                      onChange={(e) => handleInputChange('textColor', e.target.value)}
                      className="w-16 h-10 p-1"
                    />
                    <Input
                      value={theme.textColor}
                      onChange={(e) => handleInputChange('textColor', e.target.value)}
                      placeholder="#1f2937"
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Layout Tab */}
          <TabsContent value="layout" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Layout Settings</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="borderRadius">Border Radius</Label>
                  <Input
                    id="borderRadius"
                    value={theme.borderRadius}
                    onChange={(e) => handleInputChange('borderRadius', e.target.value)}
                    placeholder="8px"
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Dark Mode</Label>
                    <p className="text-xs text-muted-foreground">
                      Enable dark theme by default
                    </p>
                  </div>
                  <Switch
                    checked={theme.darkMode}
                    onCheckedChange={(checked) => handleInputChange('darkMode', checked)}
                  />
                </div>

                <Separator />

                <div className="space-y-2">
                  <Label htmlFor="customCss">Custom CSS</Label>
                  <Textarea
                    id="customCss"
                    value={theme.customCss}
                    onChange={(e) => handleInputChange('customCss', e.target.value)}
                    placeholder="/* Add your custom CSS here */"
                    rows={8}
                    className="font-mono text-sm"
                  />
                  <p className="text-xs text-muted-foreground">
                    Add custom CSS to override default styles
                  </p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </motion.div>
    </div>
  );
}
