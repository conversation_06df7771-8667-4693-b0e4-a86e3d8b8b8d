"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Badge } from "@/components/ui/badge";
import { MoreHorizontal, Pencil, Eye, BarChart } from "lucide-react";
import AdminLayout from "@/components/admin/AdminLayout";
import { Switch } from "@/components/ui/switch";
import { ALL_TOOLS } from "@/data/tools";
import Link from "next/link";

export default function ToolsManagementPage() {
  const [searchTerm, setSearchTerm] = useState("");
  const [tools, setTools] = useState(ALL_TOOLS.map(tool => ({
    ...tool,
    active: true,
    usageCount: Math.floor(Math.random() * 10000)
  })));

  const filteredTools = tools.filter(tool =>
    tool.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    tool.description.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const toggleToolStatus = (id: string) => {
    setTools(tools.map(tool =>
      tool.id === id ? { ...tool, active: !tool.active } : tool
    ));
  };

  const getCategoryBadge = (category: string) => {
    switch (category) {
      case "pdf":
        return <Badge className="bg-blue-500">PDF</Badge>;
      case "office":
        return <Badge className="bg-green-500">Office</Badge>;
      case "image":
        return <Badge className="bg-purple-500">Image</Badge>;
      default:
        return <Badge variant="outline">{category}</Badge>;
    }
  };

  return (
    <AdminLayout title="Tools Management">
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div className="w-full max-w-sm">
            <Input
              placeholder="Search tools..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          <Button asChild>
            <Link href="/admin/tools/stats">
              <BarChart className="mr-2 h-4 w-4" />
              Usage Statistics
            </Link>
          </Button>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>All Tools</CardTitle>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Tool</TableHead>
                  <TableHead>Category</TableHead>
                  <TableHead>Usage Count</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredTools.length > 0 ? (
                  filteredTools.map((tool) => (
                    <TableRow key={tool.id}>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <span className="text-xl">{tool.icon}</span>
                          <div>
                            <div className="font-medium">{tool.title}</div>
                            <div className="text-xs text-muted-foreground">{tool.description}</div>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>{getCategoryBadge(tool.category)}</TableCell>
                      <TableCell>{tool.usageCount.toLocaleString()}</TableCell>
                      <TableCell>
                        <Switch
                          checked={tool.active}
                          onCheckedChange={() => toggleToolStatus(tool.id)}
                        />
                      </TableCell>
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="icon">
                              <MoreHorizontal className="h-4 w-4" />
                              <span className="sr-only">Actions</span>
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem asChild>
                              <Link href={`/tools/${tool.id}`} target="_blank">
                                <Eye className="mr-2 h-4 w-4" />
                                View Tool
                              </Link>
                            </DropdownMenuItem>
                            <DropdownMenuItem asChild>
                              <Link href={`/admin/tools/edit/${tool.id}`}>
                                <Pencil className="mr-2 h-4 w-4" />
                                Edit Settings
                              </Link>
                            </DropdownMenuItem>
                            <DropdownMenuItem asChild>
                              <Link href={`/admin/tools/stats/${tool.id}`}>
                                <BarChart className="mr-2 h-4 w-4" />
                                View Stats
                              </Link>
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={5} className="text-center py-6 text-muted-foreground">
                      No tools found
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      </div>
    </AdminLayout>
  );
}
