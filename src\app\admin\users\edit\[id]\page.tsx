"use client";

import { useEffect, useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import AdminLayout from "@/components/admin/AdminLayout";
import { UserForm } from "@/components/admin/UserForm";
import { useParams } from "next/navigation";

// Mock user data for demonstration
const mockUsers = [
  {
    _id: "1",
    name: "Admin User",
    email: "<EMAIL>",
    role: "admin",
    createdAt: "2023-01-15",
  },
  {
    _id: "2",
    name: "Regular User 2",
    email: "<EMAIL>",
    role: "user",
    createdAt: "2023-03-28",
  },
  {
    _id: "3",
    name: "Regular User",
    email: "<EMAIL>",
    role: "user",
    createdAt: "2023-05-12",
  },
  {
    _id: "4",
    name: "Ghazwa User",
    email: "<EMAIL>",
    role: "user",
    createdAt: "2023-06-20",
  },
];

export default function EditUserPage() {
  const params = useParams();
  const userId = params.id as string;
  const [user, setUser] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // In a real app, this would fetch from API
    const fetchUser = async () => {
      try {
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 500));

        const foundUser = mockUsers.find(u => u._id === userId);
        if (foundUser) {
          setUser(foundUser);
        }
      } catch (error) {
        console.error("Error fetching user:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchUser();
  }, [userId]);

  if (loading) {
    return (
      <AdminLayout title="Edit User">
        <div className="flex items-center justify-center h-64">
          <p className="text-muted-foreground">Loading user data...</p>
        </div>
      </AdminLayout>
    );
  }

  if (!user) {
    return (
      <AdminLayout title="Edit User">
        <div className="flex items-center justify-center h-64">
          <p className="text-muted-foreground">User not found</p>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout title={`Edit User: ${user.name}`}>
      <Card>
        <CardHeader>
          <CardTitle>Edit User Information</CardTitle>
        </CardHeader>
        <CardContent>
          <UserForm initialData={user} />
        </CardContent>
      </Card>
    </AdminLayout>
  );
}