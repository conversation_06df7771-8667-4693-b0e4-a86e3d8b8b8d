import { NextRequest, NextResponse } from "next/server";
import connectToDatabase from "@/lib/db";
import BlogPost from "@/models/BlogPost";
import User from "@/models/User";
import { ALL_CALCULATORS } from "@/data/calculators";
import { toolConfigs } from "@/lib/tool-configs";

export async function GET(request: NextRequest) {
  try {
    // Check if user is admin (set by middleware)
    const userRole = request.headers.get("x-user-role");
    
    if (userRole !== "admin") {
      return NextResponse.json(
        { error: "Admin access required" },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const query = searchParams.get("q");
    const limit = parseInt(searchParams.get("limit") || "20");

    if (!query || query.trim().length < 2) {
      return NextResponse.json({
        success: true,
        results: [],
        count: 0
      });
    }

    const searchTerm = query.trim().toLowerCase();
    const results: any[] = [];

    await connectToDatabase();

    // Search in blog posts (including drafts for admin)
    try {
      const blogPosts = await BlogPost.find({
        $or: [
          { title: { $regex: searchTerm, $options: "i" } },
          { excerpt: { $regex: searchTerm, $options: "i" } },
          { content: { $regex: searchTerm, $options: "i" } }
        ]
      })
        .limit(Math.ceil(limit / 4))
        .select("title slug excerpt status publishedAt createdAt")
        .populate("authorId", "name")
        .sort({ createdAt: -1 });

      blogPosts.forEach(post => {
        results.push({
          id: post._id.toString(),
          type: "blog",
          title: post.title,
          description: `${post.excerpt || "Blog post"} (${post.status})`,
          url: `/admin/blog/edit/${post._id}`,
          category: `Blog - ${post.status}`
        });
      });
    } catch (error) {
      console.error("Error searching blog posts:", error);
    }

    // Search in users
    try {
      const users = await User.find({
        $or: [
          { name: { $regex: searchTerm, $options: "i" } },
          { email: { $regex: searchTerm, $options: "i" } }
        ]
      })
        .limit(Math.ceil(limit / 4))
        .select("name email role createdAt")
        .sort({ createdAt: -1 });

      users.forEach(user => {
        results.push({
          id: user._id.toString(),
          type: "user",
          title: user.name || "Unnamed User",
          description: `${user.email} (${user.role})`,
          url: `/admin/users/edit/${user._id}`,
          category: `User - ${user.role}`
        });
      });
    } catch (error) {
      console.error("Error searching users:", error);
    }

    // Search in calculators
    const matchingCalculators = ALL_CALCULATORS.filter(calc =>
      calc.title.toLowerCase().includes(searchTerm) ||
      calc.description.toLowerCase().includes(searchTerm) ||
      calc.category.toLowerCase().includes(searchTerm)
    ).slice(0, Math.ceil(limit / 4));

    matchingCalculators.forEach(calc => {
      results.push({
        id: calc.id,
        type: "calculator",
        title: calc.title,
        description: calc.description,
        url: `/calculators/${calc.id}`,
        category: `Calculator - ${calc.category}`
      });
    });

    // Search in tools
    const matchingTools = Object.entries(toolConfigs).filter(([key, config]) =>
      config.title.toLowerCase().includes(searchTerm) ||
      config.description.toLowerCase().includes(searchTerm) ||
      key.toLowerCase().includes(searchTerm)
    ).slice(0, Math.ceil(limit / 4));

    matchingTools.forEach(([key, config]) => {
      results.push({
        id: key,
        type: "tool",
        title: config.title.replace("How to ", ""),
        description: config.description,
        url: `/tools/${key}`,
        category: "PDF Tool"
      });
    });

    // Sort results by relevance
    results.sort((a, b) => {
      const aExact = a.title.toLowerCase() === searchTerm ? 1 : 0;
      const bExact = b.title.toLowerCase() === searchTerm ? 1 : 0;
      
      if (aExact !== bExact) return bExact - aExact;
      
      const aStartsWith = a.title.toLowerCase().startsWith(searchTerm) ? 1 : 0;
      const bStartsWith = b.title.toLowerCase().startsWith(searchTerm) ? 1 : 0;
      
      if (aStartsWith !== bStartsWith) return bStartsWith - aStartsWith;
      
      // Prioritize by type: users > blog > tools > calculators
      const typeOrder = { user: 0, blog: 1, tool: 2, calculator: 3 };
      const aTypeOrder = typeOrder[a.type as keyof typeof typeOrder] || 4;
      const bTypeOrder = typeOrder[b.type as keyof typeof typeOrder] || 4;
      
      if (aTypeOrder !== bTypeOrder) return aTypeOrder - bTypeOrder;
      
      return a.title.localeCompare(b.title);
    });

    return NextResponse.json({
      success: true,
      results: results.slice(0, limit),
      count: results.length,
      query: query
    });

  } catch (error) {
    console.error("Admin search API error:", error);
    return NextResponse.json(
      {
        success: false,
        error: "Search failed",
        results: [],
        count: 0
      },
      { status: 500 }
    );
  }
}
