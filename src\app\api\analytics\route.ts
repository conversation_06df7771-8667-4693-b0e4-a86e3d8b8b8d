import { NextRequest, NextResponse } from "next/server";
import connectToDatabase from "@/lib/db";
import { PageView, UserActivity, ToolUsage } from "@/models/Analytics";
import mongoose from "mongoose";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";

// Helper function to parse date range
function parseDateRange(range: string | null): { startDate: Date, endDate: Date } {
  const endDate = new Date();
  let startDate = new Date();

  switch (range) {
    case "day":
      startDate.setDate(endDate.getDate() - 1);
      break;
    case "week":
      startDate.setDate(endDate.getDate() - 7);
      break;
    case "month":
      startDate.setMonth(endDate.getMonth() - 1);
      break;
    case "quarter":
      startDate.setMonth(endDate.getMonth() - 3);
      break;
    case "year":
      startDate.setFullYear(endDate.getFullYear() - 1);
      break;
    case "2year":
      startDate.setFullYear(endDate.getFullYear() - 2);
      break;
    default:
      // Default to last 30 days
      startDate.setDate(endDate.getDate() - 30);
  }

  return { startDate, endDate };
}

// GET analytics data
export async function GET(request: NextRequest) {
  try {
    // Check if user is admin using NextAuth session
    const session = await getServerSession(authOptions);

    if (!session || session.user.role !== "admin") {
      return NextResponse.json(
        { error: "Unauthorized - Admin access required" },
        { status: 401 }
      );
    }

    await connectToDatabase();

    const { searchParams } = new URL(request.url);
    const type = searchParams.get("type") || "traffic";
    const range = searchParams.get("range") || "month";
    const { startDate, endDate } = parseDateRange(range);

    let data;

    switch (type) {
      case "traffic":
        // Get page view data aggregated by day
        data = await PageView.aggregate([
          {
            $match: {
              timestamp: { $gte: startDate, $lte: endDate }
            }
          },
          {
            $group: {
              _id: {
                $dateToString: { format: "%Y-%m-%d", date: "$timestamp" }
              },
              users: { $addToSet: "$userId" },
              sessions: { $addToSet: "$sessionId" },
              pageViews: { $sum: 1 }
            }
          },
          {
            $project: {
              _id: 0,
              date: "$_id",
              users: { $size: "$users" },
              sessions: { $size: "$sessions" },
              pageViews: 1
            }
          },
          { $sort: { date: 1 } }
        ]);

        // If no data, return empty array
        if (!data || data.length === 0) {
          console.log("API: No traffic data found, returning empty array");
          data = [];
        }
        break;

      case "tools":
        // Get tool usage data - include all statuses, not just completed
        data = await ToolUsage.aggregate([
          {
            $match: {
              timestamp: { $gte: startDate, $lte: endDate }
              // Removed status filter to include all tool usage attempts
            }
          },
          {
            $group: {
              _id: "$toolName",
              value: { $sum: 1 },
            }
          },
          {
            $project: {
              _id: 0,
              name: "$_id",
              value: 1
            }
          },
          { $sort: { value: -1 } },
          { $limit: 10 }
        ]);

        // Add colors to the data
        const colors = [
          '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0',
          '#9966FF', '#FF9F40', '#8BC34A', '#3F51B5',
          '#009688', '#FF5722'
        ];

        // If no data, return empty array
        if (!data || data.length === 0) {
          console.log("API: No tool usage data found, returning empty array");
          data = [];
        }

        data = data.map((item, index) => ({
          ...item,
          color: colors[index % colors.length]
        }));
        break;

      case "users":
        // Get user acquisition data (referrers)
        data = await PageView.aggregate([
          {
            $match: {
              timestamp: { $gte: startDate, $lte: endDate },
              referrer: { $nin: [null, ""] }
            }
          },
          {
            $group: {
              _id: {
                $cond: {
                  if: { $regexMatch: { input: "$referrer", regex: /google|bing|yahoo|baidu|duckduckgo/i } },
                  then: "Organic Search",
                  else: {
                    $cond: {
                      if: { $regexMatch: { input: "$referrer", regex: /facebook|twitter|instagram|linkedin|pinterest/i } },
                      then: "Social Media",
                      else: {
                        $cond: {
                          if: { $eq: ["$referrer", "direct"] },
                          then: "Direct",
                          else: "Referral"
                        }
                      }
                    }
                  }
                }
              },
              count: { $sum: 1 }
            }
          },
          {
            $project: {
              _id: 0,
              name: "$_id",
              value: "$count"
            }
          },
          { $sort: { value: -1 } }
        ]);

        // Add colors
        const userColors = {
          "Organic Search": "#4CAF50",
          "Direct": "#2196F3",
          "Social Media": "#9C27B0",
          "Referral": "#FFC107",
          "Email": "#F44336"
        };

        data = data.map(item => ({
          ...item,
          color: userColors[item.name as keyof typeof userColors] || "#607D8B"
        }));

        // If no data, return empty array
        if (!data || data.length === 0) {
          console.log("API: No user acquisition data found, returning empty array");
          data = [];
        }

        // Always add Email category with zero value if missing
        if (data.length > 0 && !data.find(item => item.name === "Email")) {
          data.push({ name: "Email", value: 0, color: "#F44336" });
        }
        break;

      case "conversion":
        // Calculate conversion rate (all tool usage / page views) by day
        const toolCompletions = await ToolUsage.aggregate([
          {
            $match: {
              timestamp: { $gte: startDate, $lte: endDate }
              // Include all tool usage, not just completed ones
            }
          },
          {
            $group: {
              _id: {
                $dateToString: { format: "%Y-%m-%d", date: "$timestamp" }
              },
              completions: { $sum: 1 }
            }
          },
          {
            $project: {
              _id: 0,
              date: "$_id",
              completions: 1
            }
          }
        ]);

        const pageViews = await PageView.aggregate([
          {
            $match: {
              timestamp: { $gte: startDate, $lte: endDate }
            }
          },
          {
            $group: {
              _id: {
                $dateToString: { format: "%Y-%m-%d", date: "$timestamp" }
              },
              views: { $sum: 1 }
            }
          },
          {
            $project: {
              _id: 0,
              date: "$_id",
              views: 1
            }
          }
        ]);

        // Merge the data and calculate conversion rate
        const completionsByDate = toolCompletions.reduce((acc, item) => {
          acc[item.date] = item.completions;
          return acc;
        }, {} as Record<string, number>);

        const viewsByDate = pageViews.reduce((acc, item) => {
          acc[item.date] = item.views;
          return acc;
        }, {} as Record<string, number>);

        // Get all unique dates
        const dateSet = new Set<string>();
        Object.keys(completionsByDate).forEach(date => dateSet.add(date));
        Object.keys(viewsByDate).forEach(date => dateSet.add(date));
        const allDates = Array.from(dateSet).sort();

        // Calculate conversion rates only if we have real data
        if (allDates.length > 0) {
          data = allDates.map(date => {
            const completions = completionsByDate[date] || 0;
            const views = viewsByDate[date] || 1; // Avoid division by zero
            const rate = (completions / views) * 100;
            return {
              date: date,
              rate: parseFloat(rate.toFixed(2)),
            };
          });
        } else {
          // If no data, return empty array
          console.log("API: No conversion rate data found, returning empty array");
          data = [];
        }
        break;

      case "summary":
        // Get summary statistics
        const totalUsers = await PageView.distinct("userId").then(users => users.filter(id => id !== null).length);
        const totalViews = await PageView.countDocuments({ timestamp: { $gte: startDate, $lte: endDate } });
        const totalToolUsage = await ToolUsage.countDocuments({ timestamp: { $gte: startDate, $lte: endDate } });

        // Calculate growth rates (compare with previous period)
        const periodLength = endDate.getTime() - startDate.getTime();
        const previousStartDate = new Date(startDate.getTime() - periodLength);
        const previousEndDate = new Date(startDate.getTime());

        const previousUsers = await PageView.distinct("userId", {
          timestamp: { $gte: previousStartDate, $lte: previousEndDate }
        }).then(users => users.filter(id => id !== null).length);
        const previousViews = await PageView.countDocuments({
          timestamp: { $gte: previousStartDate, $lte: previousEndDate }
        });
        const previousToolUsage = await ToolUsage.countDocuments({
          timestamp: { $gte: previousStartDate, $lte: previousEndDate }
        });

        // Calculate growth percentages
        const userGrowth = previousUsers > 0 ? ((totalUsers - previousUsers) / previousUsers) * 100 : 0;
        const viewGrowth = previousViews > 0 ? ((totalViews - previousViews) / previousViews) * 100 : 0;
        const toolGrowth = previousToolUsage > 0 ? ((totalToolUsage - previousToolUsage) / previousToolUsage) * 100 : 0;

        data = {
          summary: {
            totalUsers,
            totalPosts: 0, // You can add blog post count if needed
            totalTools: totalToolUsage,
            totalViews,
            userGrowth: Math.round(userGrowth * 100) / 100,
            postGrowth: 0,
            toolGrowth: Math.round(toolGrowth * 100) / 100,
            viewGrowth: Math.round(viewGrowth * 100) / 100
          }
        };
        break;

      default:
        return NextResponse.json(
          { error: "Invalid analytics type" },
          { status: 400 }
        );
    }

    return NextResponse.json({ data });

  } catch (error) {
    console.error("GET /api/analytics error:", error);
    return NextResponse.json(
      { error: "Failed to fetch analytics data" },
      { status: 500 }
    );
  }
}

// POST to record analytics events
export async function POST(request: NextRequest) {
  try {
    await connectToDatabase();

    const body = await request.json();
    const { type, data } = body;

    // Get user ID from request headers (set by middleware)
    let userId: mongoose.Types.ObjectId | undefined = undefined;
    const headerUserId = request.headers.get("x-user-id");

    if (headerUserId) {
      userId = new mongoose.Types.ObjectId(headerUserId);
      console.log("API POST: User ID from request headers:", userId);
    } else {
      // For anonymous users or if middleware didn't set headers
      console.log("API POST: No user ID in headers, recording as anonymous user");
    }

    let result;

    switch (type) {
      case "pageView":
        // For page views, we allow anonymous tracking (userId can be undefined)
        // This ensures pages are tracked even when users aren't logged in

        // Check if userId was provided in the request data
        let userIdToUse = userId;

        // If data contains a userId and it's not 'anonymous', use it
        if (data.userId && data.userId !== 'anonymous') {
          try {
            userIdToUse = new mongoose.Types.ObjectId(data.userId);
            console.log("API: Using userId from request data:", userIdToUse);
          } catch (error) {
            console.error("API: Invalid userId in request data:", data.userId);
          }
        }

        console.log("API: Recording page view", {
          path: data.path,
          userId: userIdToUse ? userIdToUse.toString() : 'anonymous',
          sessionId: data.sessionId,
          referrer: data.referrer || 'unknown'
        });

        result = await PageView.create({
          ...data,
          userId: userIdToUse, // Use the determined userId
          timestamp: new Date()
        });
        break;

      case "userActivity":
        // Check if userId was provided in the request data
        let userIdForActivity = userId;

        // If data contains a userId and it's not 'anonymous', use it
        if (data.userId && data.userId !== 'anonymous') {
          try {
            userIdForActivity = new mongoose.Types.ObjectId(data.userId);
            console.log("API: Using userId from request data for activity:", userIdForActivity);
          } catch (error) {
            console.error("API: Invalid userId in request data for activity:", data.userId);
          }
        }

        if (!userIdForActivity) {
          return NextResponse.json(
            { error: "User ID is required for user activity" },
            { status: 400 }
          );
        }

        result = await UserActivity.create({
          ...data,
          userId: userIdForActivity,
          timestamp: new Date()
        });
        break;

      case "toolUsage":
        // For tool usage, we allow anonymous tracking (userId can be undefined)
        // This ensures tools are tracked even when users aren't logged in

        // Check if userId was provided in the request data
        let userIdForTool = userId;

        // If data contains a userId and it's not 'anonymous', use it
        if (data.userId && data.userId !== 'anonymous') {
          try {
            userIdForTool = new mongoose.Types.ObjectId(data.userId);
            console.log("API: Using userId from request data for tool usage:", userIdForTool);
          } catch (error) {
            console.error("API: Invalid userId in request data for tool usage:", data.userId);
          }
        }

        console.log("API: Recording tool usage", {
          toolName: data.toolName,
          userId: userIdForTool ? userIdForTool.toString() : 'anonymous',
          sessionId: data.sessionId
        });

        result = await ToolUsage.create({
          ...data,
          userId: userIdForTool, // Use the determined userId
          timestamp: new Date()
        });
        break;

      default:
        return NextResponse.json(
          { error: "Invalid analytics type" },
          { status: 400 }
        );
    }

    return NextResponse.json({ success: true, id: result._id });

  } catch (error) {
    console.error("POST /api/analytics error:", error);
    return NextResponse.json(
      { error: "Failed to record analytics data" },
      { status: 500 }
    );
  }
}
