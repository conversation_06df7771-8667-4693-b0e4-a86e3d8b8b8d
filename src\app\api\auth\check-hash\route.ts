import { NextResponse } from "next/server";
import bcrypt from "bcryptjs";

export async function POST(req: Request) {
  try {
    const { password, hash } = await req.json();

    if (!password || !hash) {
      return NextResponse.json({ valid: false, error: "Missing parameters" }, { status: 400 });
    }

    const isValid = await bcrypt.compare(password, hash);

    return NextResponse.json({ valid: isValid });
  } catch (error) {
    console.error("Check hash error:", error);
    return NextResponse.json({ valid: false, error: "Internal error" }, { status: 500 });
  }
}
