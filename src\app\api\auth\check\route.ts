import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import connectToDatabase from "@/lib/db";
import User from "@/models/User";

/**
 * GET /api/auth/check
 * Checks if the user is authenticated and returns role information
 * Optimized for quick auth checks with minimal data
 */
export async function GET(request: NextRequest) {
  try {
    // Get session from NextAuth
    const session = await getServerSession(authOptions);

    if (!session || !session.user) {
      return NextResponse.json({
        isAuthenticated: false,
        isAdmin: false,
      });
    }

    // Connect to database to get the latest user data
    await connectToDatabase();

    // Get the latest user data from the database
    const user = await User.findOne({ email: session.user.email });

    if (!user) {
      return NextResponse.json({
        isAuthenticated: false,
        isAdmin: false,
        error: "User not found in database"
      });
    }

    // Return authentication info with the latest role from the database
    return NextResponse.json({
      isAuthenticated: true,
      isAdmin: user.role === "admin",
      role: user.role,
      userId: user._id,
      name: user.name,
      email: user.email,
    });
  } catch (error) {
    console.error("Auth check error:", error);
    return NextResponse.json(
      { error: "Authentication check failed", isAuthenticated: false },
      { status: 500 }
    );
  }
}
