import { NextRequest, NextResponse } from "next/server";
import { connectToDatabase } from "@/lib/mongo";
import crypto from "crypto";
import bcrypt from "bcryptjs";

export async function POST(request: NextRequest) {
  try {
    const { email } = await request.json();

    if (!email) {
      return NextResponse.json(
        { error: "Email is required" },
        { status: 400 }
      );
    }

    const { db } = await connectToDatabase();
    const normalizedEmail = email.toLowerCase();

    // Find user by email
    const user = await db.collection("users").findOne({
      email: normalizedEmail
    });

    if (!user) {
      // Don't reveal if user exists or not for security
      return NextResponse.json({
        message: "If an account with that email exists, we've sent a password reset link."
      });
    }

    // Generate reset token
    const resetToken = crypto.randomBytes(32).toString("hex");
    const resetTokenExpiry = new Date(Date.now() + 3600000); // 1 hour from now

    // Save reset token to user
    await db.collection("users").updateOne(
      { email: normalizedEmail },
      {
        $set: {
          resetToken,
          resetTokenExpiry,
          updatedAt: new Date()
        }
      }
    );

    // In a real application, you would send an email here
    // For now, we'll just log the reset link
    const resetUrl = `${process.env.NEXTAUTH_URL}/reset-password/${resetToken}`;
    console.log(`Password reset link for ${email}: ${resetUrl}`);

    // TODO: Send email with reset link
    // await sendPasswordResetEmail(email, resetUrl);

    return NextResponse.json({
      message: "If an account with that email exists, we've sent a password reset link.",
      // In development, include the reset link
      ...(process.env.NODE_ENV === "development" && { resetUrl })
    });

  } catch (error) {
    console.error("Forgot password error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
