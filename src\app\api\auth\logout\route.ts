import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";

/**
 * POST /api/auth/logout
 * Performs server-side logout actions
 * This is called by the client before calling NextAuth signOut
 */
export async function POST(_request: NextRequest) {
  try {
    // Get session from NextAuth
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json({ success: true, message: "Already logged out" });
    }

    // Here you can perform any server-side cleanup needed
    // For example, invalidate tokens, update user's last active timestamp, etc.

    // Return success
    return NextResponse.json({ success: true, message: "Logout successful" });
  } catch (error) {
    console.error("Logout error:", error);
    return NextResponse.json(
      { error: "Logout failed", success: false },
      { status: 500 }
    );
  }
}
