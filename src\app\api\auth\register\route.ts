import { NextResponse } from "next/server";
import connectToDatabase from "@/lib/db";
import User from "@/models/User";

/**
 * POST /api/auth/register
 * Register a new user
 */
export async function POST(req: Request) {
  try {
    const { email, password, name } = await req.json();

    if (!email || !password || !name) {
      return NextResponse.json({
        message: "Missing required fields",
        success: false
      }, { status: 400 });
    }

    // Connect to database
    await connectToDatabase();
    const normalizedEmail = email.toLowerCase();

    // Check if user exists
    const existingUser = await User.findOne({ email: normalizedEmail });
    if (existingUser) {
      return NextResponse.json({
        message: "Email already in use",
        success: false
      }, { status: 409 });
    }

    // Create new user with default role user
    // Password will be hashed automatically by the User model pre-save hook
    const user = await User.create({
      email: normalizedEmail,
      password,
      name,
      role: "user",
    });

    if (!user) {
      throw new Error("User creation failed");
    }

    return NextResponse.json({
      message: "Registration successful! Please sign in.",
      success: true
    }, { status: 201 });
  } catch (error) {
    console.error("Registration error:", error);
    return NextResponse.json({
      message: "Internal server error",
      success: false
    }, { status: 500 });
  }
}
