import { NextRequest, NextResponse } from "next/server";
import { connectToDatabase } from "@/lib/mongo";
import crypto from "crypto";
import bcrypt from "bcryptjs";

// POST - Send password reset email or reset password with token
export async function POST(request: NextRequest) {
  try {
    const { email, token, newPassword } = await request.json();

    if (!email) {
      return NextResponse.json(
        { error: "Email is required" },
        { status: 400 }
      );
    }

    const { db } = await connectToDatabase();

    // If this is a password reset request (no token provided)
    if (!token && !newPassword) {
      // Check if user exists
      const user = await db.collection("users").findOne({ email });

      if (!user) {
        // Don't reveal if user exists or not for security
        return NextResponse.json({
          message: "If an account with that email exists, we've sent a password reset link."
        });
      }

      // Generate reset token
      const resetToken = crypto.randomBytes(32).toString("hex");
      const resetTokenExpiry = new Date(Date.now() + 3600000); // 1 hour from now

      // Save reset token to user
      await db.collection("users").updateOne(
        { email },
        {
          $set: {
            resetToken,
            resetTokenExpiry,
          },
        }
      );

      // In a real application, you would send an email here
      // For now, we'll just log the reset link
      const resetLink = `${process.env.NEXTAUTH_URL || 'http://localhost:3000'}/reset-password?token=${resetToken}&email=${encodeURIComponent(email)}`;
      console.log("Password reset link:", resetLink);

      // TODO: Send email with reset link
      // await sendPasswordResetEmail(email, resetLink);

      return NextResponse.json({
        message: "If an account with that email exists, we've sent a password reset link."
      });
    }

    // If this is a password reset confirmation (token and newPassword provided)
    if (token && newPassword) {
      // Validate new password
      if (newPassword.length < 8) {
        return NextResponse.json(
          { error: "Password must be at least 8 characters long" },
          { status: 400 }
        );
      }

      // Find user with valid reset token
      const user = await db.collection("users").findOne({
        email,
        resetToken: token,
        resetTokenExpiry: { $gt: new Date() },
      });

      if (!user) {
        return NextResponse.json(
          { error: "Invalid or expired reset token" },
          { status: 400 }
        );
      }

      // Hash new password
      const hashedPassword = await bcrypt.hash(newPassword, 12);

      // Update user password and clear reset token
      await db.collection("users").updateOne(
        { email },
        {
          $set: {
            password: hashedPassword,
          },
          $unset: {
            resetToken: "",
            resetTokenExpiry: "",
          },
        }
      );

      return NextResponse.json({
        message: "Password has been reset successfully"
      });
    }

    return NextResponse.json(
      { error: "Invalid request" },
      { status: 400 }
    );

  } catch (error) {
    console.error("Password reset error:", error);
    return NextResponse.json(
      { error: "An unexpected error occurred" },
      { status: 500 }
    );
  }
}

// GET - Verify reset token
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const token = searchParams.get("token");
    const email = searchParams.get("email");

    if (!token || !email) {
      return NextResponse.json(
        { error: "Token and email are required" },
        { status: 400 }
      );
    }

    const { db } = await connectToDatabase();

    // Check if token is valid and not expired
    const user = await db.collection("users").findOne({
      email,
      resetToken: token,
      resetTokenExpiry: { $gt: new Date() },
    });

    if (!user) {
      return NextResponse.json(
        { error: "Invalid or expired reset token" },
        { status: 400 }
      );
    }

    return NextResponse.json({
      message: "Token is valid",
      email: user.email
    });

  } catch (error) {
    console.error("Token verification error:", error);
    return NextResponse.json(
      { error: "An unexpected error occurred" },
      { status: 500 }
    );
  }
}
