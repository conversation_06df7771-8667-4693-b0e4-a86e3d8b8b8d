import { NextRequest, NextResponse } from "next/server";
import connectToDatabase from "@/lib/db";
import User from "@/models/User";

/**
 * POST /api/auth/setup-admin
 * Creates an admin user if none exists
 * This endpoint should only be accessible in development mode or with a special setup key
 */
export async function POST(req: NextRequest) {
  try {
    // Only allow in development mode or with setup key
    const isDevMode = process.env.NODE_ENV === "development";
    const setupKey = req.headers.get("x-setup-key");
    const validSetupKey = setupKey === process.env.ADMIN_SETUP_KEY;

    if (!isDevMode && !validSetupKey) {
      return NextResponse.json(
        { error: "Unauthorized access" },
        { status: 403 }
      );
    }

    const { email, password, name } = await req.json();

    // Validate required fields
    if (!email || !password || !name) {
      return NextResponse.json(
        { error: "Missing required fields" },
        { status: 400 }
      );
    }

    // Connect to database
    await connectToDatabase();
    const normalizedEmail = email.toLowerCase();

    // Check if any admin user already exists
    const existingAdmin = await User.findOne({ role: "admin" });
    if (existingAdmin) {
      return NextResponse.json(
        { error: "Admin user already exists" },
        { status: 409 }
      );
    }

    // Check if email is already in use
    const existingUser = await User.findOne({ email: normalizedEmail });
    if (existingUser) {
      return NextResponse.json(
        { error: "Email already in use" },
        { status: 409 }
      );
    }

    // Create admin user
    // Password will be hashed automatically by the User model pre-save hook
    const user = await User.create({
      email: normalizedEmail,
      password,
      name,
      role: "admin",
      isProtected: true, // Protect this admin account
    });

    if (!user) {
      throw new Error("Admin user creation failed");
    }

    return NextResponse.json(
      { message: "Admin user created successfully" },
      { status: 201 }
    );
  } catch (error) {
    console.error("Admin setup error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
