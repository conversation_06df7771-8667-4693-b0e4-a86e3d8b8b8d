import { NextRequest, NextResponse } from "next/server";
import connectToDatabase from "@/lib/db";
import BlogPost from "@/models/BlogPost";
import User from "@/models/User"; // Import User model to register it
import mongoose from "mongoose";
import { z } from "zod";
import { extractFeaturedImageFromContent } from "@/lib/imageUtils";
import { handleBlogPostCategoryChange } from "@/lib/categorySync";

// Schema validation for updates
const BlogPostUpdateSchema = z.object({
  title: z.string().min(5, "Title must be at least 5 characters").optional(),
  content: z.string().min(50, "Content must be at least 50 characters").optional(),
  slug: z.string().optional(),
  description: z.string().optional(),
  tags: z.array(z.string()).optional(),
  categories: z.array(z.string()).optional(),
  status: z.enum(["draft", "scheduled", "published", "archived"]).optional(),
  visibility: z.enum(["public", "private", "draft"]).optional(),
  scheduledAt: z.union([z.date(), z.string(), z.null()]).optional().nullable(),
  featuredImage: z.string().optional(),
  imageCredit: z.string().optional(),
   publishedAt: z.union([z.date(), z.string(), z.null()]).optional().nullable(), // ✅ Add this
});

// Get a single blog post by ID
export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    await connectToDatabase();

    const { id } = await params;

    // Validate ID format
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return NextResponse.json(
        { error: "Invalid post ID format" },
        { status: 400 },
      );
    }

    const post = await BlogPost.findById(id).lean();

    if (!post) {
      return NextResponse.json(
        { error: "Blog post not found" },
        { status: 404 },
      );
    }

    // Add default author info
    const postWithAuthor = {
      ...post,
      author: {
        name: "Admin User",
        email: "<EMAIL>"
      }
    };

    return NextResponse.json(postWithAuthor);
  } catch (error: any) {
    console.error("Error fetching blog post:", error);
    return NextResponse.json(
      { error: "Failed to fetch blog post" },
      { status: 500 },
    );
  }
}

// Update a blog post
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> },
) {
  try {
    await connectToDatabase();

    // Get user ID and role from request headers (set by middleware)
    const userId = request.headers.get("x-user-id");
    const userRole = request.headers.get("x-user-role");
    const finalUserId = userId || "507f1f77bcf86cd799439011"; // Default for development

    if (!userId) {
      console.log("No user ID in headers for update, using default");
    }

    const { id } = await params;
    const data = await request.json();
    console.log("Updating blog post:", id, JSON.stringify(data, null, 2));

    // Validate ID format
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return NextResponse.json(
        { error: "Invalid post ID format" },
        { status: 400 },
      );
    }

    // Validate the update data
    const validation = BlogPostUpdateSchema.safeParse(data);
    if (!validation.success) {
      console.log("Update validation failed:", validation.error);
      return NextResponse.json(
        { error: "Validation failed", details: validation.error.issues },
        { status: 400 }
      );
    }

    // Find the post first to check ownership
    const existingPost = await BlogPost.findById(id);

    if (!existingPost) {
      return NextResponse.json(
        { error: "Blog post not found" },
        { status: 404 },
      );
    }

    // Only allow the author or admin to update (skip for development)
    if (
      userId &&
      existingPost.authorId.toString() !== userId &&
      userRole !== "admin"
    ) {
      return NextResponse.json(
        { error: "You are not authorized to update this post" },
        { status: 403 },
      );
    }

    // Prepare update data
    const updateData = { ...validation.data };

    // Extract featured image from content if content is being updated and no featured image provided
    if (updateData.content && !updateData.featuredImage) {
      const extractedImage = extractFeaturedImageFromContent(updateData.content);
      if (extractedImage) {
        updateData.featuredImage = extractedImage;
      }
    }

    // Handle status changes
    if (updateData.status === "published" && existingPost.status !== "published") {
      updateData.publishedAt = new Date();
    } else if (updateData.status === "scheduled" && updateData.scheduledAt) {
      updateData.scheduledAt = new Date(updateData.scheduledAt);
    }

    // Auto-generate slug if title is being updated but slug is not provided
    if (updateData.title && !updateData.slug) {
      updateData.slug = updateData.title
        .toLowerCase()
        .replace(/[^\w\s]/gi, "")
        .replace(/\s+/g, "-")
        .substring(0, 50);
    }

    // Track category changes for post count synchronization
    const oldCategoryId = existingPost.categoryId?.toString() || null;
    const newCategoryId = updateData.categories && updateData.categories.length > 0
      ? updateData.categories[0]
      : null;

    // Update the post
    const updatedPost = await BlogPost.findByIdAndUpdate(
      id,
      { $set: updateData },
      { new: true, runValidators: true, lean: true },
    );

    // Update category post counts if category changed
    if (oldCategoryId !== newCategoryId) {
      try {
        await handleBlogPostCategoryChange(oldCategoryId, newCategoryId, 'update');
      } catch (error) {
        console.error('Error updating category post counts:', error);
        // Don't fail the blog update if category sync fails
      }
    }

    // Add default author info
    const postWithAuthor = {
      ...updatedPost,
      author: {
        name: "Admin User",
        email: "<EMAIL>"
      }
    };

    return NextResponse.json(postWithAuthor);
  } catch (error: any) {
    console.error("Error updating blog post:", error);

    // Handle duplicate slug error
    if (error.code === 11000) {
      return NextResponse.json(
        { error: "A post with this slug already exists" },
        { status: 400 },
      );
    }

    return NextResponse.json(
      { error: "Failed to update blog post" },
      { status: 500 },
    );
  }
}

// Delete a blog post
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> },
) {
  try {
    await connectToDatabase();

    // Get user ID and role from request headers (set by middleware)
    const userId = request.headers.get("x-user-id");
    const userRole = request.headers.get("x-user-role");

    if (!userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id } = await params;

    // Validate ID format
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return NextResponse.json(
        { error: "Invalid post ID format" },
        { status: 400 },
      );
    }

    // Find the post first to check ownership
    const existingPost = await BlogPost.findById(id);

    if (!existingPost) {
      return NextResponse.json(
        { error: "Blog post not found" },
        { status: 404 },
      );
    }

    // Only allow the author or admin to delete
    if (
      existingPost.authorId.toString() !== userId &&
      userRole !== "admin"
    ) {
      return NextResponse.json(
        { error: "You are not authorized to delete this post" },
        { status: 403 },
      );
    }

    // Track category for post count synchronization
    const categoryId = existingPost.categoryId?.toString() || null;

    // Delete the post
    await BlogPost.findByIdAndDelete(id);

    // Update category post count if post had a category
    if (categoryId) {
      try {
        await handleBlogPostCategoryChange(categoryId, null, 'delete');
      } catch (error) {
        console.error('Error updating category post count after deletion:', error);
        // Don't fail the deletion if category sync fails
      }
    }

    return NextResponse.json(
      { message: "Blog post deleted successfully" },
      { status: 200 },
    );
  } catch (error: any) {
    console.error("Error deleting blog post:", error);
    return NextResponse.json(
      { error: "Failed to delete blog post" },
      { status: 500 },
    );
  }
}
