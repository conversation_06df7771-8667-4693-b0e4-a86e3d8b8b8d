import { NextRequest, NextResponse } from "next/server";
import connectToDatabase from "@/lib/db";
import Comment from "@/models/Comment";
import BlogPost from "@/models/BlogPost";
import mongoose from "mongoose";
import { z } from "zod";

// Schema validation for comments
const CommentSchema = z.object({
  content: z.string().min(3, "Comment must be at least 3 characters"),
  postId: z.string().min(1, "Post ID is required"),
  authorName: z.string().min(2, "Name is required"),
  authorEmail: z.string().email("Valid email is required"),
  parentId: z.string().optional().nullable(),
});

// List of static blog post slugs
const STATIC_BLOG_POSTS = [
  "pdf-management-tips",
  "word-to-pdf-benefits",
  "pdf-security-guide",
  "pdf-accessibility",
  "pdf-vs-other-formats",
  "pdf-compression-techniques"
];

// More lenient schema for static blog posts
const StaticPostCommentSchema = z.object({
  content: z.string().min(3, "Comment must be at least 3 characters"),
  postId: z.string().min(1, "Post ID is required"),
  authorName: z.string().min(1, "Name is required"),
  authorEmail: z.string(), // Any string is fine for static posts
  parentId: z.string().optional().nullable(),
});

// GET comments (with filtering options)
export async function GET(request: NextRequest) {
  try {
    await connectToDatabase();

    const { searchParams } = new URL(request.url);
    const postId = searchParams.get("postId");
    const status = searchParams.get("status") || "approved";
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "10");
    const skip = (page - 1) * limit;
    const search = searchParams.get("search");

    // Build query
    const query: any = {};

    // Filter by post ID or slug if provided
    if (postId) {
      console.log(`GET comments for postId: ${postId}`);

      // For static blog posts, just return an empty array of comments
      // This is a workaround for demo/static content
      if (STATIC_BLOG_POSTS.includes(postId)) {

        console.log(`Using static blog post: ${postId}`);
        return NextResponse.json({
          comments: [],
          pagination: {
            page: 1,
            limit: 10,
            total: 0,
            totalPages: 0
          }
        });
      }

      // Check if it's a valid MongoDB ObjectId
      if (mongoose.Types.ObjectId.isValid(postId)) {
        query.postId = postId;
        console.log(`Using ObjectId directly: ${postId}`);
      } else {
        // If not a valid ObjectId, try to find by slug
        console.log(`Looking up post by slug: ${postId}`);
        const post = await BlogPost.findOne({ slug: postId });

        if (!post) {
          console.error(`Post not found for slug: ${postId}`);

          // Try to find all posts to see what's available
          const allPosts = await BlogPost.find({}).select('_id title slug').limit(5);
          console.log('Available posts:', allPosts);

          // Return empty comments instead of 404 for better UX
          return NextResponse.json({
            comments: [],
            pagination: {
              page: 1,
              limit: 10,
              total: 0,
              totalPages: 0
            }
          });
        }

        console.log(`Found post with ID: ${post._id} for slug: ${postId}`);
        query.postId = post._id;
      }
    }

    // Filter by status (admin can see all, public only sees approved)
    const userRole = request.headers.get("x-user-role");
    if (userRole === "admin") {
      if (status !== "all") {
        query.status = status;
      }
    } else {
      query.status = "approved";
    }

    // Add search if provided
    if (search) {
      query.$or = [
        { content: { $regex: search, $options: "i" } },
        { authorName: { $regex: search, $options: "i" } },
        { authorEmail: { $regex: search, $options: "i" } }
      ];
    }

    // Get total count for pagination
    const total = await Comment.countDocuments(query);

    // Get comments with pagination
    const comments = await Comment.find(query)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .populate("userId", "name email")
      .populate("postId", "title slug");

    // Get top-level comments and their replies
    if (postId && !search && status === "approved") {
      const topLevelComments = await Comment.find({
        postId: query.postId, // Use the resolved postId from query
        status: "approved",
        parentId: { $exists: false }
      })
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .populate("userId", "name email")
      .populate("postId", "title slug");

      // Get replies for each top-level comment
      const commentsWithReplies = await Promise.all(
        topLevelComments.map(async (comment) => {
          const replies = await Comment.find({
            parentId: comment._id,
            status: "approved"
          })
          .sort({ createdAt: 1 })
          .populate("userId", "name email");

          return {
            ...comment.toObject(),
            replies: replies
          };
        })
      );

      return NextResponse.json({
        comments: commentsWithReplies,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit)
        }
      });
    }

    return NextResponse.json({
      comments,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error("GET /api/blog/comments error:", error);
    return NextResponse.json(
      { error: "Failed to fetch comments" },
      { status: 500 }
    );
  }
}

// POST a new comment
export async function POST(request: NextRequest) {
  try {
    await connectToDatabase();

    // Get user ID from request headers (set by middleware)
    const userId = request.headers.get("x-user-id");
    const userRole = request.headers.get("x-user-role");

    const body = await request.json();
    console.log('Received comment data:', body);

    // Check if this is a static blog post
    const isStaticPost = STATIC_BLOG_POSTS.includes(body.postId);

    // Use the appropriate schema based on post type
    const validation = isStaticPost
      ? StaticPostCommentSchema.safeParse(body)
      : CommentSchema.safeParse(body);

    if (!validation.success) {
      console.error('Validation failed:', validation.error);
      return NextResponse.json(
        { error: "Validation failed", details: validation.error },
        { status: 400 }
      );
    }

    // Verify the post exists - handle both ObjectId and slug
    let post;

    // For static blog posts, create a mock post object
    if (STATIC_BLOG_POSTS.includes(validation.data.postId)) {

      console.log(`Using static blog post: ${validation.data.postId}`);
      // Create a mock comment with the current timestamp
      const mockComment = {
        _id: new mongoose.Types.ObjectId().toString(),
        content: validation.data.content,
        authorName: validation.data.authorName,
        authorEmail: validation.data.authorEmail,
        postId: validation.data.postId,
        createdAt: new Date().toISOString(),
        isAdmin: userRole === "admin",
        status: userRole === "admin" ? "approved" : "pending",
        parentId: validation.data.parentId,
        userId: userId || undefined,
        role: userRole || 'user',
      };

      return NextResponse.json(mockComment, { status: 201 });
    }

    // Check if postId is a valid MongoDB ObjectId
    if (mongoose.Types.ObjectId.isValid(validation.data.postId)) {
      post = await BlogPost.findById(validation.data.postId);
    } else {
      // If not a valid ObjectId, try to find by slug
      post = await BlogPost.findOne({ slug: validation.data.postId });

      // Log for debugging
      console.log(`Looking up post by slug: ${validation.data.postId}`);
      console.log(`Found post:`, post);
    }

    if (!post) {
      // Log the error for debugging
      console.error(`Post not found for ID/slug: ${validation.data.postId}`);

      // Try to find all posts to see what's available
      const allPosts = await BlogPost.find({}).select('_id title slug').limit(5);
      console.log('Available posts:', allPosts);

      return NextResponse.json(
        { error: "Blog post not found" },
        { status: 404 }
      );
    }

    // Use the actual post ID for the comment
    validation.data.postId = post._id.toString();

    // Create the comment
    const comment = await Comment.create({
      ...validation.data,
      userId: userId || undefined,
      status: userRole === "admin" ? "approved" : "pending", // Auto-approve admin comments
      isAdmin: userRole === "admin",
      role: userRole || 'user',
    });

    // Update comment count on the blog post
    await BlogPost.findByIdAndUpdate(
      validation.data.postId,
      { $inc: { commentCount: 1 } }
    );

    return NextResponse.json(comment, { status: 201 });
  } catch (error) {
    console.error("POST /api/blog/comments error:", error);
    return NextResponse.json(
      { error: "Failed to create comment" },
      { status: 500 }
    );
  }
}
