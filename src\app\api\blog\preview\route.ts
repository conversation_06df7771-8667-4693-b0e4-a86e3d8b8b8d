import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { connectToDatabase } from "@/lib/mongo";
import { ObjectId } from "mongodb";

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const id = searchParams.get("id");

    if (!id) {
      return NextResponse.json(
        { error: "Post ID is required" },
        { status: 400 }
      );
    }

    // Handle "new" post case
    if (id === "new") {
      return NextResponse.json({
        _id: "new",
        title: "New Blog Post",
        content: "",
        excerpt: "",
        category: "",
        tags: [],
        status: "draft",
        visibility: "public",
        featuredImage: "",
        publishedAt: null,
        scheduledFor: null,
        author: {
          _id: session.user.id,
          name: session.user.name,
          email: session.user.email
        },
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        slug: "",
        metaTitle: "",
        metaDescription: "",
        readingTime: 0,
        viewCount: 0
      });
    }

    const { db } = await connectToDatabase();

    // Check if it's a valid ObjectId
    let post;
    if (ObjectId.isValid(id)) {
      post = await db.collection("posts").findOne({ _id: new ObjectId(id) });
    } else {
      // Try to find by slug
      post = await db.collection("posts").findOne({ slug: id });
    }

    if (!post) {
      return NextResponse.json(
        { error: "Post not found" },
        { status: 404 }
      );
    }

    // Check if user has permission to preview this post
    const user = await db.collection("users").findOne({ 
      email: session.user.email 
    });

    const isAdmin = user?.role === "admin";
    const isAuthor = post.authorId?.toString() === session.user.id;

    if (!isAdmin && !isAuthor && post.status !== "published") {
      return NextResponse.json(
        { error: "Access denied" },
        { status: 403 }
      );
    }

    // Get author information
    let author = null;
    if (post.authorId) {
      author = await db.collection("users").findOne(
        { _id: new ObjectId(post.authorId) },
        { projection: { name: 1, email: 1, avatar: 1 } }
      );
    }

    // Format the post for preview
    const formattedPost = {
      ...post,
      _id: post._id.toString(),
      author: author ? {
        _id: author._id.toString(),
        name: author.name,
        email: author.email,
        avatar: author.avatar || null
      } : null,
      createdAt: post.createdAt?.toISOString() || new Date().toISOString(),
      updatedAt: post.updatedAt?.toISOString() || new Date().toISOString(),
      publishedAt: post.publishedAt?.toISOString() || null,
      scheduledFor: post.scheduledFor?.toISOString() || null
    };

    return NextResponse.json(formattedPost);

  } catch (error) {
    console.error("Blog preview error:", error);
    return NextResponse.json(
      { error: "Failed to load blog preview" },
      { status: 500 }
    );
  }
}
