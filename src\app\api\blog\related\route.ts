import { NextRequest, NextResponse } from 'next/server';
import { fetchRelatedBlogPosts } from '@/lib/db-server';

// GET /api/blog/related - Get related blog posts
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const currentSlug = searchParams.get('currentSlug');
    const category = searchParams.get('category');
    const tagsParam = searchParams.get('tags');
    const limitParam = searchParams.get('limit');

    if (!currentSlug || !category) {
      return NextResponse.json(
        { error: 'currentSlug and category are required' },
        { status: 400 }
      );
    }

    const tags = tagsParam ? tagsParam.split(',').filter(Boolean) : [];
    const limit = limitParam ? parseInt(limitParam) : 6;

    const relatedPosts = await fetchRelatedBlogPosts(currentSlug, category, tags, limit);

    return NextResponse.json(relatedPosts);
  } catch (error) {
    console.error('Error fetching related blog posts:', error);
    return NextResponse.json(
      { error: 'Failed to fetch related blog posts' },
      { status: 500 }
    );
  }
}
