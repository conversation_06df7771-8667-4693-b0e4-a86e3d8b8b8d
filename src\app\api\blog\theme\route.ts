import { NextRequest, NextResponse } from "next/server";
import connectToDatabase from "@/lib/db";
import mongoose from "mongoose";
import { z } from "zod";

// Define a schema for the theme settings
const ThemeSettingsSchema = z.object({
  primaryColor: z.string().optional(),
  secondaryColor: z.string().optional(),
  fontFamily: z.string().optional(),
  logoUrl: z.string().url().optional().nullable(),
  darkMode: z.boolean().optional(),
  accentColor: z.string().optional(),
  headerStyle: z.enum(["default", "centered", "minimal"]).optional(),
  sidebarEnabled: z.boolean().optional(),
  customCss: z.string().optional(),
});

// Create a model for theme settings if it doesn't exist
let ThemeSettings: mongoose.Model<any>;

try {
  // Try to get the model if it already exists
  ThemeSettings = mongoose.model("ThemeSettings");
} catch (error) {
  // Define the schema and create the model if it doesn't exist
  const themeSettingsSchema = new mongoose.Schema({
    primaryColor: { type: String, default: "#3b82f6" },
    secondaryColor: { type: String, default: "#10b981" },
    fontFamily: { type: String, default: "Inter, sans-serif" },
    logoUrl: { type: String, default: null },
    darkMode: { type: Boolean, default: true },
    accentColor: { type: String, default: "#8b5cf6" },
    headerStyle: { type: String, enum: ["default", "centered", "minimal"], default: "default" },
    sidebarEnabled: { type: Boolean, default: true },
    customCss: { type: String, default: "" },
    createdAt: { type: Date, default: Date.now },
    updatedAt: { type: Date, default: Date.now }
  });
  
  ThemeSettings = mongoose.model("ThemeSettings", themeSettingsSchema);
}

// GET theme settings
export async function GET(request: NextRequest) {
  try {
    await connectToDatabase();
    
    // Get theme settings from database
    let themeSettings = await ThemeSettings.findOne({});
    
    // If no settings exist, create default settings
    if (!themeSettings) {
      themeSettings = await ThemeSettings.create({
        primaryColor: "#3b82f6",
        secondaryColor: "#10b981",
        fontFamily: "Inter, sans-serif",
        logoUrl: null,
        darkMode: true,
        accentColor: "#8b5cf6",
        headerStyle: "default",
        sidebarEnabled: true,
        customCss: ""
      });
    }
    
    return NextResponse.json(themeSettings);
  } catch (error) {
    console.error("GET /api/blog/theme error:", error);
    return NextResponse.json(
      { error: "Failed to fetch theme settings" },
      { status: 500 }
    );
  }
}

// PUT to update theme settings
export async function PUT(request: NextRequest) {
  try {
    // Check authentication using request headers (set by middleware)
    const userRole = request.headers.get("x-user-role");
    
    // Only admin users can update theme settings
    if (userRole !== "admin") {
      return NextResponse.json(
        { error: "Unauthorized - Admin access required" },
        { status: 401 }
      );
    }
    
    await connectToDatabase();
    
    const body = await request.json();
    const validation = ThemeSettingsSchema.safeParse(body);
    
    if (!validation.success) {
      return NextResponse.json(
        { error: "Validation failed", details: validation.error },
        { status: 400 }
      );
    }
    
    // Get existing settings or create new ones
    let themeSettings = await ThemeSettings.findOne({});
    
    if (!themeSettings) {
      // Create new settings with the provided values
      themeSettings = await ThemeSettings.create({
        ...validation.data,
        createdAt: new Date(),
        updatedAt: new Date()
      });
    } else {
      // Update existing settings
      themeSettings = await ThemeSettings.findOneAndUpdate(
        {},
        { 
          ...validation.data,
          updatedAt: new Date()
        },
        { new: true }
      );
    }
    
    return NextResponse.json(themeSettings);
  } catch (error) {
    console.error("PUT /api/blog/theme error:", error);
    return NextResponse.json(
      { error: "Failed to update theme settings" },
      { status: 500 }
    );
  }
}
