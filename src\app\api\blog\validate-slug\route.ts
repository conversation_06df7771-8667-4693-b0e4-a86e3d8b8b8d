import { NextRequest, NextResponse } from "next/server";
import connectToDatabase from "@/lib/db";
import BlogPost from "@/models/BlogPost";

export async function GET(request: NextRequest) {
  try {
    await connectToDatabase();

    const { searchParams } = new URL(request.url);
    const slug = searchParams.get("slug");
    const excludeId = searchParams.get("excludeId"); // For editing existing posts

    if (!slug) {
      return NextResponse.json(
        { error: "Slug is required" },
        { status: 400 }
      );
    }

    // Check if slug already exists
    const query: any = { slug };
    if (excludeId) {
      query._id = { $ne: excludeId };
    }

    const existingPost = await BlogPost.findOne(query);

    return NextResponse.json({
      available: !existingPost,
      slug,
      exists: !!existingPost
    });

  } catch (error) {
    console.error("GET /api/blog/validate-slug error:", error);
    return NextResponse.json(
      { error: "Failed to validate slug" },
      { status: 500 }
    );
  }
}
