import { NextRequest, NextResponse } from "next/server";
import connectToDatabase from "@/lib/db";
import Comment from "@/models/Comment";
import BlogPost from "@/models/BlogPost";
import { z } from "zod";

// Schema validation for comments
const CommentSchema = z.object({
  content: z.string().min(3, "Comment must be at least 3 characters"),
  postId: z.string().min(1, "Post ID is required"),
  authorName: z.string().min(2, "Name is required"),
  authorEmail: z.string().email("Valid email is required"),
  authorWebsite: z.string().url().optional().nullable(),
  parentId: z.string().optional().nullable(),
});

// GET comments (with filtering options)
export async function GET(request: NextRequest) {
  try {
    await connectToDatabase();

    const { searchParams } = new URL(request.url);
    const postId = searchParams.get("postId");
    const status = searchParams.get("status") || "approved";
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "10");
    const skip = (page - 1) * limit;
    const search = searchParams.get("search");

    // Build query
    const query: any = {};
    
    // Filter by post ID if provided
    if (postId) {
      query.postId = postId;
    }
    
    // Filter by status (admin can see all, public only sees approved)
    const userRole = request.headers.get("x-user-role");
    if (userRole === "admin") {
      if (status !== "all") {
        query.status = status;
      }
    } else {
      query.status = "approved";
    }
    
    // Add search if provided
    if (search) {
      query.$text = { $search: search };
    }

    // Get total count for pagination
    const total = await Comment.countDocuments(query);
    
    // Get comments with pagination
    const comments = await Comment.find(query)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .populate("userId", "name email")
      .populate("postId", "title slug");
    
    // Get top-level comments and their replies
    if (postId && !search && status === "approved") {
      const topLevelComments = await Comment.find({ 
        postId, 
        status: "approved",
        parentId: { $exists: false }
      })
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .populate("userId", "name email")
      .populate("postId", "title slug");
      
      // Get replies for each top-level comment
      const commentsWithReplies = await Promise.all(
        topLevelComments.map(async (comment) => {
          const replies = await Comment.find({
            parentId: comment._id,
            status: "approved"
          })
          .sort({ createdAt: 1 })
          .populate("userId", "name email");
          
          return {
            ...comment.toObject(),
            replies: replies
          };
        })
      );
      
      return NextResponse.json({
        comments: commentsWithReplies,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit)
        }
      });
    }

    return NextResponse.json({
      comments,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error("GET /api/comments error:", error);
    return NextResponse.json(
      { error: "Failed to fetch comments" },
      { status: 500 }
    );
  }
}

// POST a new comment
export async function POST(request: NextRequest) {
  try {
    await connectToDatabase();

    // Get user ID from request headers (set by middleware)
    const userId = request.headers.get("x-user-id");
    const userRole = request.headers.get("x-user-role");

    const body = await request.json();
    const validation = CommentSchema.safeParse(body);
    
    if (!validation.success) {
      return NextResponse.json(
        { error: "Validation failed", details: validation.error },
        { status: 400 }
      );
    }

    // Verify the post exists
    const post = await BlogPost.findById(validation.data.postId);
    if (!post) {
      return NextResponse.json(
        { error: "Blog post not found" },
        { status: 404 }
      );
    }

    // Create the comment
    const comment = await Comment.create({
      ...validation.data,
      userId: userId || undefined,
      status: userRole === "admin" ? "approved" : "pending", // Auto-approve admin comments
      isAdmin: userRole === "admin",
    });

    return NextResponse.json(comment, { status: 201 });
  } catch (error) {
    console.error("POST /api/comments error:", error);
    return NextResponse.json(
      { error: "Failed to create comment" },
      { status: 500 }
    );
  }
}
