import { NextRequest, NextResponse } from "next/server";
import connectToDatabase from "@/lib/db";
import SiteSettings from "@/models/SiteSettings";

// GET site metadata
export async function GET(request: NextRequest) {
  try {
    await connectToDatabase();

    // Find the site settings (there should only be one document)
    const settings = await SiteSettings.findOne({});

    // If no settings exist yet, return default metadata
    if (!settings) {
      return NextResponse.json({
        title: "PDF Tools - All-in-one PDF Solution",
        description: "Free online PDF tools to merge, compress, convert, and edit PDF files",
        keywords: ["PDF tools", "convert PDF", "edit PDF", "merge PDF", "compress PDF"],
        ogTitle: "PDF Tools - All-in-one PDF Solution",
        ogDescription: "Free online PDF tools to merge, compress, convert, and edit PDF files",
        ogImage: "/og-image.jpg",
        twitterHandle: "@pdftools",
        logoUrl: "/logo.png",
        faviconUrl: "/favicon.ico",
      });
    }

    // Return the metadata from the settings
    return NextResponse.json({
      title: settings.metaTitle,
      description: settings.metaDescription,
      keywords: settings.metaKeywords,
      ogTitle: settings.ogTitle,
      ogDescription: settings.ogDescription,
      ogImage: settings.ogImage,
      twitterHandle: settings.twitterHandle,
      logoUrl: settings.logoUrl,
      faviconUrl: settings.faviconUrl,
      siteName: settings.siteName,
      siteUrl: settings.siteUrl,
    });
  } catch (error) {
    console.error("GET /api/metadata error:", error);
    return NextResponse.json(
      { error: "Failed to fetch site metadata" },
      { status: 500 }
    );
  }
}
