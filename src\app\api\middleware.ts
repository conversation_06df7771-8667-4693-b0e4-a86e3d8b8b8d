import { NextRequest, NextResponse } from "next/server";
import { getToken } from "next-auth/jwt";

const secret = process.env.NEXTAUTH_SECRET;

export async function middleware(req: NextRequest) {
  const token = await getToken({ req, secret });
  const pathname = req.nextUrl.pathname;

  // Handle API routes - add user headers for authenticated requests
  if (pathname.startsWith("/api")) {
    const requestHeaders = new Headers(req.headers);
    
    if (token) {
      requestHeaders.set('x-user-id', token.sub as string);
      requestHeaders.set('x-user-role', token.role as string || 'user');
      requestHeaders.set('x-user-email', token.email as string);
    }
    
    return NextResponse.next({
      request: {
        headers: requestHeaders,
      },
    });
  }

  // Public routes accessible by anyone
  const publicRoutes = ["/", "/tools", "/blog", "/login", "/register", "/forgot-password"];

  // Allow access to public routes without token
  if (publicRoutes.some(route => pathname === route || pathname.startsWith(route + "/"))) {
    return NextResponse.next();
  }

  // Protect /user routes for logged-in users only
  if (pathname.startsWith("/user")) {
    if (!token) {
      // Redirect to login if no valid token
      return NextResponse.redirect(new URL("/login", req.url));
    }
    return NextResponse.next();
  }

  // Protect /admin routes for admin users only
  if (pathname.startsWith("/admin")) {
    if (!token) {
      return NextResponse.redirect(new URL("/login", req.url));
    }

    if (token.role !== "admin") {
      // Redirect non-admin users to home
      return NextResponse.redirect(new URL("/", req.url));
    }

    return NextResponse.next();
  }

  // Default: allow access to other routes
  return NextResponse.next();
}

export const config = {
  matcher: [
    "/admin/:path*",
    "/user/:path*",
    "/api/:path*",
  ],
};
