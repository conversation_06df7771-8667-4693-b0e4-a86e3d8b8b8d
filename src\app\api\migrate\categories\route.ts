import { NextRequest, NextResponse } from "next/server";
import connectToDatabase from "@/lib/db";
import BlogPost from "@/models/BlogPost";
import mongoose from "mongoose";

// Define Category model if it doesn't exist
let Category: mongoose.Model<any>;

try {
  Category = mongoose.model('Category');
} catch (error) {
  const categorySchema = new mongoose.Schema({
    name: {
      type: String,
      required: true,
      unique: true,
      trim: true,
    },
    description: {
      type: String,
      trim: true,
    },
    slug: {
      type: String,
      unique: true,
      trim: true,
      lowercase: true,
    },
    count: {
      type: Number,
      default: 0,
    },
  }, { timestamps: true });

  categorySchema.pre('save', function(next) {
    if (this.isModified('name')) {
      this.slug = this.name.toLowerCase().replace(/\s+/g, '-').replace(/[^\w-]+/g, '');
    }
    next();
  });

  Category = mongoose.model('Category', categorySchema);
}

export async function POST(request: NextRequest) {
  try {
    await connectToDatabase();

    console.log('Starting blog category migration...');

    // Get all categories
    let categories = await Category.find();
    console.log(`Found ${categories.length} categories`);

    if (categories.length === 0) {
      console.log('No categories found. Creating default categories...');
      
      const defaultCategories = [
        { name: 'Technology', description: 'Technology related posts' },
        { name: 'Health', description: 'Health and wellness posts' },
        { name: 'General', description: 'General posts' }
      ];

      for (const cat of defaultCategories) {
        await Category.create(cat);
      }
      
      console.log('Created default categories');
      categories = await Category.find();
    }

    // Get all blog posts without categories
    const postsWithoutCategories = await BlogPost.find({
      $or: [
        { categoryId: null },
        { categoryId: { $exists: false } }
      ]
    });

    console.log(`Found ${postsWithoutCategories.length} posts without categories`);

    if (postsWithoutCategories.length === 0) {
      return NextResponse.json({
        success: true,
        message: 'All posts already have categories assigned',
        updated: 0
      });
    }

    // Assign categories based on content/title keywords
    const categoryKeywords = {
      'Technology': ['tech', 'technology', 'software', 'code', 'programming', 'development', 'app', 'web', 'digital', 'radix', 'react', 'ui'],
      'Health': ['health', 'medical', 'fitness', 'wellness', 'diet', 'exercise', 'nutrition'],
      'General': [] // fallback category
    };

    let updatedCount = 0;

    for (const post of postsWithoutCategories) {
      let assignedCategory = null;
      const postText = (post.title + ' ' + post.content + ' ' + (post.tags || []).join(' ')).toLowerCase();

      // Find matching category based on keywords
      for (const [categoryName, keywords] of Object.entries(categoryKeywords)) {
        if (categoryName === 'General') continue; // Skip general for now
        
        const hasKeyword = keywords.some(keyword => postText.includes(keyword));
        if (hasKeyword) {
          const category = categories.find(cat => cat.name === categoryName);
          if (category) {
            assignedCategory = category._id;
            break;
          }
        }
      }

      // If no category found, assign to General
      if (!assignedCategory) {
        const generalCategory = categories.find(cat => cat.name === 'General');
        if (generalCategory) {
          assignedCategory = generalCategory._id;
        }
      }

      // Update the post
      if (assignedCategory) {
        await BlogPost.findByIdAndUpdate(post._id, { categoryId: assignedCategory });
        updatedCount++;
        console.log(`Updated post "${post.title}" with category`);
      }
    }

    console.log(`Migration completed. Updated ${updatedCount} posts.`);

    // Update category counts
    console.log('Updating category counts...');
    const categoryUpdates = [];
    
    for (const category of categories) {
      const count = await BlogPost.countDocuments({
        categoryId: category._id,
        status: 'published',
        visibility: 'public'
      });
      
      await Category.findByIdAndUpdate(category._id, { count });
      categoryUpdates.push({ name: category.name, count });
      console.log(`Category "${category.name}" has ${count} posts`);
    }

    return NextResponse.json({
      success: true,
      message: `Migration completed successfully. Updated ${updatedCount} posts.`,
      updated: updatedCount,
      categoryUpdates
    });

  } catch (error: any) {
    console.error('Migration error:', error);
    return NextResponse.json(
      {
        success: false,
        error: "Migration failed",
        details: error.message
      },
      { status: 500 }
    );
  }
}
