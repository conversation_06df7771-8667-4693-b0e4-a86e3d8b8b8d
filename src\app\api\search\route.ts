import { NextRequest, NextResponse } from "next/server";
import connectToDatabase from "@/lib/db";
import mongoose from "mongoose";
import User from "@/models/User";
import BlogPost from "@/models/BlogPost";
import { ToolUsage } from "@/models/Analytics";

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const query = searchParams.get("q") || "";
    const type = searchParams.get("type") || "all";
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "10");

    // Check if query is empty
    if (!query.trim()) {
      return NextResponse.json({
        results: [],
        pagination: {
          total: 0,
          page,
          limit,
          totalPages: 0,
        },
      });
    }

    await connectToDatabase();

    // Get user role and ID from request headers (set by middleware)
    const userRole = request.headers.get("x-user-role") || "";
    const userId = request.headers.get("x-user-id") || "";

    // Determine user permissions based on role
    const isAdmin = userRole === "admin";

    let results: any[] = [];
    let total = 0;

    // Search based on type
    switch (type) {
      case "users":
        // Only admins can search users
        if (!isAdmin) {
          return NextResponse.json(
            { error: "Unauthorized to search users" },
            { status: 403 }
          );
        }

        // Search users by name or email
        const usersQuery = {
          $or: [
            { name: { $regex: query, $options: "i" } },
            { email: { $regex: query, $options: "i" } },
          ],
        };

        [results, total] = await Promise.all([
          User.find(usersQuery)
            .select("name email role createdAt")
            .sort({ createdAt: -1 })
            .skip((page - 1) * limit)
            .limit(limit)
            .lean(),
          User.countDocuments(usersQuery),
        ]);

        // Add result type
        results = results.map(item => ({ ...item, type: "user" }));
        break;

      case "blog":
        // Build blog query based on permissions
        let blogQuery: any = {
          $or: [
            { title: { $regex: query, $options: "i" } },
            { content: { $regex: query, $options: "i" } },
            { tags: { $regex: query, $options: "i" } },
          ],
        };

        // Regular users can only see published posts
        if (!isAdmin) {
          blogQuery.status = "published";
        }

        [results, total] = await Promise.all([
          BlogPost.find(blogQuery)
            .select("title slug status createdAt authorId")
            .sort({ createdAt: -1 })
            .skip((page - 1) * limit)
            .limit(limit)
            .populate("authorId", "name")
            .lean(),
          BlogPost.countDocuments(blogQuery),
        ]);

        // Add result type
        results = results.map(item => ({ ...item, type: "blog" }));
        break;

      case "tools":
        // Only admins can search tool usage
        if (!isAdmin) {
          return NextResponse.json(
            { error: "Unauthorized to search tool usage" },
            { status: 403 }
          );
        }

        // Search tool usage
        const toolsQuery = {
          $or: [
            { toolName: { $regex: query, $options: "i" } },
            { toolId: { $regex: query, $options: "i" } },
          ],
        };

        [results, total] = await Promise.all([
          ToolUsage.find(toolsQuery)
            .sort({ timestamp: -1 })
            .skip((page - 1) * limit)
            .limit(limit)
            .lean(),
          ToolUsage.countDocuments(toolsQuery),
        ]);

        // Add result type
        results = results.map(item => ({ ...item, type: "tool" }));
        break;

      case "all":
      default:
        // For all types, we need to combine results from different collections
        // This is a simplified approach - in a real app, you might want to use
        // a more sophisticated search solution like Elasticsearch

        // Define queries based on permissions
        const userQuery = isAdmin ? {
          $or: [
            { name: { $regex: query, $options: "i" } },
            { email: { $regex: query, $options: "i" } },
          ],
        } : null;

        let postQuery: any = {
          $or: [
            { title: { $regex: query, $options: "i" } },
            { content: { $regex: query, $options: "i" } },
            { tags: { $regex: query, $options: "i" } },
          ],
        };

        if (!isAdmin) {
          postQuery.status = "published";
        }

        const toolQuery = isAdmin ? {
          $or: [
            { toolName: { $regex: query, $options: "i" } },
            { toolId: { $regex: query, $options: "i" } },
          ],
        } : null;

        // Execute queries in parallel with error handling
        let users = [];
        let posts = [];
        let tools = [];

        try {
          [users, posts, tools] = await Promise.all([
            userQuery ? User.find(userQuery).select("name email role createdAt").limit(limit / 3).lean() : Promise.resolve([]),
            BlogPost.find(postQuery).select("title slug status createdAt authorId").limit(limit / 3).populate("authorId", "name").lean(),
            toolQuery ? ToolUsage.find(toolQuery).sort({ timestamp: -1 }).limit(limit / 3).lean() : Promise.resolve([]),
          ]);
        } catch (queryError) {
          console.error("Error in combined search queries:", queryError);
          // Continue with whatever results we have
        }

        // Ensure all results are arrays
        users = Array.isArray(users) ? users : [];
        posts = Array.isArray(posts) ? posts : [];
        tools = Array.isArray(tools) ? tools : [];

        // Combine and format results
        results = [
          ...users.map(item => ({ ...item, type: "user" })),
          ...posts.map(item => ({ ...item, type: "blog" })),
          ...tools.map(item => ({ ...item, type: "tool" })),
        ];

        // Sort by relevance (simplified - just by creation date)
        results.sort((a, b) => {
          const dateA = a.createdAt || a.timestamp || new Date(0);
          const dateB = b.createdAt || b.timestamp || new Date(0);
          return dateB - dateA;
        });

        // Paginate results
        total = results.length;
        results = results.slice((page - 1) * limit, page * limit);
        break;
    }

    return NextResponse.json({
      results,
      pagination: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      },
    });

  } catch (error) {
    console.error("Search API error:", error);

    // Provide more detailed error information
    const errorMessage = error instanceof Error
      ? `${error.name}: ${error.message}`
      : "Unknown error";

    console.error("Error details:", errorMessage);

    // Check for specific error types
    if (error instanceof mongoose.Error.ValidationError) {
      return NextResponse.json(
        { error: "Validation error", details: errorMessage },
        { status: 400 }
      );
    }

    if (error instanceof mongoose.Error.CastError) {
      return NextResponse.json(
        { error: "Invalid ID format", details: errorMessage },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "Failed to perform search", details: errorMessage },
      { status: 500 }
    );
  }
}
