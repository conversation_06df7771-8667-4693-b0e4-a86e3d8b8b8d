import { NextRequest, NextResponse } from "next/server";
import connectToDatabase from "@/lib/db";
import Category from "@/models/Category";

const defaultCategories = [
  {
    name: "Technology",
    description: "Latest technology trends, software development, and digital innovations"
  },
  {
    name: "Food", 
    description: "Recipes, cooking tips, restaurant reviews, and culinary adventures"
  },
  {
    name: "Automotive",
    description: "Car reviews, automotive technology, and transportation trends"
  },
  {
    name: "Design",
    description: "UI/UX design, graphic design, and creative inspiration"
  },
  {
    name: "AI",
    description: "Artificial intelligence, machine learning, and automation"
  },
  {
    name: "Web",
    description: "Web development, frameworks, and online technologies"
  },
  {
    name: "General",
    description: "General topics and miscellaneous content"
  }
];

export async function POST(request: NextRequest) {
  try {
    await connectToDatabase();
    
    console.log("Starting category seeding...");
    
    // Check if categories already exist
    const existingCategories = await Category.find();
    
    if (existingCategories.length > 0) {
      return NextResponse.json({
        success: true,
        message: `Found ${existingCategories.length} existing categories. No seeding needed.`,
        data: existingCategories
      });
    }
    
    // Create default categories
    const createdCategories = [];
    
    for (const categoryData of defaultCategories) {
      try {
        const category = await Category.create(categoryData);
        createdCategories.push(category);
        console.log(`Created category: ${category.name}`);
      } catch (error: any) {
        if (error.code === 11000) {
          console.log(`Category ${categoryData.name} already exists, skipping...`);
          const existing = await Category.findOne({ name: categoryData.name });
          if (existing) {
            createdCategories.push(existing);
          }
        } else {
          console.error(`Error creating category ${categoryData.name}:`, error);
        }
      }
    }
    
    return NextResponse.json({
      success: true,
      message: `Successfully seeded ${createdCategories.length} categories`,
      data: createdCategories
    });
    
  } catch (error) {
    console.error("Error seeding categories:", error);
    return NextResponse.json(
      { 
        success: false, 
        error: "Failed to seed categories",
        details: error instanceof Error ? error.message : "Unknown error"
      },
      { status: 500 }
    );
  }
}
