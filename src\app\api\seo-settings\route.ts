import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { connectToDatabase } from "@/lib/mongo";

// SEO Settings Schema
interface SEOSettings {
  siteTitle: string;
  siteDescription: string;
  metaKeywords: string;
  defaultShareImage: string;
  robotsAllowed: boolean;
  canonicalUrl: string;
  ogTitle: string;
  ogDescription: string;
  twitterCardType: string;
  twitterSite: string;
  favicon: string;
  sitemapEnabled: boolean;
  googleAnalyticsId: string;
  googleSearchConsoleId: string;
  updatedAt: Date;
  updatedBy: string;
}

// GET - Retrieve SEO settings
export async function GET() {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    const { db } = await connectToDatabase();
    const settings = await db.collection("seo_settings").findOne({});

    if (!settings) {
      // Return default settings if none exist
      const defaultSettings = {
        siteTitle: "ToolBox - PDF Tools & Calculators",
        siteDescription: "Professional PDF tools and calculators for all your document needs. Convert, compress, merge, and split PDFs with ease.",
        metaKeywords: "PDF tools, PDF converter, PDF compressor, calculators, document tools",
        defaultShareImage: "",
        robotsAllowed: true,
        canonicalUrl: "https://toolbox.com",
        ogTitle: "",
        ogDescription: "",
        twitterCardType: "summary_large_image",
        twitterSite: "@toolbox",
        favicon: "",
        sitemapEnabled: true,
        googleAnalyticsId: "",
        googleSearchConsoleId: ""
      };

      return NextResponse.json(defaultSettings);
    }

    // Remove MongoDB _id from response
    const { _id, ...settingsData } = settings;
    return NextResponse.json(settingsData);

  } catch (error) {
    console.error("Error fetching SEO settings:", error);
    return NextResponse.json(
      { error: "Failed to fetch SEO settings" },
      { status: 500 }
    );
  }
}

// POST - Save SEO settings
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Check if user is admin
    const { db } = await connectToDatabase();
    const user = await db.collection("users").findOne({
      email: session.user.email
    });

    if (!user || user.role !== "admin") {
      return NextResponse.json(
        { error: "Admin access required" },
        { status: 403 }
      );
    }

    const body = await request.json();

    // Validate required fields
    const requiredFields = ['siteTitle', 'siteDescription', 'canonicalUrl'];
    for (const field of requiredFields) {
      if (!body[field] || body[field].trim() === '') {
        return NextResponse.json(
          { error: `${field} is required` },
          { status: 400 }
        );
      }
    }

    // Validate URL format for canonicalUrl
    try {
      new URL(body.canonicalUrl);
    } catch {
      return NextResponse.json(
        { error: "Invalid canonical URL format" },
        { status: 400 }
      );
    }

    // Prepare settings data
    const settingsData: SEOSettings = {
      siteTitle: body.siteTitle.trim(),
      siteDescription: body.siteDescription.trim(),
      metaKeywords: body.metaKeywords || "",
      defaultShareImage: body.defaultShareImage || "",
      robotsAllowed: Boolean(body.robotsAllowed),
      canonicalUrl: body.canonicalUrl.trim(),
      ogTitle: body.ogTitle || "",
      ogDescription: body.ogDescription || "",
      twitterCardType: body.twitterCardType || "summary_large_image",
      twitterSite: body.twitterSite || "",
      favicon: body.favicon || "",
      sitemapEnabled: Boolean(body.sitemapEnabled),
      googleAnalyticsId: body.googleAnalyticsId || "",
      googleSearchConsoleId: body.googleSearchConsoleId || "",
      updatedAt: new Date(),
      updatedBy: session.user.email || ""
    };

    // Upsert settings (update if exists, create if not)
    await db.collection("seo_settings").replaceOne(
      {}, // Empty filter to match any document (since we only want one settings document)
      settingsData,
      { upsert: true }
    );

    // Generate robots.txt based on settings
    await generateRobotsTxt(settingsData);

    // Generate sitemap if enabled
    if (settingsData.sitemapEnabled) {
      await generateSitemap(settingsData);
    }

    return NextResponse.json({
      message: "SEO settings saved successfully",
      settings: settingsData
    });

  } catch (error) {
    console.error("Error saving SEO settings:", error);
    return NextResponse.json(
      { error: "Failed to save SEO settings" },
      { status: 500 }
    );
  }
}

// Helper function to generate robots.txt
async function generateRobotsTxt(settings: SEOSettings) {
  try {
    const robotsContent = settings.robotsAllowed
      ? `User-agent: *
Allow: /

Sitemap: ${settings.canonicalUrl}/sitemap.xml`
      : `User-agent: *
Disallow: /`;

    // In a real implementation, you would write this to public/robots.txt
    // For now, we'll just log it
    console.log("Generated robots.txt:", robotsContent);

  } catch (error) {
    console.error("Error generating robots.txt:", error);
  }
}

// Helper function to generate sitemap
async function generateSitemap(settings: SEOSettings) {
  try {
    // In a real implementation, you would generate an actual sitemap.xml
    // This would include all your pages, blog posts, tools, etc.
    const sitemapContent = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
  <url>
    <loc>${settings.canonicalUrl}</loc>
    <lastmod>${new Date().toISOString()}</lastmod>
    <changefreq>daily</changefreq>
    <priority>1.0</priority>
  </url>
  <url>
    <loc>${settings.canonicalUrl}/tools</loc>
    <lastmod>${new Date().toISOString()}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>0.8</priority>
  </url>
  <url>
    <loc>${settings.canonicalUrl}/calculators</loc>
    <lastmod>${new Date().toISOString()}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>0.8</priority>
  </url>
  <url>
    <loc>${settings.canonicalUrl}/blog</loc>
    <lastmod>${new Date().toISOString()}</lastmod>
    <changefreq>daily</changefreq>
    <priority>0.9</priority>
  </url>
</urlset>`;

    // In a real implementation, you would write this to public/sitemap.xml
    console.log("Generated sitemap.xml:", sitemapContent);

  } catch (error) {
    console.error("Error generating sitemap:", error);
  }
}
