import { NextRequest, NextResponse } from "next/server";
import connectToDatabase from "@/lib/db";
import mongoose from "mongoose";

// Define the Settings model
let Settings: any;

try {
  // Try to get the model if it already exists
  Settings = mongoose.model("Settings");
} catch (error) {
  // Define the schema and create the model if it doesn't exist
  const settingsSchema = new mongoose.Schema({
    // General settings
    siteName: { type: String, default: "PDF Tools Admin" },
    siteDescription: { type: String, default: "All-in-one PDF solution for your document needs" },
    siteUrl: { type: String, default: "https://pdf-tools.example.com" },
    maintenanceMode: { type: Boolean, default: false },
    maintenanceMessage: { type: String, default: "We're currently performing maintenance. Please check back soon." },

    // SEO settings
    metaTitle: { type: String, default: "PDF Tools - All-in-one PDF Solution" },
    metaDescription: { type: String, default: "Free online PDF tools to merge, compress, convert, and edit PDF files" },
    metaKeywords: { type: [String], default: ["PDF tools", "convert PDF", "edit PDF", "merge PDF", "compress PDF"] },

    // Social media & OG settings
    ogTitle: { type: String, default: "PDF Tools - All-in-one PDF Solution" },
    ogDescription: { type: String, default: "Free online PDF tools to merge, compress, convert, and edit PDF files" },
    ogImage: { type: String, default: "/og-image.jpg" },
    twitterHandle: { type: String, default: "@pdftools" },

    // Logo & Favicon
    logoUrl: { type: String, default: "/logo.png" },
    faviconUrl: { type: String, default: "/favicon.ico" },

    // Analytics & Tracking
    googleAnalyticsId: { type: String, default: "" },
    facebookPixelId: { type: String, default: "" },

    // Updated timestamp
    updatedAt: { type: Date, default: Date.now }
  });

  Settings = mongoose.model("Settings", settingsSchema);
}

// GET settings
export async function GET() {
  try {
    await connectToDatabase();

    // Get settings from database
    let settings = await Settings.findOne({});

    // If no settings exist, create default settings
    if (!settings) {
      settings = await Settings.create({});
    }

    return NextResponse.json(settings);
  } catch (error) {
    console.error("GET /api/settings error:", error);
    return NextResponse.json(
      { error: "Failed to fetch settings" },
      { status: 500 }
    );
  }
}

// PUT to update settings
export async function PUT(request: NextRequest) {
  try {
    // Check authentication using request headers (set by middleware)
    const userRole = request.headers.get("x-user-role");

    // Only admin users can update settings
    if (userRole !== "admin") {
      return NextResponse.json(
        { error: "Unauthorized - Admin access required" },
        { status: 401 }
      );
    }

    await connectToDatabase();

    const body = await request.json();

    // Get existing settings or create new ones
    let settings = await Settings.findOne({});

    if (!settings) {
      // Create new settings with the provided values
      settings = await Settings.create({
        ...body,
        updatedAt: new Date()
      });
    } else {
      // Update existing settings
      settings = await Settings.findOneAndUpdate(
        {},
        {
          ...body,
          updatedAt: new Date()
        },
        { new: true }
      );
    }

    return NextResponse.json(settings);
  } catch (error) {
    console.error("PUT /api/settings error:", error);
    return NextResponse.json(
      { error: "Failed to update settings" },
      { status: 500 }
    );
  }
}

