import { NextRequest, NextResponse } from "next/server";
import { writeFile, mkdir } from "fs/promises";
import { join } from "path";
import { v4 as uuidv4 } from "uuid";
import { existsSync } from "fs";
import { z } from "zod";
import connectToDatabase from "@/lib/db";
import Image from "@/models/Image";

// Validation schemas
const FileUploadSchema = z.object({
  type: z.enum(["blog-cover", "blog-content", "blog-featured"]).default("blog-content"),
  folder: z.string().optional(),
});

const UrlUploadSchema = z.object({
  imageUrl: z.string().url("Must be a valid URL"),
  type: z.enum(["blog-cover", "blog-content", "blog-featured"]).default("blog-content"),
  folder: z.string().optional(),
});

// Define allowed file types
const allowedFileTypes = [
  "image/jpeg",
  "image/jpg",
  "image/png",
  "image/webp",
];

// Define maximum file size (5MB)
const MAX_FILE_SIZE = 5 * 1024 * 1024;

// Helper function to save image metadata to database
async function saveImageToDatabase(imageData: any, userId: string, uploadType: 'file' | 'url', originalUrl?: string) {
  try {
    await connectToDatabase();

    const newImage = new Image({
      url: imageData.fileUrl || imageData.url,
      fileName: imageData.fileName,
      uploadedBy: userId,
      uploadedAt: new Date(),
      location: 'local',
      type: uploadType,
      originalUrl: originalUrl,
      width: imageData.width,
      height: imageData.height,
      bytes: imageData.bytes,
      folder: imageData.type || 'blog-content',
    });

    await newImage.save();
    return newImage;
  } catch (error) {
    console.warn('Failed to save image metadata to database:', error);
    return null;
  }
}

export async function POST(request: NextRequest) {
  try {
    // Check authentication using request headers (set by middleware)
    const userRole = request.headers.get("x-user-role");
    const userId = request.headers.get("x-user-id");

    // Only admin users can upload files
    if (userRole !== "admin" || !userId) {
      return NextResponse.json(
        { error: "Unauthorized - Admin access required" },
        { status: 401 }
      );
    }

    const contentType = request.headers.get("content-type");

    // Handle file upload
    if (contentType?.includes("multipart/form-data")) {
      return await handleFileUpload(request, userId);
    }

    // Handle URL upload
    if (contentType?.includes("application/json")) {
      return await handleUrlUpload(request, userId);
    }

    return NextResponse.json(
      { error: "Unsupported content type" },
      { status: 400 }
    );

  } catch (error) {
    console.error("POST /api/upload/local error:", error);
    return NextResponse.json(
      { error: "Failed to upload image" },
      { status: 500 }
    );
  }
}

async function handleFileUpload(request: NextRequest, userId: string) {
  try {
    // Parse the form data
    const formData = await request.formData();
    const file = formData.get("file") as File;
    const type = formData.get("type") as string || "blog-content";
    const folder = formData.get("folder") as string;

    if (!file) {
      return NextResponse.json(
        { error: "No file provided" },
        { status: 400 }
      );
    }

    // Validate input
    const validation = FileUploadSchema.safeParse({ type, folder });
    if (!validation.success) {
      return NextResponse.json(
        { error: "Invalid input", details: validation.error },
        { status: 400 }
      );
    }

    // Check file type
    if (!allowedFileTypes.includes(file.type)) {
      return NextResponse.json(
        { error: `File type not allowed. Allowed types: ${allowedFileTypes.join(", ")}` },
        { status: 400 }
      );
    }

    // Check file size
    if (file.size > MAX_FILE_SIZE) {
      return NextResponse.json(
        { error: "File size exceeds the limit of 5MB" },
        { status: 400 }
      );
    }

    // Sanitize filename
    const originalName = file.name.split('.')[0];
    const extension = file.name.split('.').pop() || 'jpg';
    const sanitizedName = originalName
      .replace(/[^a-zA-Z0-9.-]/g, '_')
      .replace(/_{2,}/g, '_')
      .toLowerCase();

    const fileName = `${sanitizedName}-${uuidv4()}.${extension}`;

    // Determine folder based on type
    const uploadFolder = folder || getUploadFolder(type);
    const uploadDir = join(process.cwd(), "public", "uploads", uploadFolder);

    // Ensure the upload directory exists
    if (!existsSync(uploadDir)) {
      await mkdir(uploadDir, { recursive: true });
      console.log(`Created directory: ${uploadDir}`);
    }

    // Create the full file path
    const filePath = join(uploadDir, fileName);

    // Convert the file to a Buffer
    const buffer = Buffer.from(await file.arrayBuffer());

    // Write the file to the server
    await writeFile(filePath, buffer);

    console.log(`File saved to: ${filePath}`);

    // Return the file URL
    const fileUrl = `/uploads/${uploadFolder}/${fileName}`;

    // Get image dimensions (basic implementation)
    const dimensions = await getImageDimensions(buffer);

    const result = {
      success: true,
      url: fileUrl,
      fileUrl,
      fileName,
      width: dimensions.width,
      height: dimensions.height,
      bytes: file.size,
      type: type,
    };

    // Save image metadata to database
    await saveImageToDatabase(result, userId, 'file');

    return NextResponse.json(result);

  } catch (error) {
    console.error("File upload error:", error);
    return NextResponse.json(
      { error: "Failed to upload file to local storage" },
      { status: 500 }
    );
  }
}

async function handleUrlUpload(request: NextRequest, userId: string) {
  try {
    const body = await request.json();

    // Validate input
    const validation = UrlUploadSchema.safeParse(body);
    if (!validation.success) {
      return NextResponse.json(
        { error: "Invalid input", details: validation.error },
        { status: 400 }
      );
    }

    const { imageUrl, type, folder } = validation.data;

    // Sanitize URL
    const sanitizedUrl = imageUrl.trim();

    // Validate URL format
    try {
      new URL(sanitizedUrl);
    } catch {
      return NextResponse.json(
        { error: "Invalid URL format" },
        { status: 400 }
      );
    }

    // Fetch the image
    const response = await fetch(sanitizedUrl, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (compatible; BlogImageUploader/1.0)',
      },
    });

    if (!response.ok) {
      return NextResponse.json(
        { error: `Failed to fetch image: ${response.status} ${response.statusText}` },
        { status: 400 }
      );
    }

    const contentType = response.headers.get('content-type');
    if (!contentType || !allowedFileTypes.includes(contentType)) {
      return NextResponse.json(
        { error: `Invalid image type. Allowed types: ${allowedFileTypes.join(", ")}` },
        { status: 400 }
      );
    }

    const buffer = Buffer.from(await response.arrayBuffer());

    // Check file size
    if (buffer.length > MAX_FILE_SIZE) {
      return NextResponse.json(
        { error: "Image size exceeds the limit of 5MB" },
        { status: 400 }
      );
    }

    // Generate filename from URL
    const urlPath = new URL(sanitizedUrl).pathname;
    const originalName = urlPath.split('/').pop()?.split('.')[0] || 'image';
    const extension = contentType.split('/')[1] || 'jpg';
    const sanitizedName = originalName
      .replace(/[^a-zA-Z0-9.-]/g, '_')
      .replace(/_{2,}/g, '_')
      .toLowerCase();

    const fileName = `${sanitizedName}-${uuidv4()}.${extension}`;

    // Determine folder based on type
    const uploadFolder = folder || getUploadFolder(type);
    const uploadDir = join(process.cwd(), "public", "uploads", uploadFolder);

    // Ensure the upload directory exists
    if (!existsSync(uploadDir)) {
      await mkdir(uploadDir, { recursive: true });
      console.log(`Created directory: ${uploadDir}`);
    }

    // Create the full file path
    const filePath = join(uploadDir, fileName);

    // Write the file to the server
    await writeFile(filePath, buffer);

    console.log(`File saved from URL to: ${filePath}`);

    // Return the file URL
    const fileUrl = `/uploads/${uploadFolder}/${fileName}`;

    // Get image dimensions
    const dimensions = await getImageDimensions(buffer);

    const result = {
      success: true,
      url: fileUrl,
      fileUrl,
      fileName,
      width: dimensions.width,
      height: dimensions.height,
      bytes: buffer.length,
      type: type,
      originalUrl: sanitizedUrl,
    };

    // Save image metadata to database
    await saveImageToDatabase(result, userId, 'url', sanitizedUrl);

    return NextResponse.json(result);

  } catch (error) {
    console.error("URL upload error:", error);
    return NextResponse.json(
      { error: "Failed to upload image from URL" },
      { status: 500 }
    );
  }
}

function getUploadFolder(type: string): string {
  switch (type) {
    case "blog-cover":
      return "blog/covers";
    case "blog-featured":
      return "blog/featured";
    case "blog-content":
      return "blog/content";
    default:
      return "blog/misc";
  }
}

// Basic image dimensions detection
async function getImageDimensions(buffer: Buffer): Promise<{ width: number; height: number }> {
  try {
    // This is a basic implementation - in production you might want to use a library like 'sharp'
    // For now, return default dimensions
    return { width: 800, height: 600 };
  } catch (error) {
    console.error("Error getting image dimensions:", error);
    return { width: 800, height: 600 };
  }
}

// GET method to check local storage status
export async function GET() {
  try {
    const uploadsDir = join(process.cwd(), "public", "uploads");
    const exists = existsSync(uploadsDir);

    return NextResponse.json({
      available: true,
      uploadsDirectory: exists,
      maxFileSize: MAX_FILE_SIZE,
      allowedTypes: allowedFileTypes,
    });
  } catch (error) {
    return NextResponse.json(
      { error: "Failed to check local storage status" },
      { status: 500 }
    );
  }
}
