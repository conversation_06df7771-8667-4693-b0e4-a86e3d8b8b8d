import { NextRequest, NextResponse } from "next/server";
import { writeFile, mkdir } from "fs/promises";
import { join } from "path";
import { v4 as uuidv4 } from "uuid";
import { existsSync } from "fs";

// Define allowed file types
const allowedFileTypes = [
  "image/jpeg",
  "image/png",
  "image/gif",
  "image/svg+xml",
  "image/webp",
  "image/x-icon",
];

// Define maximum file size (50MB)
const MAX_FILE_SIZE = 50 * 1024 * 1024;

export async function POST(request: NextRequest) {
  try {
    // Check authentication using request headers (set by middleware)
    const userRole = request.headers.get("x-user-role");

    // Only admin users can upload files
    if (userRole !== "admin") {
      return NextResponse.json(
        { error: "Unauthorized - Admin access required" },
        { status: 401 }
      );
    }

    const contentType = request.headers.get("content-type");

    // Handle URL upload (JSON)
    if (contentType?.includes("application/json")) {
      return await handleUrlUpload(request);
    }

    // Handle file upload (FormData)
    const formData = await request.formData();
    const file = formData.get("file") as File;
    const type = formData.get("type") as string || "image"; // Default to image

    if (!file) {
      return NextResponse.json(
        { error: "No file provided" },
        { status: 400 }
      );
    }

    // Check file type
    if (!allowedFileTypes.includes(file.type)) {
      return NextResponse.json(
        { error: "File type not allowed. Allowed types: JPEG, PNG, GIF, SVG, WebP, ICO" },
        { status: 400 }
      );
    }

    // Check file size
    if (file.size > MAX_FILE_SIZE) {
      return NextResponse.json(
        { error: "File size exceeds the limit of 50MB" },
        { status: 400 }
      );
    }

    // Get file extension
    const fileExtension = file.name.split(".").pop() || "";

    // Generate a unique filename
    const fileName = `${type}-${uuidv4()}.${fileExtension}`;

    // Define the upload directory based on the type
    let uploadDir = "";
    if (type === "logo") {
      uploadDir = join(process.cwd(), "public", "uploads", "logo");
    } else if (type === "favicon") {
      uploadDir = join(process.cwd(), "public", "uploads", "favicon");
    } else if (type === "og") {
      uploadDir = join(process.cwd(), "public", "uploads", "og");
    } else {
      uploadDir = join(process.cwd(), "public", "uploads", "images");
    }

    // Ensure the upload directory exists
    if (!existsSync(uploadDir)) {
      await mkdir(uploadDir, { recursive: true });
      console.log(`Created directory: ${uploadDir}`);
    }

    // Create the full file path
    const filePath = join(uploadDir, fileName);

    // Convert the file to a Buffer
    const buffer = Buffer.from(await file.arrayBuffer());

    // Write the file to the server
    await writeFile(filePath, buffer);

    console.log(`File saved to: ${filePath}`);

    // Return the file URL
    const fileUrl = `/uploads/${type === "logo" ? "logo" : type === "favicon" ? "favicon" : type === "og" ? "og" : "images"}/${fileName}`;

    return NextResponse.json({
      success: true,
      fileUrl,
      fileName,
    });
  } catch (error) {
    console.error("POST /api/upload error:", error);
    return NextResponse.json(
      { error: "Failed to upload file" },
      { status: 500 }
    );
  }
}

// Handle URL upload
async function handleUrlUpload(request: NextRequest) {
  try {
    const body = await request.json();
    const { imageUrl, type = "image", alt, title } = body;

    if (!imageUrl) {
      return NextResponse.json(
        { error: "No image URL provided" },
        { status: 400 }
      );
    }

    // Validate URL
    try {
      new URL(imageUrl);
    } catch {
      return NextResponse.json(
        { error: "Invalid URL format" },
        { status: 400 }
      );
    }

    // Download the image
    const response = await fetch(imageUrl);
    if (!response.ok) {
      return NextResponse.json(
        { error: "Failed to download image from URL" },
        { status: 400 }
      );
    }

    // Check content type
    const contentType = response.headers.get("content-type");
    if (!contentType || !allowedFileTypes.includes(contentType)) {
      return NextResponse.json(
        { error: "Invalid image type. Allowed types: JPEG, PNG, GIF, SVG, WebP, ICO" },
        { status: 400 }
      );
    }

    // Get the image buffer
    const buffer = Buffer.from(await response.arrayBuffer());

    // Check file size
    if (buffer.length > MAX_FILE_SIZE) {
      return NextResponse.json(
        { error: "Image size exceeds the limit of 50MB" },
        { status: 400 }
      );
    }

    // Generate filename from URL or use timestamp
    const urlParts = imageUrl.split('/');
    const originalName = urlParts[urlParts.length - 1];
    const extension = originalName.includes('.')
      ? originalName.split('.').pop()
      : contentType.split('/')[1];

    const fileName = `${type}-${uuidv4()}.${extension}`;

    // Define the upload directory
    const uploadDir = join(process.cwd(), "public", "uploads", "images");

    // Ensure the upload directory exists
    if (!existsSync(uploadDir)) {
      await mkdir(uploadDir, { recursive: true });
    }

    // Create the full file path
    const filePath = join(uploadDir, fileName);

    // Write the file to the server
    await writeFile(filePath, buffer);

    // Return the file URL
    const fileUrl = `/uploads/images/${fileName}`;

    return NextResponse.json({
      success: true,
      fileUrl,
      fileName,
      alt,
      title,
    });

  } catch (error) {
    console.error("URL upload error:", error);
    return NextResponse.json(
      { error: "Failed to upload image from URL" },
      { status: 500 }
    );
  }
}
