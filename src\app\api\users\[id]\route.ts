import { NextRequest, NextResponse } from "next/server";
import connectToDatabase from "@/lib/db";
import User from "@/models/User";
import bcrypt from "bcryptjs";
import mongoose from "mongoose";
import { UserActivity } from "@/models/Analytics";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";

// GET a single user by ID
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check if user is admin or the user themselves using NextAuth session
    const session = await getServerSession(authOptions);

    if (!session || !session.user) {
      return NextResponse.json(
        { error: "Unauthorized - Authentication required" },
        { status: 401 }
      );
    }

    // Regular users can only access their own profile
    if (session.user.role !== "admin" && session.user.id !== params.id) {
      return NextResponse.json(
        { error: "Forbidden" },
        { status: 403 }
      );
    }

    await connectToDatabase();

    // Find user
    const user = await User.findById(params.id).select("-password").lean();

    if (!user) {
      return NextResponse.json(
        { error: "User not found" },
        { status: 404 }
      );
    }

    // Get user activity if admin
    let activity = [];
    if (userRole === "admin") {
      activity = await UserActivity.find({ userId: params.id })
        .sort({ timestamp: -1 })
        .limit(20)
        .lean();
    }

    return NextResponse.json({
      user,
      activity: userRole === "admin" ? activity : undefined,
    });

  } catch (error) {
    console.error(`GET /api/users/${params.id} error:`, error);
    return NextResponse.json(
      { error: "Failed to fetch user" },
      { status: 500 }
    );
  }
}

// PUT to update a user
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check if user is admin or the user themselves using NextAuth session
    const session = await getServerSession(authOptions);

    if (!session || !session.user) {
      return NextResponse.json(
        { error: "Unauthorized - Authentication required" },
        { status: 401 }
      );
    }

    // Regular users can only update their own profile
    // Admins can update any profile
    if (session.user.role !== "admin" && session.user.id !== params.id) {
      return NextResponse.json(
        { error: "Forbidden - You can only update your own profile" },
        { status: 403 }
      );
    }

    await connectToDatabase();

    const body = await request.json();

    // Find user
    const user = await User.findById(params.id);

    if (!user) {
      return NextResponse.json(
        { error: "User not found" },
        { status: 404 }
      );
    }

    // Update fields
    if (body.name) user.name = body.name;

    // Only admins can change roles and can't change protected users' roles
    if (body.role && userRole === "admin" && !user.isProtected) {
      user.role = body.role;
    } else if (body.role && user.isProtected) {
      return NextResponse.json(
        { error: "Cannot change role of a protected user" },
        { status: 403 }
      );
    }

    // Handle email change
    if (body.email && body.email.toLowerCase() !== user.email) {
      // Check if email already exists
      const existingUser = await User.findOne({ email: body.email.toLowerCase() });
      if (existingUser) {
        return NextResponse.json(
          { error: "Email already exists" },
          { status: 409 }
        );
      }

      user.email = body.email.toLowerCase();
    }

    // Handle password change
    if (body.password) {
      const salt = await bcrypt.genSalt(10);
      user.password = await bcrypt.hash(body.password, salt);
    }

    // Save changes
    await user.save();

    // Log activity
    await UserActivity.create({
      userId: userId,
      action: "update_user",
      entityType: "user",
      entityId: params.id,
      details: {
        updatedBy: userId,
        updatedFields: Object.keys(body).filter(key => key !== "password"),
      },
      timestamp: new Date(),
    });

    // Return user without password
    const updatedUser = user.toObject();
    delete updatedUser.password;

    return NextResponse.json(updatedUser);

  } catch (error) {
    console.error(`PUT /api/users/${params.id} error:`, error);
    return NextResponse.json(
      { error: "Failed to update user" },
      { status: 500 }
    );
  }
}

// DELETE a user
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check if user is admin using NextAuth session
    const session = await getServerSession(authOptions);

    if (!session || !session.user || session.user.role !== "admin") {
      return NextResponse.json(
        { error: "Unauthorized - Admin access required" },
        { status: 401 }
      );
    }

    await connectToDatabase();

    // Find user first to check if it's protected
    const user = await User.findById(params.id);

    if (!user) {
      return NextResponse.json(
        { error: "User not found" },
        { status: 404 }
      );
    }

    // Check if user is protected
    if (user.isProtected) {
      return NextResponse.json(
        { error: "Cannot delete a protected user" },
        { status: 403 }
      );
    }

    // Delete the user
    await User.findByIdAndDelete(params.id);

    // Log activity
    await UserActivity.create({
      userId: userId,
      action: "delete_user",
      entityType: "user",
      entityId: params.id,
      details: {
        deletedBy: userId,
        deletedUserEmail: user.email,
      },
      timestamp: new Date(),
    });

    return NextResponse.json({
      success: true,
      message: "User deleted successfully",
    });

  } catch (error) {
    console.error(`DELETE /api/users/${params.id} error:`, error);
    return NextResponse.json(
      { error: "Failed to delete user" },
      { status: 500 }
    );
  }
}
