import { NextRequest, NextResponse } from "next/server";
import connectToDatabase from "@/lib/db";
import User from "@/models/User";
import bcrypt from "bcryptjs";
import mongoose from "mongoose";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";

// GET all users with pagination and search
export async function GET(request: NextRequest) {
  try {
    // Check if user is admin using NextAuth session
    const session = await getServerSession(authOptions);

    if (!session || session.user.role !== "admin") {
      return NextResponse.json(
        { error: "Unauthorized - Admin access required" },
        { status: 401 }
      );
    }

    await connectToDatabase();

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "10");
    const search = searchParams.get("search") || "";
    const role = searchParams.get("role") || "";

    // Build query
    const query: any = {};

    if (search) {
      query.$or = [
        { name: { $regex: search, $options: "i" } },
        { email: { $regex: search, $options: "i" } },
      ];
    }

    if (role) {
      query.role = role;
    }

    // Execute query with pagination
    const [users, total] = await Promise.all([
      User.find(query)
        .select("-password")
        .sort({ createdAt: -1 })
        .skip((page - 1) * limit)
        .limit(limit)
        .lean(),
      User.countDocuments(query),
    ]);

    return NextResponse.json({
      users,
      pagination: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      },
    });

  } catch (error) {
    console.error("GET /api/users error:", error);
    return NextResponse.json(
      { error: "Failed to fetch users" },
      { status: 500 }
    );
  }
}

// POST to create a new user
export async function POST(request: NextRequest) {
  try {
    // Check if user is admin using NextAuth session
    const session = await getServerSession(authOptions);

    if (!session || session.user.role !== "admin") {
      return NextResponse.json(
        { error: "Unauthorized - Admin access required" },
        { status: 401 }
      );
    }

    await connectToDatabase();

    const body = await request.json();

    // Validate required fields
    if (!body.name || !body.email || !body.password) {
      return NextResponse.json(
        { error: "Name, email, and password are required" },
        { status: 400 }
      );
    }

    // Check if email already exists
    const existingUser = await User.findOne({ email: body.email.toLowerCase() });
    if (existingUser) {
      return NextResponse.json(
        { error: "Email already exists" },
        { status: 409 }
      );
    }

    // Hash password
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(body.password, salt);

    // Create user
    const newUser = await User.create({
      name: body.name,
      email: body.email.toLowerCase(),
      password: hashedPassword,
      role: body.role || "user",
    });

    // Return user without password
    const user = newUser.toObject();
    delete user.password;

    return NextResponse.json(user, { status: 201 });

  } catch (error) {
    console.error("POST /api/users error:", error);
    return NextResponse.json(
      { error: "Failed to create user" },
      { status: 500 }
    );
  }
}

// PATCH to update multiple users (batch operations)
export async function PATCH(request: NextRequest) {
  try {
    // Check if user is admin using NextAuth session
    const session = await getServerSession(authOptions);

    if (!session || session.user.role !== "admin") {
      return NextResponse.json(
        { error: "Unauthorized - Admin access required" },
        { status: 401 }
      );
    }

    await connectToDatabase();

    const body = await request.json();

    // Validate request
    if (!body.userIds || !Array.isArray(body.userIds) || body.userIds.length === 0) {
      return NextResponse.json(
        { error: "User IDs are required" },
        { status: 400 }
      );
    }

    // Convert string IDs to ObjectIds
    const userIds = body.userIds.map((id: string) => new mongoose.Types.ObjectId(id));

    // Update operation
    let result;

    if (body.operation === "delete") {
      // Find protected users
      const protectedUsers = await User.find({
        _id: { $in: userIds },
        isProtected: true
      });

      if (protectedUsers.length > 0) {
        // Filter out protected users
        const protectedIds = protectedUsers.map(user => user._id.toString());
        const nonProtectedIds = userIds.filter(id =>
          !protectedIds.includes(id.toString())
        );

        // Delete non-protected users
        result = await User.deleteMany({
          _id: { $in: nonProtectedIds },
          isProtected: { $ne: true }
        });

        return NextResponse.json({
          success: true,
          message: `${result.deletedCount} users deleted. ${protectedUsers.length} protected users were not deleted.`,
          deletedCount: result.deletedCount,
          protectedCount: protectedUsers.length
        });
      } else {
        // No protected users, delete all
        result = await User.deleteMany({ _id: { $in: userIds } });

        return NextResponse.json({
          success: true,
          message: `${result.deletedCount} users deleted`,
          deletedCount: result.deletedCount,
        });
      }
    } else if (body.operation === "updateRole") {
      // Update role
      if (!body.role) {
        return NextResponse.json(
          { error: "Role is required for updateRole operation" },
          { status: 400 }
        );
      }

      // Find protected users
      const protectedUsers = await User.find({
        _id: { $in: userIds },
        isProtected: true
      });

      if (protectedUsers.length > 0) {
        // Filter out protected users
        const protectedIds = protectedUsers.map(user => user._id.toString());
        const nonProtectedIds = userIds.filter(id =>
          !protectedIds.includes(id.toString())
        );

        // Update non-protected users
        result = await User.updateMany(
          {
            _id: { $in: nonProtectedIds },
            isProtected: { $ne: true }
          },
          { $set: { role: body.role } }
        );

        return NextResponse.json({
          success: true,
          message: `${result.modifiedCount} users updated. ${protectedUsers.length} protected users were not modified.`,
          modifiedCount: result.modifiedCount,
          protectedCount: protectedUsers.length
        });
      } else {
        // No protected users, update all
        result = await User.updateMany(
          { _id: { $in: userIds } },
          { $set: { role: body.role } }
        );

        return NextResponse.json({
          success: true,
          message: `${result.modifiedCount} users updated`,
          modifiedCount: result.modifiedCount,
        });
      }
    } else {
      return NextResponse.json(
        { error: "Invalid operation" },
        { status: 400 }
      );
    }

  } catch (error) {
    console.error("PATCH /api/users error:", error);
    return NextResponse.json(
      { error: "Failed to update users" },
      { status: 500 }
    );
  }
}
