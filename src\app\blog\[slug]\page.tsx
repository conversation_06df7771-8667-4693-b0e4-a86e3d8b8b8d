// This is a server component
import { Metadata } from "next";
import { notFound } from "next/navigation";
import Header from "@/components/layout/Header";
import Footer from "@/components/layout/Footer";
import { BlogPostContent } from "@/components/blog/BlogPostContent";
import { fetchBlogPost } from "@/lib/db-server";

import { blogPosts } from "@/data/blog-posts";

interface Props {
  params: Promise<{
    slug: string;
  }>;
}



interface Post {
  _id?: string;
  title: string;
  date: string;
  author: string;
  category: string;
  image: string;
  content: string;
  description?: string;
  imageCredit?: string;
  tags?: string[];
}

async function getBlogPost(slug: string): Promise<Post | null> {
  try {
    // Use server-only database utility to avoid MongoDB client-side issues
    const post = await fetchBlogPost(slug);

    if (!post) {
      // Fallback to static data
      const staticPost = blogPosts[slug as keyof typeof blogPosts];
      return staticPost || null;
    }

    // Format the post data to match BlogPostContent interface
    const blogPost = post as any; // Type assertion for dynamic data
    return {
      _id: blogPost._id,
      title: blogPost.title,
      content: blogPost.content,
      date: blogPost.createdAt ? new Date(blogPost.createdAt).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
      }) : new Date().toLocaleDateString(),
      author: blogPost.author || 'Admin',
      category: blogPost.category || 'General',
      image: blogPost.featuredImage || '/images/blog-placeholder.jpg',
      description: blogPost.description || '',
      imageCredit: blogPost.credit || '',
      tags: blogPost.tags || [],
    };
  } catch (error) {
    console.error("Error fetching blog post:", error);
    // Fallback to static data
    const staticPost = blogPosts[slug as keyof typeof blogPosts];
    return staticPost || null;
  }
}

export async function generateMetadata({ params }: Props): Promise<Metadata> {
  const { slug } = await params;
  const post = await fetchBlogPost(slug);
  if (!post) {
    return {
      title: "Post Not Found - ToolCrush Blog",
      description: "Blog post not found",
    };
  }
  const blogPost = post as any; // Type assertion for dynamic data
  return {
    title: blogPost.title,
    description: blogPost.description || "Read this amazing blog post on ToolCrush",
  };
}

export default async function BlogPostPage({ params }: Props) {
  const { slug } = await params;
  const post = await getBlogPost(slug);

  if (!post) {
    notFound();
  }

  return (
    <div className="min-h-screen flex flex-col bg-background text-foreground">
      <Header />
      <BlogPostContent post={post} slug={slug} />
      <Footer />
    </div>
  );
}
