'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import { PinterestMasonryLayout } from '@/components/blog/PinterestMasonryLayout';
import { But<PERSON> } from '@/components/ui/button';
import { ArrowLeft, Shuffle, Grid3X3, Columns } from 'lucide-react';
import Link from 'next/link';

// Demo blog posts with various categories and content lengths
const demoBlogPosts = [
  {
    id: '1',
    _id: '1',
    title: 'The Future of AI in Web Development: Transforming How We Build Digital Experiences',
    excerpt: 'Explore how artificial intelligence is revolutionizing the way we build and design websites, from automated code generation to intelligent user experiences that adapt in real-time.',
    content: 'Artificial intelligence is fundamentally changing how we approach web development. From automated testing to intelligent design systems, AI tools are becoming indispensable for modern developers.',
    slug: 'future-ai-web-development',
    featuredImage: 'https://images.unsplash.com/photo-1677442136019-21780ecad995?w=800&q=80',
    imageCredit: 'Unsplash',
    category: 'AI',
    publishedAt: '2024-01-15T10:00:00Z',
    author: { name: '<PERSON>', email: '<EMAIL>' }
  },
  {
    id: '2',
    _id: '2',
    title: 'Mastering Modern CSS Grid Layouts',
    excerpt: 'Learn advanced CSS Grid techniques for creating responsive, flexible layouts.',
    content: 'CSS Grid has revolutionized how we create layouts on the web.',
    slug: 'mastering-css-grid-layouts',
    featuredImage: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=800&q=80',
    imageCredit: 'Unsplash',
    category: 'Design',
    publishedAt: '2024-01-14T14:30:00Z',
    author: { name: 'Sarah Johnson', email: '<EMAIL>' }
  },
  {
    id: '3',
    _id: '3',
    title: 'Building Scalable React Applications with TypeScript',
    excerpt: 'A comprehensive guide to building large-scale React applications using TypeScript for better type safety and developer experience.',
    content: 'TypeScript brings static typing to JavaScript, making it easier to build and maintain large React applications.',
    slug: 'scalable-react-typescript',
    featuredImage: 'https://images.unsplash.com/photo-1633356122544-f134324a6cee?w=800&q=80',
    imageCredit: 'Unsplash',
    category: 'Development',
    publishedAt: '2024-01-13T09:15:00Z',
    author: { name: 'Mike Rodriguez', email: '<EMAIL>' }
  },
  {
    id: '4',
    _id: '4',
    title: 'The Psychology of User Interface Design',
    excerpt: 'Understanding how users interact with interfaces and designing for better user experiences.',
    content: 'Great UI design is rooted in understanding human psychology and behavior patterns.',
    slug: 'psychology-ui-design',
    featuredImage: 'https://images.unsplash.com/photo-1586717791821-3f44a563fa4c?w=800&q=80',
    imageCredit: 'Unsplash',
    category: 'Design',
    publishedAt: '2024-01-12T16:45:00Z',
    author: { name: 'Emma Wilson', email: '<EMAIL>' }
  },
  {
    id: '5',
    _id: '5',
    title: 'Cryptocurrency Investment Strategies for 2024',
    excerpt: 'Navigate the volatile crypto market with proven investment strategies and risk management techniques.',
    content: 'The cryptocurrency market continues to evolve, presenting both opportunities and challenges for investors.',
    slug: 'crypto-investment-strategies-2024',
    featuredImage: 'https://images.unsplash.com/photo-1639762681485-074b7f938ba0?w=800&q=80',
    imageCredit: 'Unsplash',
    category: 'Finance',
    publishedAt: '2024-01-11T11:20:00Z',
    author: { name: 'David Kim', email: '<EMAIL>' }
  },
  {
    id: '6',
    _id: '6',
    title: 'Healthy Meal Prep Ideas for Busy Professionals',
    excerpt: 'Quick and nutritious meal prep recipes that fit into your busy schedule.',
    content: 'Eating healthy doesn\'t have to be time-consuming. These meal prep ideas will save you time and keep you nourished.',
    slug: 'healthy-meal-prep-busy-professionals',
    featuredImage: 'https://images.unsplash.com/photo-1490645935967-10de6ba17061?w=800&q=80',
    imageCredit: 'Unsplash',
    category: 'Health',
    publishedAt: '2024-01-10T08:30:00Z',
    author: { name: 'Lisa Chen', email: '<EMAIL>' }
  },
  {
    id: '7',
    _id: '7',
    title: 'Electric Vehicle Revolution: What to Expect in 2024',
    excerpt: 'The automotive industry is rapidly shifting towards electric vehicles. Here\'s what consumers and investors need to know.',
    content: 'Electric vehicles are becoming mainstream, with major automakers committing to full electrification.',
    slug: 'electric-vehicle-revolution-2024',
    featuredImage: 'https://images.unsplash.com/photo-**********-bd32c8ce0db2?w=800&q=80',
    imageCredit: 'Unsplash',
    category: 'Automotive',
    publishedAt: '2024-01-09T13:00:00Z',
    author: { name: 'James Park', email: '<EMAIL>' }
  },
  {
    id: '8',
    _id: '8',
    title: 'Travel Photography Tips for Stunning Vacation Photos',
    excerpt: 'Capture breathtaking memories with these professional photography techniques for travelers.',
    content: 'Great travel photography is about more than just having a good camera. It\'s about seeing the world through a creative lens.',
    slug: 'travel-photography-tips',
    featuredImage: 'https://images.unsplash.com/photo-1488646953014-85cb44e25828?w=800&q=80',
    imageCredit: 'Unsplash',
    category: 'Photography',
    publishedAt: '2024-01-08T15:45:00Z',
    author: { name: 'Maria Garcia', email: '<EMAIL>' }
  },
  {
    id: '9',
    _id: '9',
    title: 'The Rise of Remote Work: Tools and Best Practices',
    excerpt: 'Essential tools and strategies for successful remote work in the modern business landscape.',
    content: 'Remote work has become the new normal. Here are the tools and practices that make it successful.',
    slug: 'rise-remote-work-tools-practices',
    featuredImage: 'https://images.unsplash.com/photo-1521737604893-d14cc237f11d?w=800&q=80',
    imageCredit: 'Unsplash',
    category: 'Business',
    publishedAt: '2024-01-07T10:15:00Z',
    author: { name: 'Robert Taylor', email: '<EMAIL>' }
  },
  {
    id: '10',
    _id: '10',
    title: 'Gaming Industry Trends: What\'s Next for Gamers',
    excerpt: 'From VR to cloud gaming, explore the latest trends shaping the future of gaming.',
    content: 'The gaming industry continues to innovate with new technologies and platforms.',
    slug: 'gaming-industry-trends-future',
    featuredImage: 'https://images.unsplash.com/photo-1511512578047-dfb367046420?w=800&q=80',
    imageCredit: 'Unsplash',
    category: 'Gaming',
    publishedAt: '2024-01-06T19:30:00Z',
    author: { name: 'Kevin Wu', email: '<EMAIL>' }
  },
  {
    id: '11',
    _id: '11',
    title: 'Sustainable Living: Small Changes, Big Impact',
    excerpt: 'Simple lifestyle changes that can make a significant difference for the environment.',
    content: 'Living sustainably doesn\'t require drastic changes. Small, consistent actions can have a big impact.',
    slug: 'sustainable-living-small-changes',
    featuredImage: 'https://images.unsplash.com/photo-1542601906990-b4d3fb778b09?w=800&q=80',
    imageCredit: 'Unsplash',
    category: 'Lifestyle',
    publishedAt: '2024-01-05T12:00:00Z',
    author: { name: 'Anna Thompson', email: '<EMAIL>' }
  },
  {
    id: '12',
    _id: '12',
    title: 'Machine Learning Fundamentals for Beginners',
    excerpt: 'A beginner-friendly introduction to machine learning concepts and applications.',
    content: 'Machine learning is transforming industries. Here\'s how to get started with the fundamentals.',
    slug: 'machine-learning-fundamentals-beginners',
    featuredImage: 'https://images.unsplash.com/photo-1555949963-aa79dcee981c?w=800&q=80',
    imageCredit: 'Unsplash',
    category: 'AI',
    publishedAt: '2024-01-04T14:20:00Z',
    author: { name: 'Dr. Jennifer Lee', email: '<EMAIL>' }
  }
];

export default function PinterestDemoPage() {
  const [posts, setPosts] = useState(demoBlogPosts);
  const [loading, setLoading] = useState(false);

  const shufflePosts = () => {
    setLoading(true);
    setTimeout(() => {
      const shuffled = [...posts].sort(() => Math.random() - 0.5);
      setPosts(shuffled);
      setLoading(false);
    }, 500);
  };

  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      {/* Hero Section */}
      <motion.section
        className="py-16 px-4 bg-gradient-to-br from-primary/5 via-background to-secondary/5"
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <div className="max-w-7xl mx-auto">
          {/* Back Button */}
          <motion.div
            className="mb-8"
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.1, duration: 0.5 }}
          >
            <Link href="/blog">
              <Button
                variant="outline"
                className="flex items-center gap-2 rounded-full px-6 py-3 transition-all duration-300 hover:scale-105"
              >
                <ArrowLeft className="h-4 w-4" />
                Back to Blog
              </Button>
            </Link>
          </motion.div>

          <div className="text-center">
            <motion.h1
              className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2, duration: 0.6 }}
            >
              📌 Pinterest-Style Blog Layout
            </motion.h1>

            <motion.p
              className="text-xl md:text-2xl mb-8 text-muted-foreground"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3, duration: 0.6 }}
            >
              Modern masonry grid with dynamic card heights and smooth animations
            </motion.p>

            {/* Demo Controls */}
            <motion.div
              className="flex flex-wrap justify-center gap-4 mb-12"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4, duration: 0.6 }}
            >
              <Button
                onClick={shufflePosts}
                disabled={loading}
                className="flex items-center gap-2 rounded-full px-6 py-3"
              >
                <Shuffle className="h-4 w-4" />
                {loading ? 'Shuffling...' : 'Shuffle Posts'}
              </Button>
              
              <div className="flex items-center gap-2 px-4 py-2 rounded-full bg-muted text-muted-foreground">
                <Grid3X3 className="h-4 w-4" />
                <span className="text-sm font-medium">Responsive Masonry</span>
              </div>
              
              <div className="flex items-center gap-2 px-4 py-2 rounded-full bg-muted text-muted-foreground">
                <Columns className="h-4 w-4" />
                <span className="text-sm font-medium">1-4 Columns</span>
              </div>
            </motion.div>

            {/* Features List */}
            <motion.div
              className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto mb-12"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.5, duration: 0.6 }}
            >
              <div className="text-center p-6 rounded-2xl bg-card border border-border">
                <div className="w-12 h-12 mx-auto mb-4 rounded-full bg-primary/10 flex items-center justify-center">
                  <Grid3X3 className="h-6 w-6 text-primary" />
                </div>
                <h3 className="font-semibold mb-2">True Masonry Layout</h3>
                <p className="text-sm text-muted-foreground">CSS columns for natural card flow and balanced heights</p>
              </div>
              
              <div className="text-center p-6 rounded-2xl bg-card border border-border">
                <div className="w-12 h-12 mx-auto mb-4 rounded-full bg-secondary/10 flex items-center justify-center">
                  <Shuffle className="h-6 w-6 text-secondary" />
                </div>
                <h3 className="font-semibold mb-2">Smooth Animations</h3>
                <p className="text-sm text-muted-foreground">Framer Motion powered entrance and hover effects</p>
              </div>
              
              <div className="text-center p-6 rounded-2xl bg-card border border-border">
                <div className="w-12 h-12 mx-auto mb-4 rounded-full bg-accent/10 flex items-center justify-center">
                  <Columns className="h-6 w-6 text-accent-foreground" />
                </div>
                <h3 className="font-semibold mb-2">Responsive Design</h3>
                <p className="text-sm text-muted-foreground">Adapts from 1 column on mobile to 4 on desktop</p>
              </div>
            </motion.div>
          </div>
        </div>
      </motion.section>

      {/* Pinterest Masonry Layout Demo */}
      <section className="py-16 px-4">
        <div className="max-w-7xl mx-auto">
          <PinterestMasonryLayout
            posts={posts}
            loading={loading}
            showAnimation={true}
            className="pinterest-demo-layout"
          />
        </div>
      </section>

      <Footer />
    </div>
  );
}
