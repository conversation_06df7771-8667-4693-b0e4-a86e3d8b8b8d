import { notFound } from "next/navigation";
import { Suspense } from "react";
import { Metadata } from "next";
import { ALL_CALCULATORS } from "@/data/calculators";
import ImmersiveCalculatorLayout from "@/components/calculators/ImmersiveCalculatorLayout";
import CalculatorDialog from "@/components/calculators/CalculatorDialog";
import ImmersiveCalculatorSkeleton from "@/components/calculators/ImmersiveCalculatorSkeleton";
import { generateRouteMetadata, isValidCalculatorRoute } from "@/lib/routing";

interface CalculatorPageProps {
  params: Promise<{ slug: string }>;
}

// Generate static paths for all calculators - optimized for performance
export async function generateStaticParams() {
  // Only generate for calculators that exist in our data
  return ALL_CALCULATORS.map((calculator) => ({
    slug: calculator.id,
  }));
}

// Static generation with ISR for better performance
export const dynamic = 'force-static';
export const revalidate = 3600; // Revalidate every hour

// Generate metadata for SEO
export async function generateMetadata({ params }: CalculatorPageProps): Promise<Metadata> {
  const { slug } = await params;

  // Validate route first
  if (!isValidCalculatorRoute(slug)) {
    return {
      title: "Calculator Not Found - ToolCrush",
      description: "The requested calculator could not be found.",
    };
  }

  // Use centralized metadata generation
  const baseMetadata = generateRouteMetadata(`/calculators/${slug}`);

  // Get calculator-specific data for enhanced metadata
  const calculator = ALL_CALCULATORS.find(calc => calc.id === slug);

  return {
    ...baseMetadata,
    // Add calculator-specific metadata
    alternates: {
      canonical: `/calculators/${slug}`,
    },
    robots: {
      index: true,
      follow: true,
    },
    // Add structured data for calculators
    other: {
      'calculator-category': calculator?.category || 'general',
      'calculator-popular': calculator?.popular ? 'true' : 'false',
    },
  };
}

export default async function CalculatorPage({ params }: CalculatorPageProps) {
  const { slug } = await params;

  // Validate route using centralized validation
  if (!isValidCalculatorRoute(slug)) {
    notFound();
  }

  const calculator = ALL_CALCULATORS.find(calc => calc.id === slug);

  // Double-check that we have calculator data
  if (!calculator) {
    notFound();
  }

  return (
    <ImmersiveCalculatorLayout
      title={calculator.title}
      description={calculator.description}
      iconName={calculator.icon}
      category={calculator.category}
      popular={calculator.popular}
    >
      <Suspense fallback={<ImmersiveCalculatorSkeleton />}>
        <CalculatorDialog calculator={calculator} isModal={false} />
      </Suspense>
    </ImmersiveCalculatorLayout>
  );
}
