"use client";

import { useEffect } from "react";
import { signOut } from "next-auth/react";
import { useRouter } from "next/navigation";
import { motion } from "framer-motion";
import { Loader2 } from "lucide-react";

export default function LogoutPage() {
  const router = useRouter();

  useEffect(() => {
    const performLogout = async () => {
      try {
        await signOut({ redirect: false });
        // Redirect to home page after logout
        router.push("/");
      } catch (error) {
        console.error("Logout error:", error);
        // Redirect to home page even if there's an error
        router.push("/");
      }
    };

    performLogout();
  }, [router]);

  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-gray-50 dark:bg-gray-900">
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        className="text-center"
      >
        <Loader2 className="h-12 w-12 animate-spin mx-auto mb-4 text-blue-600" />
        <h1 className="text-2xl font-bold mb-2 dark:text-white">Logging out...</h1>
        <p className="text-gray-500 dark:text-gray-400">
          You will be redirected to the home page shortly.
        </p>
      </motion.div>
    </div>
  );
}
