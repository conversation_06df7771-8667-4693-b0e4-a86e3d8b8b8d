"use client";

import { ThemeProvider } from "@/context/theme-context";
import { ColorThemeProvider } from "@/hooks/use-color-theme";
import { SessionProvider } from "@/components/providers/SessionProvider";
import { Provider as ReduxProvider } from "react-redux";
import { store } from "@/redux/store";
import { ColorSchemeInitializer } from "@/components/theme/ColorSchemeInitializer";
import { LoadingProvider } from "@/contexts/LoadingContext";

export function Providers({ children }: { children: React.ReactNode }) {
  return (
    <ReduxProvider store={store}>
      <SessionProvider>
        <ThemeProvider>
          <ColorThemeProvider>
            <LoadingProvider>
              <ColorSchemeInitializer />
              {children}
            </LoadingProvider>
          </ColorThemeProvider>
        </ThemeProvider>
      </SessionProvider>
    </ReduxProvider>
  );
}
