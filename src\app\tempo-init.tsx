"use client";
import { useEffect } from "react";
import { TempoDevtools } from "@/lib/tempo-mock";

export function TempoInit() {
  useEffect(() => {
    const init = async () => {
      try {
        if (typeof window !== 'undefined' && process.env.NEXT_PUBLIC_TEMPO) {
          TempoDevtools.init();
        }
      } catch (error) {
        console.error("Failed to initialize Tempo:", error);
      }
    };

    init();

    // No dependencies needed as this should only run once on mount
  }, []);

  return null;
}
