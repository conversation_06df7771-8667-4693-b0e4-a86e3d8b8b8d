'use client';

import { useState } from 'react';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { useTheme } from '@/hooks/useTheme';
import { ALL_CALCULATORS } from '@/data/calculators';
import { ALL_TOOLS } from '@/data/tools';
import { blogPosts } from '@/data/blog-posts';
import UnifiedCard from '@/components/ui/UnifiedCard';

export default function TestUnifiedRoutingPage() {
  const { theme } = useTheme();
  const [activeTab, setActiveTab] = useState<'calculators' | 'tools' | 'blogs'>('calculators');

  // Sample data for testing
  const sampleCalculators = ALL_CALCULATORS.slice(0, 4);
  const sampleTools = ALL_TOOLS.slice(0, 4);
  const sampleBlogs = Object.entries(blogPosts).slice(0, 4).map(([id, post]) => ({
    id,
    ...post,
  }));

  const tabs = [
    { id: 'calculators', label: 'Calculators', count: sampleCalculators.length },
    { id: 'tools', label: 'Tools', count: sampleTools.length },
    { id: 'blogs', label: 'Blogs', count: sampleBlogs.length },
  ];

  const renderContent = () => {
    switch (activeTab) {
      case 'calculators':
        return (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {sampleCalculators.map((calc, index) => (
              <UnifiedCard
                key={calc.id}
                id={calc.id}
                title={calc.title}
                description={calc.description}
                icon={calc.icon}
                type="calculator"
                category={calc.category}
                popular={calc.popular}
                comingSoon={calc.comingSoon}
                index={index}
                variant="enhanced"
              />
            ))}
          </div>
        );

      case 'tools':
        return (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {sampleTools.map((tool, index) => (
              <UnifiedCard
                key={tool.id}
                id={tool.id}
                title={tool.title}
                description={tool.description}
                icon={tool.icon}
                type="tool"
                category={tool.category}
                inputFormat={tool.inputFormat}
                outputFormat={tool.outputFormat}
                popular={tool.popular}
                comingSoon={!tool.hasConfig}
                index={index}
                variant="enhanced"
              />
            ))}
          </div>
        );

      case 'blogs':
        return (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {sampleBlogs.map((blog, index) => (
              <UnifiedCard
                key={blog.id}
                id={blog.id}
                title={blog.title}
                description={blog.description || 'Read this article to learn more.'}
                icon="file-text"
                type="blog"
                category={blog.category}
                author={blog.author}
                date={blog.date}
                image={blog.image}
                index={index}
                variant="enhanced"
              />
            ))}
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className={`min-h-screen ${
      theme === 'dark' 
        ? 'bg-gradient-to-br from-gray-900 to-slate-900 text-white' 
        : 'bg-gradient-to-br from-gray-50 to-slate-50 text-gray-900'
    }`}>
      <div className="container mx-auto px-4 py-12">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-12"
        >
          <h1 className="text-4xl font-bold mb-4">
            🧪 Unified Routing System Test
          </h1>
          <p className={`text-lg ${theme === 'dark' ? 'text-gray-300' : 'text-gray-600'}`}>
            Test the new unified dynamic routing system for calculators, tools, and blogs
          </p>
          <div className="mt-6 p-4 rounded-lg bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800">
            <p className="text-sm text-blue-700 dark:text-blue-300">
              <strong>Routes:</strong> /calculators/[slug], /tools/[slug], /blogs/[slug] → /[type]/[slug]
            </p>
          </div>
        </motion.div>

        {/* Tabs */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="flex justify-center mb-8"
        >
          <div className={`flex rounded-lg p-1 ${
            theme === 'dark' ? 'bg-gray-800' : 'bg-gray-100'
          }`}>
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`px-6 py-3 rounded-md font-medium transition-all duration-200 ${
                  activeTab === tab.id
                    ? theme === 'dark'
                      ? 'bg-blue-600 text-white shadow-lg'
                      : 'bg-white text-blue-600 shadow-lg'
                    : theme === 'dark'
                    ? 'text-gray-300 hover:text-white hover:bg-gray-700'
                    : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                }`}
              >
                {tab.label}
                <span className={`ml-2 px-2 py-1 rounded-full text-xs ${
                  activeTab === tab.id
                    ? 'bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-200'
                    : 'bg-gray-200 text-gray-600 dark:bg-gray-600 dark:text-gray-300'
                }`}>
                  {tab.count}
                </span>
              </button>
            ))}
          </div>
        </motion.div>

        {/* Content */}
        <motion.div
          key={activeTab}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          {renderContent()}
        </motion.div>

        {/* Test Links */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          className="mt-12 p-6 rounded-lg bg-gray-50 dark:bg-gray-800 border border-gray-200 dark:border-gray-700"
        >
          <h3 className="text-lg font-semibold mb-4">🔗 Test Direct Links</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div>
              <h4 className="font-medium mb-2 text-yellow-600 dark:text-yellow-400">Calculators</h4>
              <ul className="space-y-1">
                <li><Link href="/calculators/mortgage-calculator" className="text-blue-600 dark:text-blue-400 hover:underline">/calculators/mortgage-calculator</Link></li>
                <li><Link href="/calculators/bmi-calculator" className="text-blue-600 dark:text-blue-400 hover:underline">/calculators/bmi-calculator</Link></li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium mb-2 text-blue-600 dark:text-blue-400">Tools</h4>
              <ul className="space-y-1">
                <li><Link href="/tools/compress-pdf" className="text-blue-600 dark:text-blue-400 hover:underline">/tools/compress-pdf</Link></li>
                <li><Link href="/tools/merge-pdf" className="text-blue-600 dark:text-blue-400 hover:underline">/tools/merge-pdf</Link></li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium mb-2 text-green-600 dark:text-green-400">Blogs</h4>
              <ul className="space-y-1">
                <li><Link href="/blogs/pdf-management-tips" className="text-blue-600 dark:text-blue-400 hover:underline">/blogs/pdf-management-tips</Link></li>
                <li><Link href="/blogs/word-to-pdf-benefits" className="text-blue-600 dark:text-blue-400 hover:underline">/blogs/word-to-pdf-benefits</Link></li>
              </ul>
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  );
}
