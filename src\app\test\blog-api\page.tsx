"use client";

import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { CheckCircle, XCircle, AlertCircle } from "lucide-react";

interface BlogPost {
  id: string;
  title: string;
  slug: string;
  excerpt?: string;
  description?: string;
  featuredImage?: string;
  image?: string;
  publishedAt: string;
  author: {
    name: string;
    email: string;
  } | string;
  category?: string;
  imageCredit?: string;
}

export default function BlogApiTestPage() {
  const [recentPosts, setRecentPosts] = useState<BlogPost[]>([]);
  const [blogPosts, setBlogPosts] = useState<BlogPost[]>([]);
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<string[]>([]);

  const testRecentPostsApi = async () => {
    try {
      setLoading(true);
      setErrors([]);
      
      const response = await fetch('/api/blog/recent?limit=3');
      const data = await response.json();
      
      if (data.success) {
        setRecentPosts(data.data);
        console.log('Recent posts API response:', data);
      } else {
        setErrors(prev => [...prev, 'Recent posts API failed']);
      }
    } catch (error) {
      console.error('Recent posts API error:', error);
      setErrors(prev => [...prev, `Recent posts API error: ${error}`]);
    } finally {
      setLoading(false);
    }
  };

  const testBlogPostsApi = async () => {
    try {
      setLoading(true);
      setErrors([]);
      
      const response = await fetch('/api/blog?page=1&limit=6&status=published');
      const data = await response.json();
      
      if (data.success) {
        setBlogPosts(data.data);
        console.log('Blog posts API response:', data);
      } else {
        setErrors(prev => [...prev, 'Blog posts API failed']);
      }
    } catch (error) {
      console.error('Blog posts API error:', error);
      setErrors(prev => [...prev, `Blog posts API error: ${error}`]);
    } finally {
      setLoading(false);
    }
  };

  const validatePost = (post: BlogPost) => {
    const issues = [];
    if (!post.title) issues.push('Missing title');
    if (!post.slug) issues.push('Missing slug');
    if (!post.author || (typeof post.author === 'object' && !post.author.name)) issues.push('Missing author');
    if (!post.category) issues.push('Missing category');
    if (!post.featuredImage && !post.image) issues.push('Missing image');
    if (!post.publishedAt) issues.push('Missing publishedAt');
    return issues;
  };

  useEffect(() => {
    testRecentPostsApi();
    testBlogPostsApi();
  }, []);

  return (
    <div className="container mx-auto p-6 max-w-7xl">
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CheckCircle className="h-6 w-6 text-green-600" />
            Blog API Test Suite
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex gap-4 mb-6">
            <Button onClick={testRecentPostsApi} disabled={loading}>
              Test Recent Posts API
            </Button>
            <Button onClick={testBlogPostsApi} disabled={loading}>
              Test Blog Posts API
            </Button>
          </div>

          {errors.length > 0 && (
            <div className="mb-6">
              <h3 className="text-lg font-semibold text-red-600 mb-2">Errors:</h3>
              {errors.map((error, index) => (
                <div key={index} className="flex items-center gap-2 text-red-600">
                  <XCircle className="h-4 w-4" />
                  <span>{error}</span>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Recent Posts Test */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Recent Posts API Test ({recentPosts.length} posts)</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {recentPosts.map((post) => {
              const issues = validatePost(post);
              return (
                <div key={post.id} className="border rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-2">
                    {issues.length === 0 ? (
                      <CheckCircle className="h-4 w-4 text-green-600" />
                    ) : (
                      <AlertCircle className="h-4 w-4 text-yellow-600" />
                    )}
                    <h4 className="font-semibold truncate">{post.title || 'No Title'}</h4>
                  </div>
                  
                  <div className="space-y-1 text-sm">
                    <div>Author: {typeof post.author === 'string' ? post.author : post.author?.name || 'N/A'}</div>
                    <div>Category: {post.category || 'N/A'}</div>
                    <div>Image: {post.featuredImage || post.image ? '✓' : '✗'}</div>
                    <div>Slug: {post.slug || 'N/A'}</div>
                  </div>

                  {issues.length > 0 && (
                    <div className="mt-2">
                      <Badge variant="destructive" className="text-xs">
                        {issues.length} issue(s)
                      </Badge>
                      <ul className="text-xs text-red-600 mt-1">
                        {issues.map((issue, i) => (
                          <li key={i}>• {issue}</li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Blog Posts Test */}
      <Card>
        <CardHeader>
          <CardTitle>Blog Posts API Test ({blogPosts.length} posts)</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {blogPosts.map((post) => {
              const issues = validatePost(post);
              return (
                <div key={post.id} className="border rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-2">
                    {issues.length === 0 ? (
                      <CheckCircle className="h-4 w-4 text-green-600" />
                    ) : (
                      <AlertCircle className="h-4 w-4 text-yellow-600" />
                    )}
                    <h4 className="font-semibold truncate">{post.title || 'No Title'}</h4>
                  </div>
                  
                  <div className="space-y-1 text-sm">
                    <div>Author: {typeof post.author === 'string' ? post.author : post.author?.name || 'N/A'}</div>
                    <div>Category: {post.category || 'N/A'}</div>
                    <div>Image: {post.featuredImage || post.image ? '✓' : '✗'}</div>
                    <div>Slug: {post.slug || 'N/A'}</div>
                  </div>

                  {issues.length > 0 && (
                    <div className="mt-2">
                      <Badge variant="destructive" className="text-xs">
                        {issues.length} issue(s)
                      </Badge>
                      <ul className="text-xs text-red-600 mt-1">
                        {issues.map((issue, i) => (
                          <li key={i}>• {issue}</li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
