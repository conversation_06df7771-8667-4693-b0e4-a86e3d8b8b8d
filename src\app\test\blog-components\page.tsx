'use client';

import { useState, useEffect } from 'react';
import { BlogPostList } from '@/components/admin/BlogPostList';
import { BlogList } from '@/components/blog/BlogList';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { CheckCircle, XCircle, AlertTriangle } from 'lucide-react';

export default function BlogComponentsTestPage() {
  const [testResults, setTestResults] = useState<{
    adminList: 'loading' | 'success' | 'error';
    publicList: 'loading' | 'success' | 'error';
    errors: string[];
  }>({
    adminList: 'loading',
    publicList: 'loading',
    errors: []
  });

  // Test API endpoints
  const testApiEndpoints = async () => {
    const results = { ...testResults, errors: [] };
    
    try {
      // Test admin posts API
      const adminResponse = await fetch('/api/posts?admin=true&limit=5');
      const adminData = await adminResponse.json();
      
      if (adminData.success) {
        results.adminList = 'success';
      } else {
        results.adminList = 'error';
        results.errors.push(`Admin API error: ${adminData.error}`);
      }
    } catch (error) {
      results.adminList = 'error';
      results.errors.push(`Admin API fetch error: ${error}`);
    }

    try {
      // Test public posts API
      const publicResponse = await fetch('/api/posts?status=published&limit=5');
      const publicData = await publicResponse.json();
      
      if (publicData.success) {
        results.publicList = 'success';
      } else {
        results.publicList = 'error';
        results.errors.push(`Public API error: ${publicData.error}`);
      }
    } catch (error) {
      results.publicList = 'error';
      results.errors.push(`Public API fetch error: ${error}`);
    }

    setTestResults(results);
  };

  useEffect(() => {
    testApiEndpoints();
  }, []);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'error':
        return <XCircle className="h-4 w-4 text-red-600" />;
      case 'loading':
        return <AlertTriangle className="h-4 w-4 text-yellow-600" />;
      default:
        return null;
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'success':
        return <Badge className="bg-green-100 text-green-800">Working</Badge>;
      case 'error':
        return <Badge variant="destructive">Error</Badge>;
      case 'loading':
        return <Badge variant="outline">Testing...</Badge>;
      default:
        return null;
    }
  };

  return (
    <div className="container mx-auto p-6 max-w-7xl">
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CheckCircle className="h-6 w-6 text-green-600" />
            Blog Components Test Suite
          </CardTitle>
          <CardDescription>
            Test page to verify all blog components handle undefined data gracefully and don't crash with TypeErrors.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
            <div className="space-y-2">
              <h3 className="font-semibold">Test Status</h3>
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="flex items-center gap-2">
                    {getStatusIcon(testResults.adminList)}
                    Admin Blog List
                  </span>
                  {getStatusBadge(testResults.adminList)}
                </div>
                <div className="flex items-center justify-between">
                  <span className="flex items-center gap-2">
                    {getStatusIcon(testResults.publicList)}
                    Public Blog List
                  </span>
                  {getStatusBadge(testResults.publicList)}
                </div>
              </div>
            </div>
            
            <div className="space-y-2">
              <h3 className="font-semibold">Fixes Applied</h3>
              <ul className="text-sm space-y-1">
                <li className="flex items-center gap-2">
                  <CheckCircle className="h-3 w-3 text-green-600" />
                  Fixed .slice() on undefined categories
                </li>
                <li className="flex items-center gap-2">
                  <CheckCircle className="h-3 w-3 text-green-600" />
                  Added defensive array checks
                </li>
                <li className="flex items-center gap-2">
                  <CheckCircle className="h-3 w-3 text-green-600" />
                  Enhanced error handling
                </li>
                <li className="flex items-center gap-2">
                  <CheckCircle className="h-3 w-3 text-green-600" />
                  Data normalization
                </li>
              </ul>
            </div>
          </div>

          {testResults.errors.length > 0 && (
            <Alert variant="destructive" className="mb-4">
              <XCircle className="h-4 w-4" />
              <AlertDescription>
                <div className="space-y-1">
                  <p className="font-semibold">Errors detected:</p>
                  {testResults.errors.map((error, index) => (
                    <p key={index} className="text-sm">• {error}</p>
                  ))}
                </div>
              </AlertDescription>
            </Alert>
          )}

          <Button onClick={testApiEndpoints} className="mb-4">
            Re-run API Tests
          </Button>
        </CardContent>
      </Card>

      <Tabs defaultValue="admin" className="space-y-4">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="admin">Admin Blog List</TabsTrigger>
          <TabsTrigger value="public">Public Blog List</TabsTrigger>
        </TabsList>
        
        <TabsContent value="admin" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Admin Blog Post List</CardTitle>
              <CardDescription>
                Tests the BlogPostList component with admin features. Should handle undefined categories gracefully.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <BlogPostList isAdmin={true} />
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="public" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Public Blog List</CardTitle>
              <CardDescription>
                Tests the BlogList component for public viewing. Should handle all edge cases without errors.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <BlogList 
                showSearch={true}
                showFilters={true}
                limit={10}
                layout="grid"
              />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      <Card className="mt-6">
        <CardHeader>
          <CardTitle>Test Instructions</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-3">
              <h4 className="font-semibold">What to Test:</h4>
              <ul className="text-sm space-y-1 list-disc list-inside">
                <li>Components load without TypeErrors</li>
                <li>Category display works with undefined data</li>
                <li>Empty states display properly</li>
                <li>Loading states work correctly</li>
                <li>Error states are handled gracefully</li>
              </ul>
            </div>
            
            <div className="space-y-3">
              <h4 className="font-semibold">Expected Behavior:</h4>
              <ul className="text-sm space-y-1 list-disc list-inside">
                <li>No console errors or crashes</li>
                <li>Categories show "No category" when undefined</li>
                <li>Arrays default to empty arrays when undefined</li>
                <li>Proper fallback values for all fields</li>
                <li>Smooth user experience</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
