'use client';

import { useState } from 'react';
import { CategoryDropdown } from '@/components/blog/CategoryDropdown';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

export default function CategoryDropdownTestPage() {
  const [selectedCategoryId, setSelectedCategoryId] = useState('');

  return (
    <div className="container mx-auto p-6 max-w-4xl">
      <Card>
        <CardHeader>
          <CardTitle>Category Dropdown Test</CardTitle>
          <CardDescription>
            Test the enhanced CategoryDropdown component with improved error handling
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Test Scenarios</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div className="space-y-2">
                <h4 className="font-medium text-green-600">✅ Should Work:</h4>
                <ul className="list-disc list-inside space-y-1 text-muted-foreground">
                  <li>Create new unique category</li>
                  <li>Select existing category</li>
                  <li>Auto-select when duplicate detected</li>
                  <li>Show warning for potential duplicates</li>
                </ul>
              </div>
              <div className="space-y-2">
                <h4 className="font-medium text-blue-600">🔧 Error Handling:</h4>
                <ul className="list-disc list-inside space-y-1 text-muted-foreground">
                  <li>Graceful handling of duplicate categories</li>
                  <li>User-friendly error messages</li>
                  <li>No application crashes</li>
                  <li>Proper validation feedback</li>
                </ul>
              </div>
            </div>
          </div>

          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Category Dropdown</h3>
            <CategoryDropdown
              selectedCategoryId={selectedCategoryId}
              onCategoryChange={setSelectedCategoryId}
              placeholder="Select or create a category"
              className="max-w-md"
            />
          </div>

          {selectedCategoryId && (
            <div className="space-y-2">
              <h4 className="font-medium">Selected Category:</h4>
              <Badge variant="secondary">{selectedCategoryId}</Badge>
            </div>
          )}

          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Test Instructions</h3>
            <div className="space-y-3 text-sm">
              <div className="p-4 bg-muted rounded-lg">
                <h4 className="font-medium mb-2">1. Test Duplicate Detection:</h4>
                <p>Try creating a category with the same name as an existing one (case-insensitive). 
                   You should see a warning and the option to select the existing category.</p>
              </div>
              
              <div className="p-4 bg-muted rounded-lg">
                <h4 className="font-medium mb-2">2. Test Error Handling:</h4>
                <p>The component should gracefully handle server errors and provide helpful feedback 
                   instead of crashing the application.</p>
              </div>
              
              <div className="p-4 bg-muted rounded-lg">
                <h4 className="font-medium mb-2">3. Test Validation:</h4>
                <p>Try creating categories with empty names, very short names (1 char), 
                   or very long names (50+ chars) to test validation.</p>
              </div>
              
              <div className="p-4 bg-muted rounded-lg">
                <h4 className="font-medium mb-2">4. Test Auto-Selection:</h4>
                <p>When a duplicate is detected, the component should automatically select 
                   the existing category and clear the input field.</p>
              </div>
            </div>
          </div>

          <div className="space-y-2">
            <h4 className="font-medium">Common Test Categories:</h4>
            <div className="flex flex-wrap gap-2">
              <Badge variant="outline">Technology</Badge>
              <Badge variant="outline">Health</Badge>
              <Badge variant="outline">General</Badge>
              <Badge variant="outline">Food</Badge>
              <Badge variant="outline">Travel</Badge>
            </div>
            <p className="text-sm text-muted-foreground">
              Try creating categories with these names to test duplicate detection.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
