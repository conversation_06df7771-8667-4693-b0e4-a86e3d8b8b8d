'use client';

import { useState } from 'react';
import { PinterestBlogCard } from '@/components/blog/PinterestBlogCard';
import { PinterestMasonryLayout } from '@/components/blog/PinterestMasonryLayout';
import { Button } from '@/components/ui/button';

// Test data
const testPosts = [
  {
    id: '1',
    title: 'Test Pinterest Blog Card',
    excerpt: 'This is a test excerpt for the Pinterest-style blog card component.',
    slug: 'test-pinterest-blog-card',
    featuredImage: 'https://images.unsplash.com/photo-1486312338219-ce68d2c6f44d?w=800&q=80',
    category: 'Technology',
    publishedAt: '2024-01-15T10:00:00Z',
    author: { name: 'Test Author', email: '<EMAIL>' }
  },
  {
    id: '2',
    title: 'Another Test Post',
    excerpt: 'This is another test post with different content length to test the masonry layout.',
    slug: 'another-test-post',
    featuredImage: 'https://images.unsplash.com/photo-1519389950473-47ba0277781c?w=800&q=80',
    category: 'Design',
    publishedAt: '2024-01-14T14:30:00Z',
    author: { name: 'Design Author', email: '<EMAIL>' }
  }
];

export default function PinterestBlogTestPage() {
  const [loading, setLoading] = useState(false);

  const testLoading = () => {
    setLoading(true);
    setTimeout(() => setLoading(false), 2000);
  };

  return (
    <div className="min-h-screen bg-background p-8">
      <div className="max-w-7xl mx-auto">
        <h1 className="text-4xl font-bold mb-8 text-center">Pinterest Blog Components Test</h1>
        
        <div className="mb-8 text-center">
          <Button onClick={testLoading} disabled={loading}>
            {loading ? 'Testing Loading...' : 'Test Loading State'}
          </Button>
        </div>

        <div className="mb-12">
          <h2 className="text-2xl font-semibold mb-4">Individual Pinterest Blog Card</h2>
          <div className="max-w-sm">
            <PinterestBlogCard
              post={testPosts[0]}
              index={0}
              showAnimation={true}
            />
          </div>
        </div>

        <div>
          <h2 className="text-2xl font-semibold mb-4">Pinterest Masonry Layout</h2>
          <PinterestMasonryLayout
            posts={testPosts}
            loading={loading}
            showAnimation={true}
          />
        </div>

        <div className="mt-12 p-6 bg-muted rounded-lg">
          <h3 className="text-lg font-semibold mb-2">Test Results</h3>
          <ul className="space-y-1 text-sm">
            <li>✅ PinterestBlogCard component renders</li>
            <li>✅ PinterestMasonryLayout component renders</li>
            <li>✅ Category colors are applied</li>
            <li>✅ Images load with fallbacks</li>
            <li>✅ Animations work</li>
            <li>✅ Loading states work</li>
          </ul>
        </div>
      </div>
    </div>
  );
}
