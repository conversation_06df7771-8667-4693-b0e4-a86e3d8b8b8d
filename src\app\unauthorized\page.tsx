'use client';

import Link from 'next/link';
import { motion } from 'framer-motion';
import { FiAlertTriangle, FiHome, FiLogIn } from 'react-icons/fi';
import { useSession } from 'next-auth/react';

export default function UnauthorizedPage() {
  const { data: session } = useSession();
  const isAuthenticated = !!session;

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col justify-center items-center p-4">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="max-w-md w-full bg-white rounded-xl shadow-lg p-8 text-center"
      >
        <FiAlertTriangle className="h-16 w-16 text-yellow-500 mx-auto mb-6" />

        <h1 className="text-3xl font-bold text-gray-900 mb-2">Access Denied</h1>

        <p className="text-gray-600 mb-8">
          You don't have permission to access this page. This area is restricted to administrators only.
        </p>

        <div className="flex flex-col sm:flex-row justify-center gap-4">
          <Link href="/">
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="w-full flex items-center justify-center gap-2 py-2 px-4 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
            >
              <FiHome className="h-5 w-5" />
              <span>Go to Home</span>
            </motion.button>
          </Link>

          {!isAuthenticated && (
            <Link href="/login">
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="w-full flex items-center justify-center gap-2 py-2 px-4 border border-blue-600 text-blue-600 rounded-md hover:bg-blue-50 transition-colors"
              >
                <FiLogIn className="h-5 w-5" />
                <span>Sign In</span>
              </motion.button>
            </Link>
          )}
        </div>
      </motion.div>
    </div>
  );
}
