import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { CheckCircle, Home, Mail } from 'lucide-react';

export const metadata = {
  title: 'Unsubscribed Successfully',
  description: 'You have been successfully unsubscribed from our newsletter.',
};

export default function UnsubscribeConfirmedPage() {
  return (
    <div className="container max-w-4xl py-12 md:py-24">
      <div className="flex flex-col items-center text-center">
        <div className="mb-6 rounded-full bg-primary/10 p-4">
          <CheckCircle className="h-12 w-12 text-primary" />
        </div>

        <h1 className="mb-4 text-3xl font-bold md:text-4xl">Unsubscribed Successfully</h1>

        <p className="mb-8 max-w-2xl text-lg text-muted-foreground">
          You have been successfully unsubscribed from our newsletter. We're sorry to see you go, but we respect your decision.
        </p>

        <div className="flex flex-col space-y-3 sm:flex-row sm:space-x-4 sm:space-y-0">
          <Button asChild>
            <Link href="/">
              <Home className="mr-2 h-4 w-4" />
              Return to Home
            </Link>
          </Button>

          <Button variant="outline" asChild>
            <Link href="/contact">
              <Mail className="mr-2 h-4 w-4" />
              Contact Us
            </Link>
          </Button>
        </div>
      </div>
    </div>
  );
}
