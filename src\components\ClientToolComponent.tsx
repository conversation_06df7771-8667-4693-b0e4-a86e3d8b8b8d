// components/tools/DynamicToolComponent.tsx
"use client"; // Mark this as a client-side component

import dynamic from "next/dynamic";
import { FC } from "react";

// Define the component type dynamically imported
export interface DynamicToolComponentProps {
  componentName: string;
}

const DynamicToolComponent: FC<DynamicToolComponentProps> = ({ componentName }) => {
  const ToolComponent = dynamic(
    () => import(`@/components/tools/converters/${componentName}`),
    {
      loading: () => <p>Loading tool...</p>,
      ssr: false, // Disable SSR for this component
    }
  );

  return <ToolComponent />;
};

export {DynamicToolComponent} ;
