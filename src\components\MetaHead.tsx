'use client';

import { useEffect, useState } from 'react';
import <PERSON>rip<PERSON> from 'next/script';
import Head from 'next/head';
import { useLoading } from '@/contexts/LoadingContext';

interface MetaHeadProps {
  title?: string;
  description?: string;
  keywords?: string[];
  ogTitle?: string;
  ogDescription?: string;
  ogImage?: string;
  twitterHandle?: string;
}

// This component is for client-side analytics and metadata
// For SEO metadata, use the Metadata API in layout.tsx
export function MetaHead({
  title,
  description,
  keywords,
  ogTitle,
  ogDescription,
  ogImage,
  twitterHandle,
}: MetaHeadProps) {
  const [siteSettings, setSiteSettings] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const { pageTitle, isLoading: contextLoading } = useLoading();

  // Create loading meta tag on mount
  useEffect(() => {
    if (typeof document !== 'undefined') {
      // Check if loading meta tag exists
      let loadingMeta = document.querySelector('meta[name="loading"]');
      if (!loadingMeta) {
        loadingMeta = document.createElement('meta');
        loadingMeta.setAttribute('name', 'loading');
        loadingMeta.setAttribute('content', contextLoading ? 'true' : 'false');
        document.head.appendChild(loadingMeta);
      }
    }
  }, [contextLoading]);

  useEffect(() => {
    async function fetchSettings() {
      try {
        setIsLoading(true);
        const response = await fetch('/api/settings');

        if (!response.ok) {
          throw new Error(`Failed to fetch settings: ${response.status}`);
        }

        const data = await response.json();
        setSiteSettings(data);
      } catch (error) {
        console.error("Error fetching site settings:", error);
      } finally {
        setIsLoading(false);
      }
    }

    fetchSettings();
  }, []);

  // Update document title when pageTitle changes
  useEffect(() => {
    if (typeof document !== 'undefined' && pageTitle) {
      document.title = pageTitle;
    }
  }, [pageTitle]);

  if (!siteSettings) {
    return null;
  }

  return (
    <>
      {/* Loading state meta tag */}
      <meta name="loading" content={contextLoading ? 'true' : 'false'} />

      {/* Analytics */}
      {siteSettings.googleAnalyticsId && (
        <>
          <Script
            src={`https://www.googletagmanager.com/gtag/js?id=${siteSettings.googleAnalyticsId}`}
            strategy="afterInteractive"
          />
          <Script id="google-analytics" strategy="afterInteractive">
            {`
              window.dataLayer = window.dataLayer || [];
              function gtag(){dataLayer.push(arguments);}
              gtag('js', new Date());
              gtag('config', '${siteSettings.googleAnalyticsId}');
            `}
          </Script>
        </>
      )}

      {/* Facebook Pixel */}
      {siteSettings.facebookPixelId && (
        <Script id="facebook-pixel" strategy="afterInteractive">
          {`
            !function(f,b,e,v,n,t,s)
            {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
            n.callMethod.apply(n,arguments):n.queue.push(arguments)};
            if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
            n.queue=[];t=b.createElement(e);t.async=!0;
            t.src=v;s=b.getElementsByTagName(e)[0];
            s.parentNode.insertBefore(t,s)}(window, document,'script',
            'https://connect.facebook.net/en_US/fbevents.js');
            fbq('init', '${siteSettings.facebookPixelId}');
            fbq('track', 'PageView');
          `}
        </Script>
      )}
    </>
  );
}
