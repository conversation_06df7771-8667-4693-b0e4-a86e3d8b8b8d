"use client";

import Link from "next/link";
import { usePathname, useRouter } from "next/navigation";
import { motion } from "framer-motion";
import { cn } from "@/lib/utils";
import { useAuth } from "@/hooks/useAuth";
import { FiHome, FiTool, FiBook, FiUser, FiSettings } from "react-icons/fi";
import { useLoading } from "@/contexts/LoadingContext";
import { Progress } from "@/components/ui/nprogress";
import { useCallback } from "react";

interface NavItem {
  name: string;
  path: string;
  icon: React.ReactNode;
  requiresAuth?: boolean;
  adminOnly?: boolean;
}

export function Navigation() {
  const pathname = usePathname();
  const router = useRouter();
  const { user } = useAuth();
  const { startLoading, stopLoading } = useLoading();

  const navItems: NavItem[] = [
    { name: "Home", path: "/", icon: <FiHome className="mr-2" /> },
    { name: "Tools", path: "/tools", icon: <FiTool className="mr-2" /> },
    { name: "Blog", path: "/blog", icon: <FiBook className="mr-2" /> },
    {
      name: "Dashboard",
      path: "/dashboard",
      icon: <FiUser className="mr-2" />,
      requiresAuth: true
    },
    {
      name: "Admin",
      path: "/admin",
      icon: <FiSettings className="mr-2" />,
      requiresAuth: true,
      adminOnly: true
    },
  ];

  // Filter items based on auth status and role
  const filteredNavItems = navItems.filter(item => {
    if (item.requiresAuth && !user) return false;
    if (item.adminOnly && user?.role !== 'admin') return false;
    return true;
  });

  // Handle navigation with loading indicator using Next.js router
  const handleNavigation = useCallback((path: string) => {
    if (pathname === path) return; // Skip if already on this path

    // Start loading indicator
    startLoading();
    Progress.start();

    // Use Next.js router for SPA navigation
    try {
      // Push to the new route
      router.push(path);

      // Set a timeout to stop loading if navigation takes too long
      const timeoutId = setTimeout(() => {
        stopLoading();
        Progress.done();
      }, 3000); // 3 second timeout

      // Clean up timeout on successful navigation
      window.addEventListener('routeChangeComplete', () => {
        clearTimeout(timeoutId);
      }, { once: true });

    } catch (error) {
      console.error('Navigation error:', error);
      // Fallback to direct navigation if router fails
      window.location.href = path;
    }
  }, [pathname, router, startLoading, stopLoading]);

  return (
    <nav className="flex items-center space-x-4">
      {filteredNavItems.map((item) => {
        const isActive = pathname === item.path || pathname.startsWith(`${item.path}/`);

        return (
          <button
            key={item.path}
            onClick={() => handleNavigation(item.path)}
            className={cn(
              "flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors",
              isActive
                ? "bg-primary text-primary-foreground"
                : "text-muted-foreground hover:text-foreground hover:bg-muted"
            )}
          >
            {item.icon}
            <span>{item.name}</span>
            {isActive && (
              <motion.div
                className="absolute bottom-0 left-0 h-0.5 w-full bg-primary"
                layoutId="navbar-indicator"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.2 }}
              />
            )}
          </button>
        );
      })}
    </nav>
  );
}








































