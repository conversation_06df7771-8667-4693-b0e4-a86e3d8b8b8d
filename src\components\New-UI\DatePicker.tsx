'use client';

import React, { forwardRef } from 'react';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import { Input } from '@/components/ui/input';

type DateTimePickerProps = {
  value: Date | null;
  onChange: (date: Date | null) => void;
  disabled?: boolean;
};

export const DateTimePicker = forwardRef<HTMLInputElement, DateTimePickerProps>(
  ({ value, onChange, disabled }, ref) => {
    return (
      <DatePicker
        selected={value}
        onChange={onChange}
        showTimeSelect
        dateFormat="Pp"
        customInput={<Input ref={ref} />}
        className="w-full"
        disabled={disabled}
      />
    );
  }
);

DateTimePicker.displayName = 'DateTimePicker';
