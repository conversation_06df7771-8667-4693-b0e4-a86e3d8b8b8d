"use client";

import * as React from "react";
import { cva, type VariantProps } from "class-variance-authority";

import { cn } from "@/lib/utils";
import { SidebarProps } from "@/types/ui";

const sidebarVariants = cva(
  "fixed inset-y-0 left-0 z-40 flex flex-col bg-background border-r shadow-sm transition-transform duration-300 ease-in-out",
  {
    variants: {
      size: {
        default: "w-64",
        sm: "w-48",
        lg: "w-80",
      },
      variant: {
        default: "",
        overlay: "md:translate-x-0",
        responsive: "md:relative md:translate-x-0",
      },
    },
    defaultVariants: {
      size: "default",
      variant: "default",
    },
  },
);

export interface SidebarComponentProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof sidebarVariants>,
    SidebarProps {}

const Sidebar = React.forwardRef<HTMLDivElement, SidebarComponentProps>(
  (
    { className, size, variant, isOpen = true, onClose, children, ...props },
    ref,
  ) => {
    return (
      <>
        {variant === "overlay" && isOpen && (
          <div
            className="fixed inset-0 z-30 bg-black/50"
            onClick={onClose}
            aria-hidden="true"
          />
        )}
        <aside
          ref={ref}
          className={cn(
            sidebarVariants({ size, variant }),
            isOpen ? "translate-x-0" : "-translate-x-full",
            className,
          )}
          {...props}
        >
          {children}
        </aside>
      </>
    );
  },
);
Sidebar.displayName = "Sidebar";

const SidebarHeader = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div ref={ref} className={cn("p-4 border-b", className)} {...props} />
));
SidebarHeader.displayName = "SidebarHeader";

const SidebarContent = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn("flex-1 overflow-auto p-4", className)}
    {...props}
  />
));
SidebarContent.displayName = "SidebarContent";

const SidebarFooter = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div ref={ref} className={cn("p-4 border-t", className)} {...props} />
));
SidebarFooter.displayName = "SidebarFooter";

const SidebarItem = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & { active?: boolean }
>(({ className, active, ...props }, ref) => (
  <div
    ref={ref}
    className={cn(
      "flex items-center py-2 px-3 rounded-md text-sm cursor-pointer transition-colors",
      active
        ? "bg-accent text-accent-foreground font-medium"
        : "text-muted-foreground hover:bg-accent/50 hover:text-accent-foreground",
      className,
    )}
    {...props}
  />
));
SidebarItem.displayName = "SidebarItem";

export { Sidebar, SidebarHeader, SidebarContent, SidebarFooter, SidebarItem };
