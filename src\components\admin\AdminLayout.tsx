"use client";

import { ReactNode, useState, useEffect } from "react";
import { useTheme } from "@/hooks/useTheme";
import { cn } from "@/lib/utils";
import { AdminSidebar } from "./AdminSidebar";
import { Button } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  Menu,
  Sun,
  Moon,
  LogOut,
  User
} from "lucide-react";
import { useSession, signOut } from "next-auth/react";
import { Skeleton } from "@/components/New-UI/Skeleton";

interface AdminLayoutProps {
  children: ReactNode;
  title?: string;
}

export default function AdminLayout({ children, title = "Admin Panel" }: AdminLayoutProps) {
  const { theme, toggleTheme } = useTheme();
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const { data: session, status: sessionStatus } = useSession();
  const isAdmin = session?.user?.role === "admin";
  const [adminData, setAdminData] = useState<{
    name: string;
    email: string;
    avatar?: string;
  } | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Simplified admin data fetching using NextAuth session
  useEffect(() => {
    if (sessionStatus === 'loading') {
      setIsLoading(true);
      return;
    }

    if (session?.user) {
      // Use session data directly
      setAdminData({
        name: session.user.name || 'Admin',
        email: session.user.email || '<EMAIL>',
        avatar: session.user.image || undefined
      });
      setIsLoading(false);
    } else {
      // No session, set default data and stop loading
      setAdminData({
        name: 'Admin',
        email: '<EMAIL>'
      });
      setIsLoading(false);
    }
  }, [session, sessionStatus]);

  const handleLogout = async () => {
    await signOut({ callbackUrl: '/login' });
  };

  return (
    <div className="flex h-screen w-full overflow-hidden bg-background">
      {/* Sidebar - fixed on all screens */}
      <div
        id="admin-sidebar"
        className={cn(
          "fixed inset-y-0 left-0 z-50 w-64 bg-card border-r border-border transition-transform duration-300 ease-in-out",
          sidebarOpen ? "translate-x-0" : "-translate-x-full"
        )}
      >
        <AdminSidebar onClose={() => setSidebarOpen(false)} />
      </div>

      {/* Overlay for sidebar - shown when sidebar is open */}
      {sidebarOpen && (
        <div
          className="fixed inset-0 z-40 bg-black/50"
          onClick={() => setSidebarOpen(false)}
          aria-hidden="true"
        />
      )}

      {/* Main content - takes remaining width with improved layout */}
      <div className="flex flex-col flex-1 w-full min-w-0 overflow-x-hidden">
        {/* Header */}
        <header className="sticky top-0 z-30 flex items-center justify-between h-16 px-4 border-b bg-card w-full">
          <div className="flex items-center gap-4">
            {/* Menu button - visible on all screens */}
            <Button
              variant="ghost"
              size="icon"
              onClick={() => {
                setSidebarOpen(prev => !prev);
              }}
              className="block"
              aria-label={sidebarOpen ? "Close sidebar" : "Open sidebar"}
              aria-expanded={sidebarOpen}
              aria-controls="admin-sidebar"
              id="menu-button"
            >
              <Menu className="h-5 w-5" />
            </Button>

            <h2 className="text-xl font-semibold truncate">{title}</h2>
          </div>

          <div className="flex items-center gap-3 flex-shrink-0">
            {/* Theme Toggle */}
            <Button
              variant="ghost"
              size="icon"
              onClick={toggleTheme}
              aria-label="Toggle theme"
            >
              {theme === "dark" ? (
                <Sun className="h-5 w-5" />
              ) : (
                <Moon className="h-5 w-5" />
              )}
            </Button>

            {/* User Info */}
            <div className="flex items-center gap-2">
              {isLoading ? (
                <>
                  <Skeleton className="h-8 w-8 rounded-full" />
                  <div className="hidden md:block">
                    <Skeleton className="h-4 w-24 mb-1" />
                    <Skeleton className="h-3 w-16" />
                  </div>
                </>
              ) : (
                <>
                  <Avatar className="h-8 w-8 flex-shrink-0">
                    {adminData?.avatar ? (
                      <AvatarImage src={adminData.avatar} alt={adminData.name} />
                    ) : (
                      <AvatarFallback>
                        <User className="h-4 w-4" />
                      </AvatarFallback>
                    )}
                  </Avatar>
                  <div className="hidden md:block">
                    <p className="text-sm font-medium leading-none">{adminData?.name || 'Admin'}</p>
                    <p className="text-xs text-muted-foreground">{isAdmin ? 'Administrator' : 'User'}</p>
                  </div>
                </>
              )}
              <Button
                variant="ghost"
                size="icon"
                onClick={handleLogout}
                className="ml-1"
                title="Logout"
              >
                <LogOut className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </header>

        {/* Main content area with improved width handling */}
        <main className="flex-1 overflow-auto p-4 md:p-6 w-full max-w-full min-w-0">
          <div className="w-full max-w-full mx-auto min-w-0">
            {children}
          </div>
        </main>
      </div>
    </div>
  );
}
