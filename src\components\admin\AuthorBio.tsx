export default function AuthorBio({ author }: { author: any }) {
    return (
      <div className="p-4 border rounded-lg bg-gray-50">
        <div className="flex items-center gap-4 mb-4">
          <div className="w-12 h-12 rounded-full bg-gray-300 flex items-center justify-center">
            {author.name.charAt(0)}
          </div>
          <div>
            <h4 className="font-semibold">{author.name}</h4>
            <p className="text-sm text-gray-600">{author.email}</p>
          </div>
        </div>
        {author.bio && <p className="text-sm text-gray-600">{author.bio}</p>}
      </div>
    );
  }