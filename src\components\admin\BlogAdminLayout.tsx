"use client";

import { ReactNode } from "react";
import { motion } from "framer-motion";

interface BlogAdminLayoutProps {
  children: ReactNode;
  title: string;
  subtitle?: string;
}

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
      delayChildren: 0.1,
    },
  },
};

const itemVariants = {
  hidden: { y: 20, opacity: 0 },
  visible: {
    y: 0,
    opacity: 1,
    transition: { type: "spring", stiffness: 300, damping: 24 },
  },
};

export function BlogAdminLayout({
  children,
  title,
  subtitle,
}: BlogAdminLayoutProps) {
  return (
    <div className="min-h-screen flex items-center justify-center px-4 py-10 bg-background">
      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
        className="w-full max-w-4xl space-y-6"
      >
        <motion.div variants={itemVariants}>
          <h1 className="text-3xl font-bold text-center text-adaptive">
            {title}
          </h1>
          {subtitle && (
            <p className="text-sm text-center text-muted-foreground mt-1">
              {subtitle}
            </p>
          )}
        </motion.div>

        <motion.div variants={itemVariants} className="w-full">
          {children}
        </motion.div>
      </motion.div>
    </div>
  );
}
