"use client";
import { ColumnDef } from "@tanstack/react-table";
import { MoreHorizontal } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import Link from "next/link";
import { Badge } from "@/components/ui/badge";
import { BlogPost } from "@/types/blog";
import { ErrorBoundary } from "../ErrorBoundary";
import { DataTable } from "@/components/ui/data-table";

export const columns: ColumnDef<BlogPost>[] = [
  {
    accessorKey: "title",
    header: "Title",
    cell: ({ row }: { row: any }) => {
      const post = row.original as BlogPost;
      return (
        <Link
          href={`/admin/blog/${post._id}`}
          className="font-medium hover:text-primary"
        >
          {row.getValue("title") || "Untitled"}
        </Link>
      );
    },
  },
  {
    accessorKey: "status",
    header: "Status",
    cell: ({ row }: { row: any }) => {
      const status = row.getValue("status") as "published" | "draft" | "scheduled" | "archived";
      const variantMap: Record<string, any> = {
        published: 'default',
        draft: 'secondary',
        scheduled: 'outline',
        archived: 'destructive'
      };

      // Ensure status is a valid string
      const displayStatus = typeof status === 'string' ? status : 'unknown';
      const variant = variantMap[displayStatus] || 'default';

      return (
        <Badge variant={variant}>
          {displayStatus.charAt(0).toUpperCase() + displayStatus.slice(1)}
        </Badge>
      );
    },
  },
  {
    accessorKey: "scheduledAt",
    header: "Publish Date",
    cell: ({ row }: { row: any }) => (
      row.original.scheduledAt
        ? new Date(row.original.scheduledAt).toLocaleDateString()
        : "Immediately"
    ),
  },
  {
    id: "actions",
    cell: ({ row }: { row: any }) => {
      const post = row.original as BlogPost;
      return (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-8 w-8 p-0">
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem asChild>
              <Link href={`/admin/blog/${post._id}`}>Edit</Link>
            </DropdownMenuItem>
            <DropdownMenuItem asChild>
              <Link href={`/blog/${post.slug}`} target="_blank">
                Preview
              </Link>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      );
    },
  },
];