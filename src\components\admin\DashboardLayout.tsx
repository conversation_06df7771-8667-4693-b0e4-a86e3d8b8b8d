"use client";

import React, { ReactNode } from "react";

// Define the props interface for DashboardLayout
interface DashboardLayoutProps {
  children: ReactNode;
}

// Export as default
export default function DashboardLayout({ children }: DashboardLayoutProps) {
  return (
    <div className="flex min-h-screen bg-background">
      <div className="w-64 bg-card border-r border-border hidden md:block">
        <div className="p-4">
          <h2 className="text-xl font-bold">Admin Panel</h2>
        </div>
        <nav className="p-2">
          {/* Navigation items would go here */}
        </nav>
      </div>
      <div className="flex-1 flex flex-col">
        <header className="border-b border-border p-4">
          <h1 className="text-2xl font-bold">Dashboard</h1>
          <p className="text-muted-foreground">Manage your application</p>
        </header>
        <main className="flex-1 p-6">
          {children}
        </main>
      </div>
    </div>
  );
}


