// "use client";

// import Link from "next/link";
// import { usePathname } from "next/navigation";

// import { Button } from "@/components/ui/Button";
// import {
//   LayoutDashboard,
//   FileText,
//   PlusCircle,
//   List,
//   ChevronDown,
//   MessageSquare,
//   X,
//   LogOut,
//   Home,
//   BarChart2,
//   Palette
// } from "lucide-react";
// import { useState } from "react";
// import { signOut } from "next-auth/react";

// // Define types for navigation items
// interface NavSubItem {
//   href: string;
//   icon: React.ReactNode;
//   label: string;
//   external?: boolean;
// }

// interface NavItem {
//   href?: string;
//   icon: React.ReactNode;
//   label: string;
//   external?: boolean;
//   subItems?: NavSubItem[];
// }

// interface EditorSidebarProps {
//   onClose?: () => void;
// }

// // const navItems: NavItem[] = [
// //   {
// //     href: "/admin",
// //     icon: <LayoutDashboard className="h-5 w-5" />,
// //     label: "Dashboard",
// //   },
// //   {
// //     href: "/",
// //     icon: <Home className="h-5 w-5" />,
// //     label: "Home",
// //   },
// //   {
// //     icon: <FileText className="h-5 w-5" />,
// //     label: "Blog",
// //     subItems: [
// //       {
// //         href: "/admin/blog/stats",
// //         icon: <BarChart2 className="h-4 w-4" />,
// //         label: "Stats",
// //       },
// //       {
// //         href: "/admin/blog/new",
// //         icon: <PlusCircle className="h-4 w-4" />,
// //         label: "New Post",
// //       },
// //       {
// //         href: "/admin/blog/posts",
// //         icon: <List className="h-4 w-4" />,
// //         label: "All Posts",
// //       },
// //       {
// //         href: "/admin/blog/comments",
// //         icon: <MessageSquare className="h-4 w-4" />,
// //         label: "Comments",
// //       },
// //       {
// //         href: "/admin/blog/theme",
// //         icon: <Palette className="h-4 w-4" />,
// //         label: "Theme",
// //       },
// //     ],
// //   },
// // ];

// export function EditorSidebar({ onClose }: EditorSidebarProps) {
//   const pathname = usePathname();
//   const [openSubmenu, setOpenSubmenu] = useState<string | null>(null);

//   const toggleSubmenu = (label: string) => {
//     setOpenSubmenu(openSubmenu === label ? null : label);
//   };

//   const handleLogout = async () => {
//     await signOut({ callbackUrl: "/login" });
//   };

//   return (
//     <div className="flex flex-col h-full bg-adaptive-card">
//       <div className="flex items-center justify-between p-4 border-b border-[rgb(var(--border))]">
//         <Link href="/admin" className="flex items-center gap-2">
//           <div className="bg-[rgb(var(--primary))] text-[rgb(var(--primary-foreground))] p-1.5 rounded">
//             <FileText className="h-5 w-5" />
//           </div>
//           <span className="font-bold text-xl text-adaptive">Admin Panel</span>
//         </Link>
//         {onClose && (
//           <Button variant="ghost" size="icon" onClick={onClose} className="lg:hidden">
//             <X className="h-5 w-5" />
//           </Button>
//         )}
//       </div>

//       <div className="flex-1 py-2 overflow-auto">
//         <nav className="px-2 space-y-1">
//           {navItems.map((item) => (
//             <div key={item.label}>
//               {item.subItems ? (
//                 <div className="space-y-1">
//                   <Button
//                     variant="ghost"
//                     className="w-full justify-start gap-2 text-adaptive-muted hover:text-adaptive hover:bg-[rgb(var(--muted))]/50"
//                     onClick={() => toggleSubmenu(item.label)}
//                   >
//                     {item.icon}
//                     <span>{item.label}</span>
//                     <ChevronDown
//                       className={`ml-auto h-4 w-4 transition-transform ${
//                         openSubmenu === item.label ? "rotate-180" : ""
//                       }`}
//                     />
//                   </Button>

//                   <div
//                     className={`overflow-hidden transition-all duration-300 ease-in-out ${
//                       openSubmenu === item.label ? "max-h-96 opacity-100" : "max-h-0 opacity-0"
//                     }`}
//                   >
//                     <div
//                       className="ml-4 pl-2 border-l border-[rgb(var(--border))] space-y-1 py-1"
//                     >
//                       {item.subItems.map((subItem) => (
//                         <Button
//                           key={subItem.href}
//                           variant="ghost"
//                           asChild
//                           className={`w-full justify-start gap-2 text-adaptive-muted hover:text-adaptive hover:bg-[rgb(var(--muted))]/50 ${
//                             pathname === subItem.href && "bg-[rgb(var(--primary))]/10 text-[rgb(var(--primary))] font-medium"
//                           }`}
//                         >
//                           {subItem.external ? (
//                             <a href={subItem.href} target="_blank" rel="noopener noreferrer" className="flex items-center gap-2">
//                               {subItem.icon}
//                               <span>{subItem.label}</span>
//                             </a>
//                           ) : (
//                             <Link href={subItem.href} className="flex items-center gap-2">
//                               {subItem.icon}
//                               <span>{subItem.label}</span>
//                             </Link>
//                           )}
//                         </Button>
//                       ))}
//                     </div>
//                   </div>
//                 </div>
//               ) : (
//                 <Button
//                   variant="ghost"
//                   asChild
//                   className={`w-full justify-start gap-2 text-adaptive-muted hover:text-adaptive hover:bg-[rgb(var(--muted))]/50 ${
//                     pathname === item.href && "bg-[rgb(var(--primary))]/10 text-[rgb(var(--primary))] font-medium"
//                   }`}
//                 >
//                   {item.external ? (
//                     <a href={item.href} target="_blank" rel="noopener noreferrer" className="flex items-center gap-2">
//                       {item.icon}
//                       <span>{item.label}</span>
//                     </a>
//                   ) : (
//                     <Link href={item.href || "#"} className="flex items-center gap-2">
//                       {item.icon}
//                       <span>{item.label}</span>
//                     </Link>
//                   )}
//                 </Button>
//               )}
//             </div>
//           ))}
//         </nav>
//       </div>

//       <div className="p-4 border-t border-[rgb(var(--border))]">
//         <div className="flex items-center gap-2 px-2 py-1.5 mb-4">
//           <div className="w-8 h-8 rounded-full bg-[rgb(var(--primary))]/20 flex items-center justify-center text-[rgb(var(--primary))]">
//             E
//           </div>
//           <div className="flex-1 min-w-0">
//             <p className="text-sm font-medium text-adaptive truncate">Editor User</p>
//             <p className="text-xs text-adaptive-muted truncate"><EMAIL></p>
//           </div>
//         </div>

//         <Button
//           variant="ghost"
//           className="w-full justify-start gap-2 text-adaptive-muted hover:text-adaptive"
//           onClick={handleLogout}
//         >
//           <LogOut className="h-5 w-5" />
//           <span>Logout</span>
//         </Button>
//       </div>
//     </div>
//   );
// }
