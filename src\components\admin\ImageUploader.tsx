'use client';

import { useState, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Upload, X, Image as ImageIcon } from 'lucide-react';
import Image from 'next/image';
import { toast } from '@/hooks/use-toast';

interface ImageUploaderProps {
  label: string;
  currentImageUrl?: string;
  onImageUpload: (imageUrl: string) => void;
  type?: 'logo' | 'favicon' | 'og' | 'image';
  className?: string;
}

export function ImageUploader({
  label,
  currentImageUrl,
  onImageUpload,
  type = 'image',
  className = '',
}: ImageUploaderProps) {
  const [isUploading, setIsUploading] = useState(false);
  const [previewUrl, setPreviewUrl] = useState<string | null>(currentImageUrl || null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // Check file type
    const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/svg+xml', 'image/webp', 'image/x-icon'];
    if (!allowedTypes.includes(file.type)) {
      toast({
        title: 'Invalid file type',
        description: 'Please upload a JPEG, PNG, GIF, SVG, WebP, or ICO file.',
        variant: 'destructive',
      });
      return;
    }

    // Check file size (5MB max)
    if (file.size > 5 * 1024 * 1024) {
      toast({
        title: 'File too large',
        description: 'Please upload an image smaller than 5MB.',
        variant: 'destructive',
      });
      return;
    }

    // Create a preview URL
    const objectUrl = URL.createObjectURL(file);
    setPreviewUrl(objectUrl);

    // Upload the file
    setIsUploading(true);
    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('type', type);

      const response = await fetch('/api/upload', {
        method: 'POST',
        body: formData,
        credentials: 'include', // Include cookies for authentication
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to upload image');
      }

      const data = await response.json();
      onImageUpload(data.fileUrl);

      toast({
        title: 'Image uploaded',
        description: 'Your image has been uploaded successfully.',
      });
    } catch (error) {
      console.error('Upload error:', error);
      toast({
        title: 'Upload failed',
        description: error instanceof Error ? error.message : 'Failed to upload image',
        variant: 'destructive',
      });

      // Revert to the previous image if upload fails
      setPreviewUrl(currentImageUrl || null);
    } finally {
      setIsUploading(false);
    }
  };

  const handleRemoveImage = () => {
    setPreviewUrl(null);
    onImageUpload('');
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  return (
    <div className={`space-y-2 ${className}`}>
      <Label htmlFor={`${type}-upload`}>{label}</Label>

      <div className="flex flex-col items-center gap-4">
        {previewUrl ? (
          <div className="relative">
            <div className="border border-[rgb(var(--border))] rounded-md overflow-hidden relative">
              <Image
                src={previewUrl}
                alt={label}
                width={type === 'logo' ? 200 : 100}
                height={type === 'logo' ? 100 : 100}
                className="object-contain"
                style={{
                  maxWidth: type === 'logo' ? '200px' : '100px',
                  maxHeight: type === 'logo' ? '100px' : '100px'
                }}
              />
            </div>
            <Button
              type="button"
              variant="destructive"
              size="icon"
              className="absolute -top-2 -right-2 h-6 w-6 rounded-full"
              onClick={handleRemoveImage}
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        ) : (
          <div className="border border-dashed border-[rgb(var(--border))] rounded-md p-6 flex flex-col items-center justify-center gap-2 bg-[rgb(var(--muted))]">
            <ImageIcon className="h-10 w-10 text-[rgb(var(--muted-foreground))]" />
            <p className="text-sm text-[rgb(var(--muted-foreground))]">No image uploaded</p>
          </div>
        )}

        <div className="flex items-center gap-2">
          <Input
            ref={fileInputRef}
            id={`${type}-upload`}
            type="file"
            accept="image/jpeg,image/png,image/gif,image/svg+xml,image/webp,image/x-icon"
            onChange={handleFileChange}
            className="hidden"
          />
          <Button
            type="button"
            variant="outline"
            onClick={() => fileInputRef.current?.click()}
            disabled={isUploading}
            className="flex items-center gap-2"
          >
            <Upload className="h-4 w-4" />
            {isUploading ? 'Uploading...' : previewUrl ? 'Change Image' : 'Upload Image'}
          </Button>
        </div>
      </div>
    </div>
  );
}
