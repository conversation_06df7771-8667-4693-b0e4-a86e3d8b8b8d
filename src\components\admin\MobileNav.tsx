"use client";
import { useState, useCallback, useRef } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet";
import { Menu } from "lucide-react";
import { AdminSidebar } from "./AdminSidebar";

export function MobileNav() {
  const [isOpen, setIsOpen] = useState(false);

  // Use a memoized callback to prevent recreation on each render
  const handleClose = useCallback(() => {
    setIsOpen(false);
  }, []);

  // Use a memoized callback for onOpenChange to prevent recreation on each render
  const handleOpenChange = useCallback((open: boolean) => {
    setIsOpen(open);
  }, []);

  return (
    <Sheet open={isOpen} onOpenChange={handleOpenChange}>
      <SheetTrigger asChild>
        <Button variant="outline" size="icon" className="md:hidden">
          <Menu className="h-5 w-5" />
          <span className="sr-only">Toggle menu</span>
        </Button>
      </SheetTrigger>
      <SheetContent side="left" className="p-0">
        <AdminSidebar onClose={handleClose} />
      </SheetContent>
    </Sheet>
  );
}