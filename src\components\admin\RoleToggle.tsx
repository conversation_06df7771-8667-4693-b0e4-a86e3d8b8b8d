"use client";

import { useState } from "react";
import { motion } from "framer-motion";
import { Shield, User, Loader2, Check, X } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useToast } from "@/hooks/use-toast";
import { USER_ROLES, type UserRole } from "@/lib/constants/roles";

interface RoleToggleProps {
  userId: string;
  currentRole: UserRole;
  userName: string;
  isProtected?: boolean;
  onRoleChange?: (newRole: UserRole) => void;
  className?: string;
}

export default function RoleToggle({
  userId,
  currentRole,
  userName,
  isProtected = false,
  onRoleChange,
  className = ""
}: RoleToggleProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [role, setRole] = useState<UserRole>(currentRole);
  const { toast } = useToast();

  const updateRole = async (newRole: UserRole) => {
    if (newRole === role || isProtected) return;

    setIsLoading(true);
    try {
      const response = await fetch(`/api/users/${userId}/role`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ role: newRole }),
      });

      if (response.ok) {
        const updatedUser = await response.json();
        setRole(newRole);
        onRoleChange?.(newRole);
        
        toast({
          title: "Role Updated",
          description: `${userName}'s role has been changed to ${newRole}`,
        });
      } else {
        const error = await response.json();
        throw new Error(error.error || "Failed to update role");
      }
    } catch (error) {
      console.error("Error updating role:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to update role",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const getRoleIcon = (roleType: UserRole) => {
    switch (roleType) {
      case USER_ROLES.ADMIN:
        return <Shield className="h-3 w-3" />;
      case USER_ROLES.EDITOR:
        return <Check className="h-3 w-3" />;
      case USER_ROLES.MODERATOR:
        return <X className="h-3 w-3" />;
      default:
        return <User className="h-3 w-3" />;
    }
  };

  const getRoleBadgeVariant = (roleType: UserRole) => {
    switch (roleType) {
      case USER_ROLES.ADMIN:
        return "destructive";
      case USER_ROLES.EDITOR:
        return "default";
      case USER_ROLES.MODERATOR:
        return "secondary";
      default:
        return "outline";
    }
  };

  const getRoleColor = (roleType: UserRole) => {
    switch (roleType) {
      case USER_ROLES.ADMIN:
        return "text-red-600 dark:text-red-400";
      case USER_ROLES.EDITOR:
        return "text-blue-600 dark:text-blue-400";
      case USER_ROLES.MODERATOR:
        return "text-yellow-600 dark:text-yellow-400";
      default:
        return "text-gray-600 dark:text-gray-400";
    }
  };

  if (isProtected) {
    return (
      <div className={`flex items-center ${className}`}>
        <Badge variant={getRoleBadgeVariant(role)} className="flex items-center gap-1">
          {getRoleIcon(role)}
          <span className="capitalize">{role}</span>
          <Shield className="h-3 w-3 ml-1 text-yellow-500" title="Protected Account" />
        </Badge>
      </div>
    );
  }

  return (
    <div className={`flex items-center ${className}`}>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="ghost"
            size="sm"
            disabled={isLoading}
            className="h-auto p-0 hover:bg-transparent"
          >
            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="flex items-center"
            >
              <Badge 
                variant={getRoleBadgeVariant(role)} 
                className="flex items-center gap-1 cursor-pointer hover:opacity-80 transition-opacity"
              >
                {isLoading ? (
                  <Loader2 className="h-3 w-3 animate-spin" />
                ) : (
                  getRoleIcon(role)
                )}
                <span className="capitalize">{role}</span>
              </Badge>
            </motion.div>
          </Button>
        </DropdownMenuTrigger>
        
        <DropdownMenuContent align="end" className="w-48">
          {Object.values(USER_ROLES).map((roleOption) => (
            <DropdownMenuItem
              key={roleOption}
              onClick={() => updateRole(roleOption)}
              disabled={isLoading || roleOption === role}
              className={`flex items-center gap-2 ${getRoleColor(roleOption)}`}
            >
              {getRoleIcon(roleOption)}
              <span className="capitalize">{roleOption}</span>
              {roleOption === role && (
                <Check className="h-3 w-3 ml-auto text-green-600" />
              )}
            </DropdownMenuItem>
          ))}
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
}
