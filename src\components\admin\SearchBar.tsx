"use client";

import { useState, useEffect, useRef } from "react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Loader2, Search, X, User, FileText, Hammer } from "lucide-react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { useDebounce } from "@/hooks/useDebounce";
import { useOnClickOutside } from "@/hooks/useOnClickOutside";

interface SearchResult {
  _id: string;
  type: "user" | "blog" | "tool";
  name?: string;
  email?: string;
  title?: string;
  slug?: string;
  toolName?: string;
  toolId?: string;
  status?: string;
  createdAt?: string;
  timestamp?: string;
  authorId?: {
    name: string;
  };
}

interface SearchBarProps {
  placeholder?: string;
  className?: string;
}

export function SearchBar({ placeholder = "Search...", className = "" }: SearchBarProps) {
  const [query, setQuery] = useState("");
  const [results, setResults] = useState<SearchResult[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isOpen, setIsOpen] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const debouncedQuery = useDebounce(query, 300);
  const router = useRouter();
  const searchRef = useRef<HTMLDivElement>(null);

  // Close search results when clicking outside
  useOnClickOutside(searchRef, () => setIsOpen(false));

  // Search when query changes
  useEffect(() => {
    const performSearch = async () => {
      if (!debouncedQuery.trim()) {
        setResults([]);
        setIsOpen(false);
        return;
      }

      try {
        setIsLoading(true);
        setError(null);

        const response = await fetch(`/api/search?q=${encodeURIComponent(debouncedQuery)}&limit=5`);

        if (!response.ok) {
          throw new Error(`Search failed: ${response.statusText}`);
        }

        const data = await response.json();
        setResults(data.results);
        setIsOpen(true);
      } catch (err) {
        console.error("Search error:", err);
        setError("Failed to perform search");
      } finally {
        setIsLoading(false);
      }
    };

    performSearch();
  }, [debouncedQuery]);

  // Handle search submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (query.trim()) {
      router.push(`/admin/search?q=${encodeURIComponent(query)}`);
      setIsOpen(false);
    }
  };

  // Clear search
  const clearSearch = () => {
    setQuery("");
    setResults([]);
    setIsOpen(false);
  };

  // Get icon based on result type
  const getIcon = (type: string) => {
    switch (type) {
      case "user":
        return <User className="h-4 w-4 text-blue-500" />;
      case "blog":
        return <FileText className="h-4 w-4 text-green-500" />;
      case "tool":
        return <Hammer className="h-4 w-4 text-purple-500" />;
      default:
        return null;
    }
  };

  // Get title based on result type
  const getTitle = (result: SearchResult) => {
    switch (result.type) {
      case "user":
        return result.name || result.email;
      case "blog":
        return result.title;
      case "tool":
        return result.toolName;
      default:
        return "Unknown result";
    }
  };

  // Get link based on result type
  const getLink = (result: SearchResult) => {
    switch (result.type) {
      case "user":
        return `/admin/users/${result._id}`;
      case "blog":
        return `/admin/blog/${result.slug}`;
      case "tool":
        return `/admin/tools?id=${result.toolId}`;
      default:
        return "#";
    }
  };

  return (
    <div className={`relative ${className}`} ref={searchRef}>
      <form onSubmit={handleSubmit} className="relative">
        <Input
          type="text"
          placeholder={placeholder}
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          className="pl-10 pr-10"
          onFocus={() => query.trim() && setIsOpen(true)}
        />
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
        {query && (
          <Button
            type="button"
            variant="ghost"
            size="sm"
            className="absolute right-1 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0"
            onClick={clearSearch}
          >
            <X className="h-4 w-4" />
            <span className="sr-only">Clear search</span>
          </Button>
        )}
        {isLoading && (
          <Loader2 className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 animate-spin" />
        )}
      </form>

      {isOpen && (
        <Card className="absolute top-full mt-1 w-full z-50 max-h-80 overflow-auto shadow-lg">
          {results.length > 0 ? (
            <ul className="py-2 divide-y">
              {results.map((result) => (
                <li key={`${result.type}-${result._id}`}>
                  <Link
                    href={getLink(result)}
                    className="flex items-center px-4 py-2 hover:bg-muted transition-colors"
                    onClick={() => setIsOpen(false)}
                  >
                    <span className="mr-2">{getIcon(result.type)}</span>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium truncate">{getTitle(result)}</p>
                      <p className="text-xs text-muted-foreground">
                        {result.type === "blog" && result.authorId?.name && `By ${result.authorId.name}`}
                        {result.type === "user" && result.email}
                        {result.type === "tool" && new Date(result.timestamp || "").toLocaleString()}
                      </p>
                    </div>
                  </Link>
                </li>
              ))}
              <li className="px-4 py-2">
                <Button
                  variant="ghost"
                  className="w-full text-sm text-muted-foreground"
                  onClick={() => {
                    router.push(`/admin/search?q=${encodeURIComponent(query)}`);
                    setIsOpen(false);
                  }}
                >
                  View all results
                </Button>
              </li>
            </ul>
          ) : (
            <div className="p-4 text-center text-sm text-muted-foreground">
              {error ? error : "No results found"}
            </div>
          )}
        </Card>
      )}
    </div>
  );
}
