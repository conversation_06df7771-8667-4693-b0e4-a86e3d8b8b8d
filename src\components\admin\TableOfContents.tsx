'use client';
import { useEffect, useState } from "react";

export default function TableOfContents({ content }: { content: string }) {
  const [headings, setHeadings] = useState<{ id: string; text: string; level: number }[]>([]);

  useEffect(() => {
    const parser = new DOMParser();
    const doc = parser.parseFromString(content, 'text/html');
    const headings = Array.from(doc.querySelectorAll('h2, h3'));
    
    const parsedHeadings = headings.map((heading) => ({
      id: heading.id || heading.textContent?.toLowerCase().replace(/\s+/g, '-') || '',
      text: heading.textContent || '',
      level: parseInt(heading.tagName.substring(1)),
    }));

    setHeadings(parsedHeadings);
  }, [content]);

  return (
    <div className="sticky top-8 p-4 border rounded-lg bg-gray-50">
      <h3 className="text-lg font-semibold mb-4">Table of Contents</h3>
      <nav className="space-y-2">
        {headings.map((heading) => (
          <a
            key={heading.id}
            href={`#${heading.id}`}
            className={`block text-sm ${heading.level === 3 ? 'ml-4' : ''} 
              text-gray-600 hover:text-blue-600 transition-colors`}
          >
            {heading.text}
          </a>
        ))}
      </nav>
    </div>
  );
}