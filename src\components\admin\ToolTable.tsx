'use client';
import { ColumnDef } from '@tanstack/react-table';
import { MoreHorizontal } from 'lucide-react';
import { Button } from '@/components/New-UI/Button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { DataTable } from '@/components/ui/data-table';
import { Tool } from '@/types/tool';


// export type Tool = {
//   _id: string;
//   name: string;
//   description: string;
//   category: string;
//   createdAt: string;
// };

export const columns: ColumnDef<Tool>[] = [
  {
    accessorKey: 'name',
    header: 'Name',
  },
  {
    accessorKey: 'description',
    header: 'Description',
  },
  {
    accessorKey: 'category',
    header: 'Category',
  },
  {
    accessorKey: 'createdAt',
    header: 'Created At',
    cell: ({ row }) => new Date(row.getValue('createdAt')).toLocaleDateString(),
  },
  {
    id: 'actions',
    cell: ({ row }) => (
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" className="h-8 w-8 p-0">
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuItem asChild>
            <a href={`/admin/tools/${row.original._id}`}>Edit</a>
          </DropdownMenuItem>
          <DropdownMenuItem className="text-red-600">
            Delete
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    ),
  },
];

export function ToolsTable({ data }: { data: Tool[] }) {
  return (
    <DataTable<Tool>
      columns={columns}
      data={data}
      searchKey="name"
      filters={[
        {
          id: 'category',
          title: 'Category',
          options: [
            { label: 'Conversion', value: 'conversion' },
            { label: 'Compression', value: 'compression' },
            { label: 'Security', value: 'security' },
          ]
        }
      ]}
    />
  );
}