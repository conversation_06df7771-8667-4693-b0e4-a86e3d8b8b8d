"use client";

import { useEffect } from 'react';
import { trackToolUsage, trackPageView } from '@/lib/analytics';

interface ToolTrackerProps {
  toolId: string;
  toolName: string;
  children: React.ReactNode;
}

/**
 * A component that tracks tool usage and page views
 * Wrap any tool component with this to automatically track usage
 */
export function ToolTracker({ toolId, toolName, children }: ToolTrackerProps) {
  useEffect(() => {
    // Track page view when the component mounts
    const path = window.location.pathname;
    trackPageView(path);
    
    // Track tool view (started)
    trackToolUsage(toolId, toolName, 'started', {
      path,
      timestamp: new Date().toISOString()
    });
    
    // Return cleanup function to track when user leaves
    return () => {
      console.log(`User left tool: ${toolName}`);
    };
  }, [toolId, toolName]);
  
  return <>{children}</>;
}

/**
 * A hook to track tool completion
 * Call this function when a tool operation completes successfully
 */
export function useToolCompletion(toolId: string, toolName: string) {
  return {
    trackCompletion: (details?: Record<string, any>) => {
      trackToolUsage(toolId, toolName, 'completed', {
        ...details,
        timestamp: new Date().toISOString()
      });
    },
    trackFailure: (error?: any) => {
      trackToolUsage(toolId, toolName, 'failed', {
        error: error?.message || 'Unknown error',
        timestamp: new Date().toISOString()
      });
    }
  };
}

export default ToolTracker;
