"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";

// Export as named export to match import in layout.tsx
export function AuthDebug() {
  const { data: session, status } = useSession();
  const [isOpen, setIsOpen] = useState(false);
  const [cookies, setCookies] = useState<Record<string, string>>({});
  const [auth, setAuth] = useState({
    token: null as string | null,
    lastAuthCheck: null as number | null,
    adminVerified: false,
  });

  useEffect(() => {
    // Parse cookies
    const cookieObj: Record<string, string> = {};
    document.cookie.split(';').forEach(cookie => {
      const [name, value] = cookie.trim().split('=');
      if (name) cookieObj[name] = value || '';
    });
    setCookies(cookieObj);

    // Check localStorage for auth token
    const token = localStorage.getItem('auth_token');
    const lastAuthCheck = localStorage.getItem('last_auth_check');
    const adminVerified = localStorage.getItem('admin_verified') === 'true';

    setAuth({
      token,
      lastAuthCheck: lastAuthCheck ? parseInt(lastAuthCheck) : null,
      adminVerified,
    });
  }, [isOpen]);

  if (!process.env.NODE_ENV || process.env.NODE_ENV === 'production') {
    return null;
  }

  return (
    <div className="fixed bottom-4 right-4 z-50">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="bg-gray-800 text-white px-3 py-1 rounded-md text-sm"
      >
        {isOpen ? "Close Auth Debug" : "Auth Debug"}
      </button>
      
      {isOpen && (
        <div className="bg-white border border-gray-300 rounded-md p-4 mt-2 shadow-lg w-96 max-h-[80vh] overflow-auto">
          <h2 className="font-bold text-lg mb-2">Auth Debug</h2>
          
          <h3 className="font-bold mt-4 mb-2">NextAuth Session:</h3>
          <div className="bg-gray-100 p-2 rounded-md">
            <p><strong>Status:</strong> {status}</p>
            <p><strong>User:</strong> {session?.user?.name || 'Not logged in'}</p>
            <p><strong>Email:</strong> {session?.user?.email || 'N/A'}</p>
            <p><strong>Role:</strong> {(session?.user as any)?.role || 'N/A'}</p>
          </div>
          
          <h3 className="font-bold mt-4 mb-2">Custom Auth:</h3>
          <div className="bg-gray-100 p-2 rounded-md">
            <p><strong>Token:</strong> {auth.token ? '(exists)' : 'None'}</p>
            <p><strong>Admin Verified:</strong> {auth.adminVerified ? 'Yes' : 'No'}</p>
            <p><strong>Last Check:</strong> {auth.lastAuthCheck ? new Date(auth.lastAuthCheck).toLocaleTimeString() : 'Never checked'}</p>
          </div>
          
          <h3 className="font-bold mt-4 mb-2">Cookies:</h3>
          <pre>{JSON.stringify(cookies, null, 2)}</pre>
          
          <h3 className="font-bold mt-4 mb-2">Local Storage:</h3>
          <pre>{JSON.stringify({
            auth_token: localStorage.getItem('auth_token') ? '(exists)' : '(none)'
          }, null, 2)}</pre>
          
          <button 
            onClick={() => {
              localStorage.removeItem('auth_token');
              document.cookie.split(';').forEach(cookie => {
                const name = cookie.split('=')[0].trim();
                document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;
              });
              window.location.reload();
            }}
            className="mt-4 bg-red-600 text-white px-3 py-1 rounded-md text-sm w-full"
          >
            Clear Auth & Reload
          </button>
        </div>
      )}
    </div>
  );
}

// Also export as default for backward compatibility
export default AuthDebug;

