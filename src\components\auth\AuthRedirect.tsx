"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { useSession } from "next-auth/react";

interface AuthRedirectProps {
  /**
   * If true, authenticated users will be redirected away from this page
   */
  redirectAuthenticated?: boolean;

  /**
   * If true, unauthenticated users will be redirected away from this page
   */
  redirectUnauthenticated?: boolean;

  /**
   * Path to redirect authenticated users to (default: "/")
   */
  authenticatedRedirectPath?: string;

  /**
   * Path to redirect unauthenticated users to (default: "/login")
   */
  unauthenticatedRedirectPath?: string;

  /**
   * If true, only admin users can access this page
   */
  requireAdmin?: boolean;

  /**
   * Path to redirect non-admin users to (default: "/unauthorized")
   */
  nonAdminRedirectPath?: string;
}

/**
 * Component that handles authentication-based redirects
 * Can be used to protect routes or redirect users away from auth pages when already logged in
 */
export function AuthRedirect({
  redirectAuthenticated = false,
  redirectUnauthenticated = false,
  authenticatedRedirectPath = "/",
  unauthenticatedRedirectPath = "/login",
  requireAdmin = false,
  nonAdminRedirectPath = "/unauthorized",
}: AuthRedirectProps) {
  const router = useRouter();
  const { data: session, status } = useSession();
  const [isChecking, setIsChecking] = useState(true);

  useEffect(() => {
    // Wait until session is loaded
    if (status === "loading") return;

    // Handle redirects based on authentication status
    if (status === "authenticated" && session) {
      // Check if user is admin when required
      if (requireAdmin && session.user.role !== "admin") {
        router.push(nonAdminRedirectPath);
        return;
      }

      // Redirect authenticated users if needed
      if (redirectAuthenticated) {
        // Redirect admins to admin dashboard, regular users to user dashboard
        const redirectPath = session.user.role === "admin"
          ? "/admin"
          : authenticatedRedirectPath;

        router.push(redirectPath);
        return;
      }
    } else if (status === "unauthenticated") {
      // Redirect unauthenticated users if needed
      if (redirectUnauthenticated || requireAdmin) {
        router.push(unauthenticatedRedirectPath);
        return;
      }
    }

    setIsChecking(false);
  }, [
    status,
    session,
    router,
    redirectAuthenticated,
    redirectUnauthenticated,
    authenticatedRedirectPath,
    unauthenticatedRedirectPath,
    requireAdmin,
    nonAdminRedirectPath
  ]);

  // This component doesn't render anything visible
  return null;
}
