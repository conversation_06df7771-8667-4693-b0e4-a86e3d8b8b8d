"use client";

import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";

interface RequireRoleProps {
  children: React.ReactNode;
  role: "admin" | "user";
  fallbackUrl?: string;
  unauthorizedUrl?: string;
  loadingComponent?: React.ReactNode;
}

export function RequireRole({
  children,
  role,
  fallbackUrl = "/login",
  unauthorizedUrl = "/unauthorized",
  loadingComponent
}: RequireRoleProps) {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [isAuthorized, setIsAuthorized] = useState<boolean | null>(null);

  useEffect(() => {
    if (status === "loading") return; // Still loading

    if (!session) {
      // Not authenticated, redirect to login
      router.push(`${fallbackUrl}?callbackUrl=${encodeURIComponent(window.location.pathname)}`);
      setIsAuthorized(false);
      return;
    }

    // For admin role, check if user is admin
    if (role === "admin" && session.user.role !== "admin") {
      // User doesn't have admin role, redirect to unauthorized
      router.push(unauthorizedUrl);
      setIsAuthorized(false);
      return;
    }

    // For user role, any authenticated user is fine
    // For admin role, we've already checked above
    setIsAuthorized(true);
  }, [session, status, role, router, fallbackUrl, unauthorizedUrl]);

  // Show custom loading component or default loading spinner
  if (status === "loading" || isAuthorized === null) {
    if (loadingComponent) {
      return <>{loadingComponent}</>;
    }

    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="flex flex-col items-center">
          <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-blue-600"></div>
          <p className="mt-4 text-gray-600">Checking authorization...</p>
        </div>
      </div>
    );
  }

  // Don't render children if not authorized
  if (!isAuthorized) {
    return null;
  }

  return <>{children}</>;
}
