"use client";

import { ReactNode } from "react";
import { usePermissions } from "@/hooks/usePermissions";
import { UserRole } from "@/lib/auth/roles";

interface RoleGuardProps {
  children: ReactNode;
  allowedRoles: UserRole[];
  fallback?: ReactNode;
}

/**
 * Component to conditionally render content based on user role
 * 
 * @example
 * <RoleGuard allowedRoles={[UserRole.ADMIN]}>
 *   <AdminOnlyComponent />
 * </RoleGuard>
 */
export function RoleGuard({ 
  children, 
  allowedRoles, 
  fallback = null 
}: RoleGuardProps) {
  const { userRole, isAuthenticated } = usePermissions();
  
  // If user is not authenticated, show fallback
  if (!isAuthenticated) {
    return fallback;
  }
  
  // Check if user's role is in the allowed roles
  const hasAccess = allowedRoles.includes(userRole as UserRole);
  
  // If user has access, show children, otherwise show fallback
  return hasAccess ? <>{children}</> : <>{fallback}</>;
}
