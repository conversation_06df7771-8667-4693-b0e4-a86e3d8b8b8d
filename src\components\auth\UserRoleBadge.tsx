"use client";

import { Badge } from "@/components/ui/badge";
import { UserRole } from "@/lib/auth/roles";

interface UserRoleBadgeProps {
  role: string;
  className?: string;
}

/**
 * Component to display a user's role as a badge
 */
export function UserRoleBadge({ role, className }: UserRoleBadgeProps) {
  // Determine badge variant based on role
  const getBadgeVariant = () => {
    switch (role) {
      case UserRole.ADMIN:
        return "default"; // Primary color
      case UserRole.USER:
      default:
        return "outline"; // Outline style for regular users
    }
  };

  // Get display text for the role
  const getRoleDisplayText = () => {
    switch (role) {
      case UserRole.ADMIN:
        return "Admin";
      case UserRole.USER:
      default:
        return "User";
    }
  };

  return (
    <Badge variant={getBadgeVariant() as any} className={className}>
      {getRoleDisplayText()}
    </Badge>
  );
}
