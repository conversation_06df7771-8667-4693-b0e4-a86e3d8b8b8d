"use client";

import { useAuth } from "@/hooks/useAuth";
import { Badge } from "@/components/ui/badge";
import Link from "next/link";
import { FiUser, FiSettings } from "react-icons/fi";

export function UserRoleDisplay() {
  const { user, isAuthenticated, isAdmin } = useAuth();

  if (!isAuthenticated || !user) {
    return null;
  }

  return (
    <div className="flex items-center space-x-2">
      <div className="flex items-center">
        <FiUser className="mr-1 h-4 w-4 text-gray-500" />
        <span className="text-sm font-medium">{user.name}</span>
      </div>

      {isAdmin && (
        <div className="flex items-center">
          <Badge variant="outline" className="bg-red-100 text-red-800 border-red-200">
            Admin
          </Badge>
          <Link
            href="/admin"
            className="ml-2 text-sm text-blue-600 hover:text-blue-800 flex items-center"
          >
            <FiSettings className="mr-1 h-3 w-3" />
            Admin Panel
          </Link>
        </div>
      )}


    </div>
  );
}
