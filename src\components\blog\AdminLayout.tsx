"use client";

import { ReactNode } from "react";
import AdminLayout from "@/components/admin/AdminLayout";

// This file is deprecated - use the main AdminLayout from components/admin instead
// This file is kept for backward compatibility but redirects to the main AdminLayout

interface DeprecatedAdminLayoutProps {
  children: ReactNode;
  title?: string;
}

export default function DeprecatedBlogAdminLayout({ children, title = "Admin Dashboard" }: DeprecatedAdminLayoutProps) {
  // Forward to the main AdminLayout
  return <AdminLayout title={title}>{children}</AdminLayout>;
}
