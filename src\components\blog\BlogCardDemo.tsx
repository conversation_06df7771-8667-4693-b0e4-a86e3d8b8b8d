'use client';

import { motion } from 'framer-motion';
import { BlogCard } from './BlogCard';

// Demo blog posts with various categories to showcase the enhanced cards
const demoBlogPosts = [
  {
    id: '1',
    _id: '1',
    title: 'The Future of AI in Web Development',
    excerpt: 'Explore how artificial intelligence is revolutionizing the way we build and design websites, from automated code generation to intelligent user experiences.',
    content: '',
    slug: 'future-ai-web-development',
    featuredImage: 'https://images.unsplash.com/photo-1677442136019-21780ecad995?w=800&q=80',
    imageCredit: 'Unsplash',
    categories: ['AI'],
    tags: ['artificial intelligence', 'web development', 'future tech'],
    publishedAt: '2024-01-15T10:00:00Z',
    author: {
      name: '<PERSON>',
      email: '<EMAIL>'
    }
  },
  {
    id: '2',
    _id: '2',
    title: 'Modern Design Principles for 2024',
    excerpt: 'Discover the latest design trends and principles that are shaping user interfaces and experiences in 2024, including minimalism, accessibility, and micro-interactions.',
    content: '',
    slug: 'modern-design-principles-2024',
    featuredImage: 'https://images.unsplash.com/photo-1561070791-2526d30994b5?w=800&q=80',
    imageCredit: 'Unsplash',
    categories: ['Design'],
    tags: ['design', 'ui/ux', 'trends'],
    publishedAt: '2024-01-12T14:30:00Z',
    author: {
      name: 'Sarah Johnson',
      email: '<EMAIL>'
    }
  },
  {
    id: '3',
    _id: '3',
    title: 'Boost Your Productivity with These Tools',
    excerpt: 'A comprehensive guide to the best productivity tools and techniques that can help you streamline your workflow and achieve more in less time.',
    content: '',
    slug: 'boost-productivity-tools',
    featuredImage: 'https://images.unsplash.com/photo-1611224923853-80b023f02d71?w=800&q=80',
    imageCredit: 'Unsplash',
    categories: ['Productivity'],
    tags: ['productivity', 'tools', 'workflow'],
    publishedAt: '2024-01-10T09:15:00Z',
    author: {
      name: 'Mike Rodriguez',
      email: '<EMAIL>'
    }
  },
  {
    id: '4',
    _id: '4',
    title: 'Building Responsive Web Applications',
    excerpt: 'Learn the essential techniques and best practices for creating web applications that work seamlessly across all devices and screen sizes.',
    content: '',
    slug: 'building-responsive-web-apps',
    featuredImage: 'https://images.unsplash.com/photo-1547658719-da2b51169166?w=800&q=80',
    imageCredit: 'Unsplash',
    categories: ['Web'],
    tags: ['responsive design', 'web development', 'mobile'],
    publishedAt: '2024-01-08T16:45:00Z',
    author: {
      name: 'Emma Davis',
      email: '<EMAIL>'
    }
  },
  {
    id: '5',
    _id: '5',
    title: 'The Ultimate Guide to PDF Tools',
    excerpt: 'Everything you need to know about PDF manipulation, from basic editing to advanced features like digital signatures and form creation.',
    content: '',
    slug: 'ultimate-guide-pdf-tools',
    featuredImage: 'https://images.unsplash.com/photo-1586281380349-632531db7ed4?w=800&q=80',
    imageCredit: 'Unsplash',
    categories: ['PDF Tools'],
    tags: ['pdf', 'tools', 'documents'],
    publishedAt: '2024-01-05T11:20:00Z',
    author: {
      name: 'David Kim',
      email: '<EMAIL>'
    }
  },
  {
    id: '6',
    _id: '6',
    title: 'Latest Automotive Technology Trends',
    excerpt: 'Explore the cutting-edge technologies transforming the automotive industry, from electric vehicles to autonomous driving systems.',
    content: '',
    slug: 'automotive-technology-trends',
    featuredImage: 'https://images.unsplash.com/photo-1549317661-bd32c8ce0db2?w=800&q=80',
    imageCredit: 'Unsplash',
    categories: ['Automotive'],
    tags: ['automotive', 'technology', 'electric vehicles'],
    publishedAt: '2024-01-03T13:00:00Z',
    author: {
      name: 'Lisa Wang',
      email: '<EMAIL>'
    }
  }
];

export function BlogCardDemo() {
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2
      }
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background/95 to-background p-8">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, ease: "easeOut" }}
          className="text-center mb-16"
        >
          <h1 className="text-4xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-primary via-purple-500 to-orange-500 bg-clip-text text-transparent">
            Enhanced Blog Cards Demo
          </h1>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            Experience our modern, responsive blog card UI with smooth Framer Motion animations, 
            category-specific icons, and beautiful hover effects.
          </p>
        </motion.div>

        {/* Blog Cards Grid */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 lg:gap-10"
        >
          {demoBlogPosts.map((post, index) => (
            <BlogCard key={post.id} post={post} index={index} />
          ))}
        </motion.div>

        {/* Features Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 1.5 }}
          className="mt-20 text-center"
        >
          <h2 className="text-3xl font-bold mb-8">Enhanced Features</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="p-6 rounded-2xl bg-card border">
              <h3 className="font-semibold mb-2">Category Icons</h3>
              <p className="text-sm text-muted-foreground">Dynamic icons for each category with smooth animations</p>
            </div>
            <div className="p-6 rounded-2xl bg-card border">
              <h3 className="font-semibold mb-2">Hover Effects</h3>
              <p className="text-sm text-muted-foreground">Sophisticated 3D transforms and particle effects</p>
            </div>
            <div className="p-6 rounded-2xl bg-card border">
              <h3 className="font-semibold mb-2">Responsive Design</h3>
              <p className="text-sm text-muted-foreground">Optimized for all screen sizes and devices</p>
            </div>
            <div className="p-6 rounded-2xl bg-card border">
              <h3 className="font-semibold mb-2">Accessibility</h3>
              <p className="text-sm text-muted-foreground">Enhanced focus states and keyboard navigation</p>
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  );
}
