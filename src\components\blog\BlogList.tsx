'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Search } from 'lucide-react';
import { UnifiedBlogCard } from './UnifiedBlogCard';

interface BlogPost {
  id: string;
  _id: string;
  title: string;
  excerpt: string;
  content: string;
  slug: string;
  featuredImage?: string;
  imageCredit?: string;
  categoryId?: string;
  tags: string[];
  publishedAt: string;
  author: {
    name: string;
    email: string;
  };
}

interface BlogListProps {
  showSearch?: boolean;
  showFilters?: boolean;
  limit?: number;
  layout?: 'grid' | 'list';
  searchQuery?: string;
  categoryFilter?: string;
}

export function BlogList({
  showSearch = true,
  showFilters = true,
  limit = 10,
  layout = 'grid',
  searchQuery = '',
  categoryFilter = ''
}: BlogListProps) {
  const [posts, setPosts] = useState<BlogPost[]>([]);
  const [loading, setLoading] = useState(true);
  const [search, setSearch] = useState(searchQuery);
  const [internalCategoryFilter, setInternalCategoryFilter] = useState('all');
  const [page, setPage] = useState(1);
  const [pagination, setPagination] = useState({
    total: 0,
    totalPages: 0,
    hasNext: false,
    hasPrev: false,
  });
  const [categories, setCategories] = useState<any[]>([]);

  // Fetch posts
  const fetchPosts = async () => {
    try {
      setLoading(true);

      const params = new URLSearchParams({
        page: page.toString(),
        limit: limit.toString(),
        admin: 'false', // Only published posts
      });

      // Use external props if provided, otherwise use internal state
      const finalSearch = searchQuery || search;
      const finalCategory = categoryFilter || internalCategoryFilter;

      if (finalSearch) params.append('search', finalSearch);
      if (finalCategory && finalCategory !== 'all') params.append('category', finalCategory);

      const response = await fetch(`/api/posts?${params}`);
      const data = await response.json();

      console.log('Public blog posts:', data);

      if (data.success) {
        // Ensure data is an array and normalize structure
        const postsData = Array.isArray(data.data) ? data.data : [];
        console.log('Normalized posts:', postsData);
        const normalizedPosts = postsData.map((post: any) => ({
          ...post,
          _id: post._id.toString(),
          categories: post.categories || (post.category ? [post.category] : []),
          tags: post.tags || [],
          excerpt: post.excerpt || post.description || '',
          featuredImage: post.featuredImage || '',
          author: post.author || { name: 'Unknown', email: '' }
        }));

        setPosts(normalizedPosts);
        setPagination(data.pagination || {
          total: 0,
          totalPages: 0,
          hasNext: false,
          hasPrev: false,
        });
      } else {
        console.error('Failed to fetch posts:', data.error);
        setPosts([]);
        setPagination({
          total: 0,
          totalPages: 0,
          hasNext: false,
          hasPrev: false,
        });
      }
    } catch (error) {
      console.error('Error fetching posts:', error);
      setPosts([]);
      setPagination({
        total: 0,
        totalPages: 0,
        hasNext: false,
        hasPrev: false,
      });
    } finally {
      setLoading(false);
    }
  };

  // Fetch categories
  const fetchCategories = async () => {
    try {
      const response = await fetch('/api/categories');
      const data = await response.json();

      if (data.success) {
        setCategories(data.data || []);
      }
    } catch (error) {
      console.error('Error fetching categories:', error);
    }
  };



  useEffect(() => {
    fetchPosts();
    if (showFilters) {
      fetchCategories();
    }
  }, [page, search, internalCategoryFilter, limit, searchQuery, categoryFilter]);

  // Debounce search
  useEffect(() => {
    const timer = setTimeout(() => {
      setPage(1); // Reset to first page when searching
      fetchPosts();
    }, 300);

    return () => clearTimeout(timer);
  }, [search]);

  return (
    <div className="space-y-6">
      {/* Header and Filters */}
      {(showSearch || showFilters) && (
        <Card>
          <CardHeader>
            <CardTitle>Blog Posts</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {showSearch && (
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search blog posts..."
                  value={search}
                  onChange={(e) => setSearch(e.target.value)}
                  className="pl-10"
                />
              </div>
            )}

            {showFilters && categories.length > 0 && (
              <div className="flex flex-wrap gap-2">
                <Button
                  variant={internalCategoryFilter === 'all' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setInternalCategoryFilter('all')}
                >
                  All Categories
                </Button>
                {categories.map((category) => (
                  <Button
                    key={category._id}
                    variant={internalCategoryFilter === category._id ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setInternalCategoryFilter(category._id)}
                  >
                    {category.name}
                  </Button>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Posts */}
      {loading ? (
        <div className="text-center py-12">
          <div className="inline-block h-8 w-8 animate-spin rounded-full border-4 border-solid border-current border-r-transparent"></div>
          <p className="mt-4 text-muted-foreground">Loading blog posts...</p>
        </div>
      ) : posts.length > 0 ? (
        <>
          <div className={layout === 'grid'
            ? "grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 max-w-7xl mx-auto"
            : "space-y-6 max-w-4xl mx-auto"
          }>
            {posts.map((post, index) => (
              <UnifiedBlogCard
                key={post.id}
                post={post}
                index={index}
                showAnimation={false}
              />
            ))}
          </div>

          {/* Pagination */}
          {pagination.totalPages > 1 && (
            <div className="flex items-center justify-center gap-4">
              <Button
                variant="outline"
                onClick={() => setPage(page - 1)}
                disabled={!pagination.hasPrev}
              >
                Previous
              </Button>

              <span className="text-sm text-muted-foreground">
                Page {page} of {pagination.totalPages}
              </span>

              <Button
                variant="outline"
                onClick={() => setPage(page + 1)}
                disabled={!pagination.hasNext}
              >
                Next
              </Button>
            </div>
          )}
        </>
      ) : (
        <Card>
          <CardContent className="text-center py-12">
            <p className="text-muted-foreground">No blog posts found</p>
            {search && (
              <p className="text-sm text-muted-foreground mt-2">
                Try adjusting your search terms or filters
              </p>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
}
