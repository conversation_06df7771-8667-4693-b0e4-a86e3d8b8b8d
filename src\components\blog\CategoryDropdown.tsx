'use client';

import React, { useState, useEffect } from 'react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Plus, Loader2 } from 'lucide-react';
import { toast } from '@/hooks/use-toast';

interface Category {
  _id: string;
  name: string;
  slug: string;
  count?: number;
}

interface CategoryDropdownProps {
  selectedCategoryId: string;
  onCategoryChange: (categoryId: string) => void;
  placeholder?: string;
  className?: string;
}

export function CategoryDropdown({
  selectedCategoryId = "",
  onCategoryChange,
  placeholder = "Select category",
  className = "",
}: CategoryDropdownProps) {
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(false);
  const [newCategoryName, setNewCategoryName] = useState('');
  const [isCreating, setIsCreating] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Check for potential duplicates as user types
  const potentialDuplicate = newCategoryName.trim() ? categories.find(
    cat => cat.name.toLowerCase() === newCategoryName.trim().toLowerCase()
  ) : null;

  // Get selected category for display
  const selectedCategory = categories.find(cat => cat._id === selectedCategoryId);

  const fetchCategories = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await fetch('/api/categories');

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      // Handle both direct array and wrapped response formats
      if (Array.isArray(data)) {
        setCategories(data);
      } else if (data.success && Array.isArray(data.data)) {
        setCategories(data.data);
      } else if (data.error) {
        throw new Error(data.error);
      } else {
        throw new Error('Invalid response format');
      }
    } catch (error) {
      console.error('Error fetching categories:', error);
      setError(error instanceof Error ? error.message : 'Failed to fetch categories');
      toast({
        title: 'Error',
        description: 'Failed to fetch categories',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const createCategory = async () => {
    const trimmedName = newCategoryName.trim();

    if (!trimmedName) {
      toast({
        title: 'Error',
        description: 'Category name cannot be empty',
        variant: 'destructive',
      });
      return;
    }

    // Validate category name length and characters
    if (trimmedName.length < 2) {
      toast({
        title: 'Error',
        description: 'Category name must be at least 2 characters long',
        variant: 'destructive',
      });
      return;
    }

    if (trimmedName.length > 50) {
      toast({
        title: 'Error',
        description: 'Category name cannot exceed 50 characters',
        variant: 'destructive',
      });
      return;
    }

    // Check if category already exists (case-insensitive)
    const existingCategory = categories.find(
      cat => cat.name.toLowerCase() === trimmedName.toLowerCase()
    );

    if (existingCategory) {
      // Offer to select the existing category instead
      toast({
        title: 'Category Already Exists',
        description: `"${existingCategory.name}" already exists. Selecting it for you.`,
      });
      onCategoryChange(existingCategory._id);
      setNewCategoryName('');
      return;
    }

    try {
      setIsCreating(true);
      const response = await fetch('/api/categories', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ name: trimmedName }),
      });

      if (!response.ok) {
        const errorData = await response.json();

        // Handle specific error cases
        if (response.status === 409) {
          // Category already exists on server
          if (errorData.existingCategory) {
            // Use the existing category info from server response
            const existingCat = errorData.existingCategory;

            // Add to categories list if not already present
            setCategories(prev => {
              const exists = prev.find(cat => cat._id === existingCat._id);
              if (!exists) {
                return [...prev, existingCat];
              }
              return prev;
            });

            // Select the existing category
            onCategoryChange(existingCat._id);
            toast({
              title: 'Category Already Exists',
              description: `"${existingCat.name}" already exists. Selected it for you.`,
            });
          } else {
            // Fallback: refresh categories and try to find it
            await fetchCategories();
            const refreshedExisting = categories.find(
              cat => cat.name.toLowerCase() === trimmedName.toLowerCase()
            );

            if (refreshedExisting) {
              onCategoryChange(refreshedExisting._id);
              toast({
                title: 'Category Already Exists',
                description: `"${refreshedExisting.name}" already exists. Selected it for you.`,
              });
            } else {
              toast({
                title: 'Error',
                description: 'Category already exists but could not be found',
                variant: 'destructive',
              });
            }
          }
          setNewCategoryName('');
          return;
        } else if (response.status === 401) {
          toast({
            title: 'Permission Denied',
            description: 'You need admin privileges to create categories',
            variant: 'destructive',
          });
          return;
        } else {
          throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
        }
      }

      const newCategory = await response.json();

      // Update categories list
      setCategories(prev => [...prev, newCategory]);

      // Auto-select the newly created category
      onCategoryChange(newCategory._id);

      setNewCategoryName('');
      toast({
        title: 'Success',
        description: `Category "${newCategory.name}" created and selected`,
      });
    } catch (error) {
      console.error('Error creating category:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to create category',
        variant: 'destructive',
      });
    } finally {
      setIsCreating(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      createCategory();
    }
  };

  useEffect(() => {
    fetchCategories();
  }, []);

  return (
    <div className={`space-y-3 ${className}`}>
      {/* Category Select */}
      <Select value={selectedCategoryId} onValueChange={onCategoryChange}>
        <SelectTrigger className="w-full">
          <SelectValue placeholder={placeholder} />
        </SelectTrigger>
        <SelectContent>
          {loading ? (
            <div className="p-4 text-center">
              <Loader2 className="h-4 w-4 animate-spin mx-auto mb-2" />
              <p className="text-sm text-muted-foreground">Loading categories...</p>
            </div>
          ) : error ? (
            <div className="p-4 text-center">
              <p className="text-sm text-destructive mb-2">Error: {error}</p>
              <Button
                size="sm"
                variant="outline"
                onClick={fetchCategories}
                className="text-xs"
              >
                Retry
              </Button>
            </div>
          ) : categories.length > 0 ? (
            categories.map((category) => (
              <SelectItem key={category._id} value={category._id}>
                {category.name}
              </SelectItem>
            ))
          ) : (
            <div className="p-4 text-center text-sm text-muted-foreground">
              No categories found
            </div>
          )}
        </SelectContent>
      </Select>

      {/* Create New Category */}
      <div className="space-y-2">
        <div className="flex gap-2">
          <Input
            placeholder="Create new category"
            value={newCategoryName}
            onChange={(e) => setNewCategoryName(e.target.value)}
            onKeyDown={handleKeyDown}
            className={`flex-1 ${potentialDuplicate ? 'border-yellow-500 focus:border-yellow-500' : ''}`}
            disabled={isCreating}
          />
          <Button
            size="sm"
            onClick={createCategory}
            disabled={isCreating || !newCategoryName.trim()}
            type="button"
          >
            {isCreating ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <Plus className="h-4 w-4" />
            )}
          </Button>
        </div>

        {/* Duplicate warning */}
        {potentialDuplicate && (
          <div className="text-sm text-yellow-600 dark:text-yellow-400 flex items-center gap-1">
            <span>⚠️</span>
            <span>
              Category "{potentialDuplicate.name}" already exists.
              <button
                type="button"
                onClick={() => {
                  onCategoryChange(potentialDuplicate._id);
                  setNewCategoryName('');
                  toast({
                    title: 'Category Selected',
                    description: `Selected existing category "${potentialDuplicate.name}"`,
                  });
                }}
                className="ml-1 underline hover:no-underline"
              >
                Select it?
              </button>
            </span>
          </div>
        )}
      </div>

      {/* Selected Category Display */}
      {selectedCategory && (
        <div className="text-sm text-muted-foreground">
          Selected: <span className="font-medium">{selectedCategory.name}</span>
        </div>
      )}
    </div>
  );
}
