'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Separator } from '@/components/ui/separator';
import { toast } from '@/hooks/use-toast';
import { formatDistanceToNow } from 'date-fns';
import { MessageSquare, ThumbsUp, Reply, AlertCircle, Loader2 } from 'lucide-react';

interface Comment {
  _id: string;
  content: string;
  authorName: string;
  authorEmail: string;
  authorWebsite?: string;
  createdAt: string;
  isAdmin: boolean;
  userId?: string;
  role?: string;
  replies?: Comment[];
}

interface CommentSectionProps {
  postId: string;
  postTitle: string;
}

export function CommentSection({ postId, postTitle }: CommentSectionProps) {
  const { data: session, status } = useSession();
  const [comments, setComments] = useState<Comment[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [replyTo, setReplyTo] = useState<string | null>(null);

  // Form state
  const [content, setContent] = useState('');

  // Log session data when it changes
  useEffect(() => {
    if (session) {
      console.log('Session data:', JSON.stringify(session, null, 2));
      console.log('User data:', session.user);
    }
  }, [session]);

  // Fetch comments on component mount
  useEffect(() => {
    async function fetchComments() {
      try {
        setIsLoading(true);
        console.log(`Fetching comments for postId: ${postId}`);
        const response = await fetch(`/api/blog/comments?postId=${postId}&status=approved`);

        if (!response.ok) {
          const errorData = await response.json();
          console.error(`Failed to fetch comments: ${response.status}`, errorData);
          throw new Error(`Failed to fetch comments: ${response.status}`);
        }

        const data = await response.json();
        console.log(`Received ${data.comments?.length || 0} comments`);
        setComments(data.comments || []);
      } catch (error) {
        console.error('Error fetching comments:', error);
        toast({
          title: 'Error',
          description: 'Failed to load comments. Please try again later.',
          variant: 'destructive',
        });
      } finally {
        setIsLoading(false);
      }
    }

    if (postId) {
      console.log(`CommentSection mounted with postId: ${postId}`);
      fetchComments();
    } else {
      console.error('No postId provided to CommentSection');
    }
  }, [postId]);



  // Handle comment submission
  const handleSubmitComment = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!content.trim()) {
      toast({
        title: 'Error',
        description: 'Please enter a comment.',
        variant: 'destructive',
      });
      return;
    }

    // We don't need to check for login anymore since we always show the comment form

    try {
      setIsSubmitting(true);

      // Get user data from session
      const userData = session?.user;
      console.log('User data for comment:', userData);

      // Make sure we have a valid email and name (required by the API)
      const userEmail = userData?.email || '<EMAIL>';
      const userName = userData?.name || 'Anonymous';

      // Log the user information we're using
      console.log(`Using name: "${userName}", email: "${userEmail}"`);

      const commentData = {
        content,
        postId,
        authorName: userName,
        authorEmail: userEmail,
        parentId: replyTo || undefined,
      };

      // Log the data being sent for debugging
      console.log('Submitting comment with data:', JSON.stringify(commentData));

      const response = await fetch('/api/blog/comments', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(commentData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        console.error('API error response:', errorData);

        // Format validation errors if available
        if (errorData.details && errorData.details.issues) {
          const validationErrors = errorData.details.issues
            .map((issue: any) => `${issue.path.join('.')}: ${issue.message}`)
            .join(', ');
          throw new Error(`Validation failed: ${validationErrors}`);
        }

        throw new Error(errorData.error || 'Failed to submit comment');
      }

      const newComment = await response.json();

      console.log('New comment created:', newComment);

      // If it's a reply, add it to the parent comment's replies
      if (replyTo) {
        setComments(prevComments =>
          prevComments.map(comment =>
            comment._id === replyTo
              ? {
                  ...comment,
                  replies: [...(comment.replies || []), newComment]
                }
              : comment
          )
        );
        setReplyTo(null);
      } else {
        // Add the new comment to the list
        setComments(prevComments => [newComment, ...prevComments]);
      }

      // Show success message
      toast({
        title: 'Comment Posted',
        description: 'Your comment has been added successfully.',
      });

      // Reset form
      setContent('');
    } catch (error) {
      console.error('Error submitting comment:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to submit comment. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle reply button click
  const handleReply = (commentId: string, authorName: string) => {
    setReplyTo(commentId);
    // Store the name of the person being replied to in session storage
    sessionStorage.setItem('replyToName', authorName);
    // Scroll to comment form
    document.getElementById('comment-form')?.scrollIntoView({ behavior: 'smooth' });
  };

  // Get the name of the person being replied to
  const getReplyToName = () => {
    if (!replyTo) return '';

    // Try to get from session storage first
    const storedName = sessionStorage.getItem('replyToName');
    if (storedName) return storedName;

    // Otherwise find in comments
    const comment = comments.find(c => c._id === replyTo);
    if (comment) return comment.authorName;

    // Check in replies
    for (const parentComment of comments) {
      if (parentComment.replies) {
        const reply = parentComment.replies.find(r => r._id === replyTo);
        if (reply) return reply.authorName;
      }
    }

    return '';
  };

  return (
    <div className="mt-10 space-y-8">
      <div className="flex items-center gap-2">
        <MessageSquare className="h-5 w-5" />
        <h3 className="text-xl font-semibold">Comments ({comments.length})</h3>
      </div>

      {isLoading ? (
        <div className="flex justify-center py-8">
          <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
        </div>
      ) : comments.length === 0 ? (
        <div className="flex flex-col items-center justify-center py-8 text-center">
          <AlertCircle className="h-12 w-12 text-muted-foreground mb-2" />
          <h4 className="text-lg font-medium">No comments yet</h4>
          <p className="text-muted-foreground">Be the first to share your thoughts!</p>
        </div>
      ) : (
        <div className="space-y-6">
          {comments.map((comment) => (
            <div
              key={comment._id}
              className={`rounded-lg border p-4 shadow-sm ${
                comment.isAdmin ? 'bg-primary/5 border-primary/20' : ''
              }`}
            >
              <div className="flex gap-3">
                <Avatar className="h-10 w-10">
                  <AvatarImage src={`https://ui-avatars.com/api/?name=${encodeURIComponent(comment.authorName)}&background=random`} />
                  <AvatarFallback>{comment.authorName.substring(0, 2).toUpperCase()}</AvatarFallback>
                </Avatar>
                <div className="flex-1 space-y-1.5">
                  <div className="flex items-center gap-2">
                    <h4 className="font-semibold">{comment.authorName}</h4>
                    {comment.isAdmin && (
                      <span className="rounded bg-primary/20 px-2 py-0.5 text-xs font-bold text-primary">
                        Admin
                      </span>
                    )}
                    {!comment.isAdmin && comment.role && comment.role !== 'user' && (
                      <span className="rounded bg-secondary/20 px-2 py-0.5 text-xs font-medium text-secondary">
                        {comment.role}
                      </span>
                    )}
                  </div>
                  <p className="text-sm text-muted-foreground">
                    {formatDistanceToNow(new Date(comment.createdAt), { addSuffix: true })}
                  </p>
                  <p className="mt-2">{comment.content}</p>
                  <div className="mt-3 flex items-center gap-4">
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-8 px-2 text-xs flex items-center gap-1 hover:bg-muted"
                      onClick={() => handleReply(comment._id, comment.authorName)}
                    >
                      <Reply className="h-4 w-4 mr-1" />
                      Reply
                    </Button>
                  </div>
                </div>
              </div>

              {/* Replies */}
              {comment.replies && comment.replies.length > 0 && (
                <div className="mt-4 ml-12 space-y-4">
                  {comment.replies.map((reply) => (
                    <div
                      key={reply._id}
                      className={`rounded-lg border p-3 shadow-sm ${
                        reply.isAdmin ? 'bg-primary/5 border-primary/20' : ''
                      }`}
                    >
                      <div className="flex gap-3">
                        <Avatar className="h-8 w-8">
                          <AvatarImage src={`https://ui-avatars.com/api/?name=${encodeURIComponent(reply.authorName)}&background=random`} />
                          <AvatarFallback>{reply.authorName.substring(0, 2).toUpperCase()}</AvatarFallback>
                        </Avatar>
                        <div className="flex-1 space-y-1">
                          <div className="flex items-center gap-2">
                            <h4 className="font-semibold">{reply.authorName}</h4>
                            {reply.isAdmin && (
                              <span className="rounded bg-primary/20 px-2 py-0.5 text-xs font-bold text-primary">
                                Admin
                              </span>
                            )}
                            {!reply.isAdmin && reply.role && reply.role !== 'user' && (
                              <span className="rounded bg-secondary/20 px-2 py-0.5 text-xs font-medium text-secondary">
                                {reply.role}
                              </span>
                            )}
                          </div>
                          <p className="text-sm text-muted-foreground">
                            {formatDistanceToNow(new Date(reply.createdAt), { addSuffix: true })}
                          </p>
                          <p className="mt-2">{reply.content}</p>
                          <div className="mt-2 flex items-center gap-4">
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-7 px-2 text-xs flex items-center gap-1 hover:bg-muted"
                              onClick={() => handleReply(comment._id, comment.authorName)}
                            >
                              <Reply className="h-3 w-3 mr-1" />
                              Reply to Thread
                            </Button>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          ))}
        </div>
      )}

      <Separator className="my-8" />

      <div id="comment-form" className="space-y-4">
        {replyTo && (
          <div className="flex items-center gap-2 rounded-lg bg-muted p-3 border border-border">
            <Reply className="h-4 w-4 text-muted-foreground" />
            <p className="text-sm">
              Replying to <span className="font-semibold">{getReplyToName()}</span>
              <button
                className="ml-2 text-primary hover:underline"
                onClick={() => {
                  setReplyTo(null);
                  sessionStorage.removeItem('replyToName');
                }}
              >
                Cancel
              </button>
            </p>
          </div>
        )}

        <div className="flex gap-3">
          <Avatar className="h-10 w-10">
            {session?.user?.image ? (
              <AvatarImage src={session.user.image} alt={session.user.name || 'User'} />
            ) : (
              <AvatarImage src={`https://ui-avatars.com/api/?name=${encodeURIComponent(session?.user?.name || 'User')}&background=random`} />
            )}
            <AvatarFallback>{(session?.user?.name || 'U').substring(0, 2).toUpperCase()}</AvatarFallback>
          </Avatar>
          <div className="flex-1">
            <form onSubmit={handleSubmitComment} className="space-y-3">
              <Textarea
                id="comment"
                value={content}
                onChange={(e) => setContent(e.target.value)}
                placeholder={replyTo ? `Reply to ${getReplyToName()}...` : "Add a comment..."}
                rows={2}
                className="resize-none"
                required
              />
              <div className="flex justify-end">
                {replyTo && (
                  <Button
                    type="button"
                    variant="ghost"
                    onClick={() => {
                      setReplyTo(null);
                      sessionStorage.removeItem('replyToName');
                    }}
                    className="mr-2"
                  >
                    Cancel
                  </Button>
                )}
                <Button
                  type="submit"
                  disabled={isSubmitting || !content.trim()}
                  className={`${session?.user?.role === 'admin' ? 'bg-primary hover:bg-primary/90' : ''}`}
                >
                  {isSubmitting ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Submitting...
                    </>
                  ) : (
                    replyTo ? 'Reply' : 'Comment'
                  )}
                </Button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
}
