'use client';

import { motion } from 'framer-motion';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { ArrowRight, FileText, Image, Zap, Globe } from 'lucide-react';
import Link from 'next/link';
import { useTheme } from '@/hooks/useTheme';
import { ALL_TOOLS } from '@/data/tools';

export function FeaturedTools() {
  const { theme } = useTheme();
  const isDark = theme === 'dark';

  // Get featured tools (popular ones)
  const featuredTools = ALL_TOOLS.filter(tool => tool.popular).slice(0, 4);

  // Icon mapping for tools
  const getToolIcon = (toolId: string) => {
    const iconMap: Record<string, JSX.Element> = {
      'compress-pdf': <Zap className="h-6 w-6" />,
      'merge-pdf': <FileText className="h-6 w-6" />,
      'pdf-to-word': <FileText className="h-6 w-6" />,
      'word-to-pdf': <FileText className="h-6 w-6" />,
      'excel-to-pdf': <FileText className="h-6 w-6" />,
      'powerpoint-to-pdf': <FileText className="h-6 w-6" />,
      'jpg-to-pdf': <Image className="h-6 w-6" />,
      'html-to-pdf': <Globe className="h-6 w-6" />,
    };
    return iconMap[toolId] || <FileText className="h-6 w-6" />;
  };

  // Color mapping for tool categories
  const getCategoryColor = (category: string) => {
    const colors = {
      'pdf': isDark ? 'from-blue-500/20 to-blue-600/20 border-blue-500/30' : 'from-blue-50 to-blue-100 border-blue-200',
      'office': isDark ? 'from-purple-500/20 to-purple-600/20 border-purple-500/30' : 'from-purple-50 to-purple-100 border-purple-200',
      'image': isDark ? 'from-green-500/20 to-green-600/20 border-green-500/30' : 'from-green-50 to-green-100 border-green-200',
      'web': isDark ? 'from-orange-500/20 to-orange-600/20 border-orange-500/30' : 'from-orange-50 to-orange-100 border-orange-200',
    };
    return colors[category as keyof typeof colors] || (isDark ? 'from-gray-500/20 to-gray-600/20 border-gray-500/30' : 'from-gray-50 to-gray-100 border-gray-200');
  };

  const getIconColor = (category: string) => {
    const colors = {
      'pdf': isDark ? 'text-blue-400' : 'text-blue-600',
      'office': isDark ? 'text-purple-400' : 'text-purple-600',
      'image': isDark ? 'text-green-400' : 'text-green-600',
      'web': isDark ? 'text-orange-400' : 'text-orange-600',
    };
    return colors[category as keyof typeof colors] || (isDark ? 'text-gray-400' : 'text-gray-600');
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.5
      }
    }
  };

  return (
    <section className={`py-20 relative overflow-hidden ${isDark ? 'bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900' : 'bg-gradient-to-br from-gray-50 via-white to-gray-50'}`}>
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute inset-0" style={{
          backgroundImage: `radial-gradient(circle at 2px 2px, currentColor 1px, transparent 0)`,
          backgroundSize: '30px 30px'
        }} />
      </div>

      <div className="container mx-auto px-4 relative">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8 }}
          className="text-center mb-16"
        >
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            whileInView={{ opacity: 1, scale: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="inline-flex items-center gap-3 px-6 py-3 rounded-full bg-primary/10 text-primary text-sm font-semibold mb-6 backdrop-blur-sm border border-primary/20"
          >
            <motion.div
              animate={{
                scale: [1, 1.2, 1],
                rotate: [0, 180, 360]
              }}
              transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
            >
              <Zap className="h-5 w-5" />
            </motion.div>
            Featured Tools
          </motion.div>

          <motion.h2
            className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8, delay: 0.4 }}
          >
            <span className="bg-gradient-to-r from-primary via-purple-500 to-orange-500 bg-clip-text text-transparent">
              🛠️ Useful Tools
            </span>
            <br />
            <span className={isDark ? 'text-gray-100' : 'text-gray-900'}>
              Related to This Post
            </span>
          </motion.h2>

          <motion.p
            className="text-muted-foreground text-xl max-w-3xl mx-auto leading-relaxed"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8, delay: 0.6 }}
          >
            Discover powerful tools that complement your reading experience and boost your productivity
          </motion.p>
        </motion.div>

        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8"
        >
          {featuredTools.map((tool, index) => (
            <motion.div
              key={tool.id}
              variants={itemVariants}
              whileHover={{
                scale: 1.05,
                y: -12,
                transition: { duration: 0.3, ease: "easeOut" }
              }}
              whileTap={{ scale: 0.98 }}
              className="group h-full"
            >
              <Link href={`/tools/${tool.id}`}>
                <div className={`
                  h-full p-8 rounded-3xl border transition-all duration-500 group-hover:shadow-2xl
                  bg-gradient-to-br ${getCategoryColor(tool.category)}
                  group-hover:border-primary/40 backdrop-blur-sm
                  relative overflow-hidden group-hover:shadow-primary/10
                `}>
                  {/* Shimmer Effect */}
                  <motion.div
                    className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent opacity-0 group-hover:opacity-100"
                    animate={{
                      x: ['-100%', '100%'],
                    }}
                    transition={{
                      duration: 1.5,
                      repeat: Infinity,
                      repeatDelay: 3,
                    }}
                  />
                  {/* Enhanced Icon */}
                  <motion.div
                    whileHover={{
                      scale: 1.2,
                      rotate: 10
                    }}
                    transition={{ duration: 0.3, type: "spring", stiffness: 200 }}
                    className={`
                      w-16 h-16 rounded-2xl bg-white/20 backdrop-blur-md flex items-center justify-center mb-6
                      ${getIconColor(tool.category)} shadow-lg group-hover:shadow-xl
                      border border-white/10
                    `}
                  >
                    {getToolIcon(tool.id)}
                  </motion.div>

                  {/* Enhanced Content */}
                  <motion.h3
                    className={`
                      text-xl font-bold mb-3 group-hover:text-primary transition-colors duration-300
                      ${isDark ? 'text-gray-100' : 'text-gray-900'}
                      leading-tight
                    `}
                    initial={{ opacity: 0, y: 10 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1 + 0.3 }}
                  >
                    {tool.title}
                  </motion.h3>

                  <motion.p
                    className={`
                      text-sm mb-6 line-clamp-3 leading-relaxed
                      ${isDark ? 'text-gray-300' : 'text-gray-600'}
                    `}
                    initial={{ opacity: 0, y: 10 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1 + 0.4 }}
                  >
                    {tool.description}
                  </motion.p>

                  {/* Enhanced Format badges */}
                  <motion.div
                    className="flex items-center gap-3 text-xs"
                    initial={{ opacity: 0, y: 10 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1 + 0.5 }}
                  >
                    {tool.inputFormat && (
                      <span className={`
                        px-3 py-2 rounded-full font-medium backdrop-blur-sm
                        ${isDark ? 'bg-gray-700/50 text-gray-300 border border-gray-600/50' : 'bg-gray-200/50 text-gray-700 border border-gray-300/50'}
                      `}>
                        {tool.inputFormat}
                      </span>
                    )}
                    {tool.inputFormat && tool.outputFormat && (
                      <motion.div
                        animate={{ x: [0, 3, 0] }}
                        transition={{ duration: 2, repeat: Infinity }}
                      >
                        <ArrowRight className="h-4 w-4 text-muted-foreground" />
                      </motion.div>
                    )}
                    {tool.outputFormat && (
                      <span className={`
                        px-3 py-2 rounded-full font-medium backdrop-blur-sm
                        ${isDark ? 'bg-gray-700/50 text-gray-300 border border-gray-600/50' : 'bg-gray-200/50 text-gray-700 border border-gray-300/50'}
                      `}>
                        {tool.outputFormat}
                      </span>
                    )}
                  </motion.div>
                </div>
              </Link>
            </motion.div>
          ))}
        </motion.div>

        {/* Enhanced View All Tools Button */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8, delay: 0.6 }}
          className="text-center mt-16"
        >
          <Link href="/tools">
            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <Button
                size="lg"
                variant="outline"
                className={`
                  rounded-full px-12 py-8 text-xl font-bold group transition-all duration-500
                  ${isDark
                    ? 'border-gray-600 hover:border-primary/50 hover:bg-primary/10 hover:shadow-lg hover:shadow-primary/20'
                    : 'border-gray-300 hover:border-primary/50 hover:bg-primary/5 hover:shadow-lg hover:shadow-primary/20'
                  }
                  backdrop-blur-sm
                `}
              >
                <span className="bg-gradient-to-r from-primary to-purple-500 bg-clip-text text-transparent">
                  View All Tools
                </span>
                <motion.div
                  animate={{ x: [0, 5, 0] }}
                  transition={{ duration: 2, repeat: Infinity }}
                  className="ml-3"
                >
                  <ArrowRight className="h-6 w-6 text-primary" />
                </motion.div>
              </Button>
            </motion.div>
          </Link>
        </motion.div>
      </div>
    </section>
  );
}
