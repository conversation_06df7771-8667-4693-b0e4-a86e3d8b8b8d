"use client";

import { useState, useEffect, useRef } from "react";
import { motion } from "framer-motion";
import { UnifiedBlogCard } from "./UnifiedBlogCard";

interface BlogPost {
  id: string;
  title: string;
  slug: string;
  excerpt?: string;
  description?: string;
  featuredImage?: string;
  image?: string;
  publishedAt: string;
  createdAt?: string;
  author: {
    name: string;
    email: string;
  } | string;
  category?: string;
  imageCredit?: string;
}

interface MasonryLayoutProps {
  posts: BlogPost[];
  loading?: boolean;
  showAnimation?: boolean;
}

export function MasonryLayout({ posts, loading = false, showAnimation = true }: MasonryLayoutProps) {
  const [columns, setColumns] = useState(3);
  const [columnPosts, setColumnPosts] = useState<BlogPost[][]>([]);
  const containerRef = useRef<HTMLDivElement>(null);

  // Responsive column calculation
  useEffect(() => {
    const updateColumns = () => {
      const width = window.innerWidth;
      if (width < 640) {
        setColumns(1);
      } else if (width < 1024) {
        setColumns(2);
      } else if (width < 1536) {
        setColumns(3);
      } else {
        setColumns(4);
      }
    };

    updateColumns();
    window.addEventListener('resize', updateColumns);
    return () => window.removeEventListener('resize', updateColumns);
  }, []);

  // Distribute posts across columns
  useEffect(() => {
    if (!posts.length) {
      setColumnPosts([]);
      return;
    }

    const newColumnPosts: BlogPost[][] = Array.from({ length: columns }, () => []);
    
    posts.forEach((post, index) => {
      const columnIndex = index % columns;
      newColumnPosts[columnIndex].push(post);
    });

    setColumnPosts(newColumnPosts);
  }, [posts, columns]);

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2
      }
    }
  };

  const columnVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        staggerChildren: 0.05
      }
    }
  };

  if (loading) {
    return (
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {Array.from({ length: 12 }).map((_, i) => (
          <div key={i} className="animate-pulse">
            <div className="bg-muted rounded-xl h-64 mb-4"></div>
            <div className="space-y-2">
              <div className="h-4 bg-muted rounded w-3/4"></div>
              <div className="h-4 bg-muted rounded w-1/2"></div>
              <div className="h-3 bg-muted rounded w-1/4"></div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  if (!posts.length) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="text-center py-20"
      >
        <div className="max-w-md mx-auto">
          <div className="w-24 h-24 mx-auto mb-6 rounded-full bg-muted flex items-center justify-center">
            <svg className="w-12 h-12 text-muted-foreground" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z" />
            </svg>
          </div>
          <h3 className="text-xl font-semibold mb-2">No articles found</h3>
          <p className="text-muted-foreground">
            Check back later for new content or try adjusting your search filters.
          </p>
        </div>
      </motion.div>
    );
  }

  return (
    <motion.div
      ref={containerRef}
      variants={showAnimation ? containerVariants : undefined}
      initial={showAnimation ? "hidden" : false}
      animate={showAnimation ? "visible" : false}
      className="grid gap-6"
      style={{
        gridTemplateColumns: `repeat(${columns}, 1fr)`
      }}
    >
      {columnPosts.map((columnPosts, columnIndex) => (
        <motion.div
          key={columnIndex}
          variants={showAnimation ? columnVariants : undefined}
          className="flex flex-col gap-6"
        >
          {columnPosts.map((post, postIndex) => (
            <motion.div
              key={post.id}
              variants={showAnimation ? {
                hidden: { opacity: 0, y: 20, scale: 0.95 },
                visible: {
                  opacity: 1,
                  y: 0,
                  scale: 1,
                  transition: {
                    duration: 0.5,
                    delay: (columnIndex * 0.1) + (postIndex * 0.05),
                    ease: "easeOut"
                  }
                }
              } : undefined}
              className="break-inside-avoid"
            >
              <UnifiedBlogCard
                post={post}
                index={columnIndex * columnPosts.length + postIndex}
                showAnimation={false} // We handle animation here
                className="h-auto"
              />
            </motion.div>
          ))}
        </motion.div>
      ))}
    </motion.div>
  );
}
