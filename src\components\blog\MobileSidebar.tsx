"use client";

import { useState, useCallback } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet";
import { Menu } from "lucide-react";
import { BlogSidebar } from "./BlogSidebar";

export function MobileSidebar() {
  const [open, setOpen] = useState(false);

  // Use a memoized callback to prevent recreation on each render
  const handleClose = useCallback(() => {
    setOpen(false);
  }, []);

  // Use a memoized callback for onOpenChange to prevent recreation on each render
  const handleOpenChange = useCallback((isOpen: boolean) => {
    setOpen(isOpen);
  }, []);

  return (
    <Sheet open={open} onOpenChange={handleOpenChange}>
      <SheetTrigger asChild>
        <Button variant="ghost" size="icon" className="lg:hidden">
          <Menu className="h-5 w-5" />
          <span className="sr-only">Toggle menu</span>
        </Button>
      </SheetTrigger>
      <SheetContent side="left" className="p-0 w-[300px]">
        <BlogSidebar onClose={handleClose} />
      </SheetContent>
    </Sheet>
  );
}
