'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { toast } from '@/hooks/use-toast';
import { Loader2, Mail, CheckCircle, BellOff } from 'lucide-react';
import { useAppSelector } from '@/redux/hooks';

interface SubscribeFormProps {
  source?: 'blog' | 'newsletter' | 'popup' | 'footer' | 'sidebar';
  showNameField?: boolean;
  className?: string;
  buttonText?: string;
  placeholder?: string;
  tags?: string[];
}

export function SubscribeForm({
  source = 'blog',
  showNameField = false,
  className = '',
  buttonText = 'Subscribe',
  placeholder = 'Enter your email',
  tags = [],
}: SubscribeFormProps) {
  const [email, setEmail] = useState('');
  const [name, setName] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  const [isSubscribed, setIsSubscribed] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  // Get current user from Redux store
  const { user, isAuthenticated } = useAppSelector((state) => state.auth);

  // Check if the user is already subscribed when component mounts
  useEffect(() => {
    const checkSubscriptionStatus = async () => {
      if (isAuthenticated && user?.email) {
        setIsLoading(true);
        try {
          const response = await fetch(`/api/subscribers/check?email=${encodeURIComponent(user.email)}`);
          if (response.ok) {
            const data = await response.json();
            setIsSubscribed(data.isSubscribed);

            // If user is logged in, use their name
            if (user.name) {
              setName(user.name);
            }

            // If user is logged in, use their email
            setEmail(user.email);
          }
        } catch (error) {
          console.error('Error checking subscription status:', error);
        } finally {
          setIsLoading(false);
        }
      } else {
        setIsLoading(false);
      }
    };

    checkSubscriptionStatus();
  }, [isAuthenticated, user]);

  const handleSubscribe = async (e: React.FormEvent) => {
    e.preventDefault();

    // For logged-in users, use their email
    const emailToUse = isAuthenticated && user?.email ? user.email : email;
    const nameToUse = isAuthenticated && user?.name ? user.name : name;

    if (!emailToUse.trim()) {
      toast({
        title: 'Error',
        description: 'Please enter your email address.',
        variant: 'destructive',
      });
      return;
    }

    try {
      setIsSubmitting(true);

      const subscriberData = {
        email: emailToUse,
        name: nameToUse || undefined,
        source,
        tags,
      };

      const response = await fetch('/api/subscribers', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(subscriberData),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to subscribe');
      }

      const data = await response.json();

      setIsSuccess(true);

      // Only clear fields for non-logged-in users
      if (!isAuthenticated) {
        setEmail('');
        setName('');
      }

      setIsSubscribed(true);

      toast({
        title: 'Success',
        description: data.message || 'You have been subscribed successfully!',
      });

      // Reset success state after 3 seconds
      setTimeout(() => {
        setIsSuccess(false);
      }, 3000);
    } catch (error) {
      console.error('Error subscribing:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to subscribe. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleUnsubscribe = async () => {
    if (!isAuthenticated || !user?.email) return;

    try {
      setIsSubmitting(true);

      // Find the subscriber by email first
      const checkResponse = await fetch(`/api/subscribers/check?email=${encodeURIComponent(user.email)}`);
      if (!checkResponse.ok) {
        throw new Error('Failed to check subscription status');
      }

      const checkData = await checkResponse.json();
      if (!checkData.isSubscribed || !checkData.subscriber?.id) {
        throw new Error('You are not currently subscribed');
      }

      // Update the subscriber status to unsubscribed
      const response = await fetch(`/api/subscribers/${checkData.subscriber.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          status: 'unsubscribed'
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to unsubscribe');
      }

      setIsSubscribed(false);

      toast({
        title: 'Success',
        description: 'You have been unsubscribed successfully.',
      });
    } catch (error) {
      console.error('Error unsubscribing:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to unsubscribe. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // If user is logged in, show subscribe/unsubscribe button only
  if (isAuthenticated && user) {
    return (
      <div className={`${className}`}>
        <Button
          onClick={isSubscribed ? handleUnsubscribe : handleSubscribe}
          disabled={isSubmitting || isLoading}
          className="h-10 w-full"
        >
          {isLoading ? (
            <Loader2 className="h-4 w-4 animate-spin mr-2" />
          ) : isSubmitting ? (
            <Loader2 className="h-4 w-4 animate-spin mr-2" />
          ) : isSubscribed ? (
            <>
              <BellOff className="h-4 w-4 mr-2" />
              Unsubscribe
            </>
          ) : (
            <>
              <Mail className="h-4 w-4 mr-2" />
              Subscribe
            </>
          )}
        </Button>
      </div>
    );
  }

  // For non-logged-in users, show the form
  return (
    <div className={`${className}`}>
      <form onSubmit={handleSubscribe} className="flex flex-col space-y-3">
        {showNameField && (
          <Input
            type="text"
            value={name}
            onChange={(e) => setName(e.target.value)}
            placeholder="Your name"
            disabled={isSubmitting || isSuccess}
            className="h-10"
          />
        )}
        <div className="flex space-x-2">
          <div className="relative flex-grow">
            <Input
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              placeholder={placeholder}
              disabled={isSubmitting || isSuccess}
              className="h-10 pl-10"
            />
            <Mail className="absolute left-3 top-1/2 h-5 w-5 -translate-y-1/2 text-muted-foreground" />
          </div>
          <Button
            type="submit"
            disabled={isSubmitting || isSuccess}
            className="h-10"
          >
            {isSubmitting ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : isSuccess ? (
              <CheckCircle className="h-4 w-4" />
            ) : (
              buttonText
            )}
          </Button>
        </div>
      </form>
    </div>
  );
}
