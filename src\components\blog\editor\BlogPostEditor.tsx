"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { TipTapEditor } from "./TipTapEditor";
import { EditorSidebar } from "./EditorSidebar";
import { EditorToolbar } from "./EditorToolbar";
import { Progress } from "@/components/ui/nprogress";
import { Progress as ProgressBar } from "@/components/ui/progress";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { toast } from "@/hooks/use-toast";
import { useAutoSave } from "@/hooks/useAutoSave";
import { motion } from "framer-motion";
import logger from "@/lib/secureLogger";
import { ConfirmDialog } from "@/components/admin/ConfirmDialog";
import { Trash2 } from "lucide-react";

interface BlogPostEditorProps {
  postId?: string | null;
}

interface BlogPostData {
  title: string;
  content: string;
  slug: string;
  description: string;
  tags: string[];
  categoryId: string;
  status: string;
  visibility: string;
  featuredImage: string;
  credit: string;
  scheduledAt: Date | null;
}

export function BlogPostEditor({ postId }: BlogPostEditorProps) {
  const router = useRouter();
  const [isSaving, setIsSaving] = useState(false);
  const [isPublishing, setIsPublishing] = useState(false);
  const [isLoading, setIsLoading] = useState(!!postId);
  const [isDeleting, setIsDeleting] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [editorData, setEditorData] = useState<{
    editor: any;
    handleImageUpload: () => void;
  } | null>(null);

  // Blog post state
  const [formData, setFormData] = useState<BlogPostData>({
    title: "",
    content: "",
    slug: "",
    description: "",
    tags: [],
    categoryId: "",
    status: "draft",
    visibility: "public",
    featuredImage: "",
    credit: "",
    scheduledAt: null,
  });

  // Auto-save functionality
  const { lastSaved, saveData, restoreData, clearSavedData } = useAutoSave(
    formData,
    {
      key: `blog-post-${postId || "new"}`,
      interval: 10000, // Auto-save every 10 seconds
      onSave: () => {
        logger.info("Auto-saved blog post data");
      },
    }
  );

  // Load post data if editing an existing post
  useEffect(() => {
    const loadPostData = async () => {
      if (!postId) {
        // Check for saved draft in sessionStorage
        const savedData = restoreData();
        if (savedData) {
          setFormData(savedData);
          toast({
            title: "Draft Restored",
            description: "Your unsaved draft has been restored.",
          });
        }
        return;
      }

      // Start progress bar
      Progress.start();
      setIsLoading(true);

      try {
        // In a real app, fetch the post data from the API
        const response = await fetch(`/api/blog/${postId}`);
        if (!response.ok) {
          throw new Error("Failed to load post data");
        }

        const postData = await response.json();
        setFormData({
          title: postData.title || "",
          content: postData.content || "",
          slug: postData.slug || "",
          description: postData.excerpt || "",
          tags: postData.tags || [],
          categoryId: postData.categoryId || "",
          status: postData.status || "draft",
          visibility: postData.visibility || "public",
          featuredImage: postData.featuredImage || "",
          credit: postData.credit || "",
          scheduledAt: postData.scheduledAt ? new Date(postData.scheduledAt) : null,
        });
      } catch (error) {
        logger.error("Error loading post");
        toast({
          title: "Error",
          description: "Failed to load post data. Please try again.",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
        // Complete progress bar
        Progress.done();
      }
    };

    loadPostData();
  }, [postId, restoreData]);

  // Update slug when title changes (only for new posts)
  useEffect(() => {
    if (!postId && formData.title && !formData.slug) {
      const generatedSlug = formData.title
        .toLowerCase()
        .replace(/[^\w\s]/gi, "")
        .replace(/\s+/g, "-");

      setFormData((prev) => ({
        ...prev,
        slug: generatedSlug,
      }));
    }
  }, [formData.title, formData.slug, postId]);

  // Estimate read time based on content
  const estimateReadTime = (content: string): number => {
    // Strip HTML tags
    const text = content.replace(/<[^>]*>/g, '');
    // Count words (average reading speed is 200-250 words per minute)
    const words = text.split(/\s+/).filter(word => word.length > 0).length;
    // Calculate minutes, with a minimum of 1 minute
    return Math.max(1, Math.ceil(words / 225));
  };



  // Handle form field changes
  const handleChange = (field: keyof BlogPostData, value: any) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  // Handle delete post
  const handleDeletePost = async () => {
    if (!postId) return;

    setIsDeleting(true);
    try {
      const response = await fetch(`/api/blog/${postId}`, {
        method: "DELETE",
      });

      if (response.ok) {
        toast({
          title: "Success",
          description: "Blog post deleted successfully",
        });
        router.push("/admin/blog");
      } else {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to delete post");
      }
    } catch (error: any) {
      console.error("Error deleting post:", error);
      toast({
        title: "Error",
        description: error.message || "Failed to delete post",
        variant: "destructive",
      });
    } finally {
      setIsDeleting(false);
      setShowDeleteDialog(false);
    }
  };

  // Save draft
  const handleSaveDraft = async () => {
    if (!formData.title) {
      toast({
        title: "Error",
        description: "Please enter a title for your post.",
        variant: "destructive",
      });
      return;
    }

    // Auto-generate slug if not provided
    const finalSlug = formData.slug || formData.title.toLowerCase().replace(/[^\w\s]/gi, "").replace(/\s+/g, "-");

    // Start progress bar
    Progress.start();
    setIsSaving(true);

    try {
      // Manual save to sessionStorage
      saveData();

      // In a real app, save to the API
      const method = postId ? "PUT" : "POST";
      const url = postId ? `/api/blog/${postId}` : "/api/blog";

      const response = await fetch(url, {
        method,
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          ...formData,
          slug: finalSlug,
          status: "draft",
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to save draft");
      }

      toast({
        title: "Success",
        description: "Draft saved successfully.",
      });

      // Clear sessionStorage after successful save
      clearSavedData();

      // If it's a new post, redirect to the edit page
      if (!postId) {
        const data = await response.json();
        router.push(`/admin/blog/editor?id=${data._id}`);
      }
    } catch (error) {
      logger.error("Error saving draft");
      toast({
        title: "Error",
        description: "Failed to save draft. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSaving(false);
      // Complete progress bar
      Progress.done();
    }
  };

  // Check if form is valid for publishing
  const isFormValid = () => {
    return formData.title.trim().length >= 5 &&
           formData.content.trim().length >= 50 &&
           formData.description.trim().length > 0;
  };

  // Publish post
  const handlePublish = async () => {
    // Auto-generate slug if not provided
    const finalSlug = formData.slug || formData.title.toLowerCase().replace(/[^\w\s]/gi, "").replace(/\s+/g, "-");

    // Validate required fields
    if (!isFormValid()) {
      toast({
        title: "Error",
        description: "Please fill in all required fields: title (min 5 chars), content (min 50 chars), and description.",
        variant: "destructive",
      });
      return;
    }

    // Start progress bar
    Progress.start();
    setIsPublishing(true);

    try {
      // In a real app, save to the API
      const method = postId ? "PUT" : "POST";
      const url = postId ? `/api/blog/${postId}` : "/api/blog";

      const response = await fetch(url, {
        method,
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          ...formData,
          slug: finalSlug,
          status: formData.status === "scheduled" ? "scheduled" : "published",
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to publish post");
      }

      toast({
        title: "Success",
        description: formData.status === "scheduled"
          ? "Post scheduled successfully."
          : "Post published successfully.",
      });

      // Clear sessionStorage after successful publish
      clearSavedData();

      // Redirect to blog management page
      router.push("/admin/blog");
    } catch (error) {
      logger.error("Error publishing post");
      toast({
        title: "Error",
        description: "Failed to publish post. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsPublishing(false);
      // Complete progress bar
      Progress.done();
    }
  };

  if (isLoading) {
    return (
      <div className="flex flex-col space-y-6">
        {/* Title skeleton */}
        <div className="h-12 bg-muted animate-pulse rounded-md w-full" />

        <div className="flex flex-col lg:flex-row gap-6">
          {/* Editor skeleton - 80% width */}
          <div className="flex-1 w-full lg:w-4/5">
            <div className="h-[600px] bg-muted animate-pulse rounded-md w-full" />
          </div>

          {/* Sidebar skeleton - 20% width */}
          <div className="w-full lg:w-1/5 min-w-[250px] space-y-4">
            <div className="h-64 bg-muted animate-pulse rounded-md" />
            <div className="h-64 bg-muted animate-pulse rounded-md" />
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col">
      {/* Progress bar for loading/saving states */}
      {(isSaving || isPublishing) && (
        <ProgressBar
          value={100}
          className="h-1 w-full fixed top-0 left-0 z-[100]"
        />
      )}

      {/* Sticky Editor Toolbar - At the very top */}
      <div className="sticky top-0 z-50 bg-background border-b border-input shadow-sm">
        {editorData && (
          <EditorToolbar
            editor={editorData.editor}
            onLinkClick={() => {
              // Link functionality will be handled by TipTap editor's link dialog
              console.log('Link click - handled by TipTap editor');
            }}
            onInsertTable={() => {
              // Table insertion
              editorData.editor
                .chain()
                .focus()
                .insertTable({ rows: 3, cols: 3, withHeaderRow: true })
                .run();
            }}
            onImageClick={editorData.handleImageUpload}
          />
        )}
      </div>

      {/* Title and Slug Section */}
      <div className="p-4 border-b border-input">
        <div className="space-y-2">
          <Label htmlFor="title">Title</Label>
          <Input
            id="title"
            value={formData.title}
            onChange={(e) => handleChange("title", e.target.value.slice(0, 100))}
            placeholder="Enter post title"
            className="text-xl font-medium"
            maxLength={100}
          />
          {formData.title && (
            <p className="text-xs text-muted-foreground mt-1">
              Slug: {formData.slug || formData.title.toLowerCase().replace(/[^\w\s]/gi, "").replace(/\s+/g, "-")}
            </p>
          )}
        </div>
      </div>

      {/* Main Content Area - Editor and Sidebar */}
      <div className="flex flex-col lg:flex-row">
        {/* Editor - 80% width on desktop */}
        <motion.div
          className="flex-1 w-full lg:w-4/5"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          <div className="h-full">
            <TipTapEditor
              content={formData.content}
              onChange={(value: string) => handleChange("content", value)}
              placeholder="Write your post content here..."
              onEditorReady={setEditorData}
            />
          </div>
        </motion.div>

        {/* Right Sidebar - 20% width on desktop */}
        <motion.div
          className="w-full lg:w-1/5 min-w-[280px] border-l border-input"
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.3, delay: 0.1 }}
        >
          <div className="sticky top-[84px] max-h-[calc(100vh-84px)] overflow-y-auto">
            <EditorSidebar
              description={formData.description}
              setDescription={(value) => handleChange("description", value)}
              tags={formData.tags}
              setTags={(value) => handleChange("tags", value)}
              categoryId={formData.categoryId}
              setCategoryId={(value) => handleChange("categoryId", value)}
              status={formData.status}
              setStatus={(value) => handleChange("status", value)}
              visibility={formData.visibility}
              setVisibility={(value) => handleChange("visibility", value)}
              scheduledAt={formData.scheduledAt}
              setScheduledAt={(value) => handleChange("scheduledAt", value)}
              featuredImage={formData.featuredImage}
              setFeaturedImage={(value) => handleChange("featuredImage", value)}
              credit={formData.credit}
              setCredit={(value) => handleChange("credit", value)}
              slug={formData.slug}
              setSlug={(value) => handleChange("slug", value)}
              title={formData.title}
            />
          </div>
        </motion.div>
      </div>

      {/* Action buttons and status bar - Fixed at the bottom */}
      <div className="fixed bottom-0 left-0 right-0 bg-background border-t border-input z-40">
        <div className="flex justify-between items-center p-4">
          <div className="flex items-center gap-4">
            {lastSaved && (
              <p className="text-xs text-muted-foreground">
                Last auto-saved: {lastSaved.toLocaleTimeString()}
              </p>
            )}
            <p className="text-xs text-muted-foreground">
              Estimated read time: {estimateReadTime(formData.content)} min
            </p>
          </div>

          <div className="flex items-center gap-3">
            {postId && (
              <Button
                variant="destructive"
                onClick={() => setShowDeleteDialog(true)}
                disabled={isDeleting}
                className="min-w-[120px]"
              >
                <Trash2 className="h-4 w-4 mr-2" />
                {isDeleting ? "Deleting..." : "Delete"}
              </Button>
            )}
            <Button
              variant="outline"
              onClick={handleSaveDraft}
              disabled={isSaving}
              className="min-w-[120px]"
            >
              {isSaving ? "Saving..." : "Save Draft"}
            </Button>
            <Button
              onClick={handlePublish}
              disabled={isPublishing || !isFormValid()}
              className="min-w-[120px]"
            >
              {isPublishing ? "Publishing..." : "Publish"}
            </Button>
          </div>
        </div>
      </div>

      {/* Add bottom padding to prevent content from being hidden behind fixed bar */}
      <div className="h-20"></div>

      {/* Delete Confirmation Dialog */}
      <ConfirmDialog
        open={showDeleteDialog}
        onOpenChange={setShowDeleteDialog}
        title="Delete Blog Post"
        description={`Are you sure you want to delete "${formData.title}"? This action cannot be undone.`}
        onConfirm={handleDeletePost}
        isLoading={isDeleting}
        confirmText="Delete"
        cancelText="Cancel"
        variant="destructive"
      />
    </div>
  );
}
