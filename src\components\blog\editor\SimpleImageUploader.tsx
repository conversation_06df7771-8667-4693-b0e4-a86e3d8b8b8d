'use client';

import { useState, useRef, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Upload,
  Image as ImageIcon,
  Cloud,
  FolderOpen,
  Link
} from 'lucide-react';
import { toast } from '@/hooks/use-toast';
import { Progress } from '@/components/ui/progress';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';

interface SimpleImageUploaderProps {
  label: string;
  onImageUpload: (imageData: {
    url: string;
    alt?: string;
    title?: string;
    credit?: string;
  }) => void;
  maxFileSizeMB?: number;
  className?: string;
}

export function SimpleImageUploader({
  label,
  onImageUpload,
  maxFileSizeMB = 50,
  className = '',
}: SimpleImageUploaderProps) {
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [dragActive, setDragActive] = useState(false);
  const [showUrlModal, setShowUrlModal] = useState(false);
  const [showGoogleDriveModal, setShowGoogleDriveModal] = useState(false);
  const [showDropboxModal, setShowDropboxModal] = useState(false);
  const [imageUrl, setImageUrl] = useState('');
  const [imageAlt, setImageAlt] = useState('');
  const [imageTitle, setImageTitle] = useState('');
  const [imageCredit, setImageCredit] = useState('');

  const fileInputRef = useRef<HTMLInputElement>(null);
  const maxSizeBytes = maxFileSizeMB * 1024 * 1024;

  // Handle drag events
  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();

    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  }, []);

  // Handle drop event
  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFiles(e.dataTransfer.files);
    }
  }, []);

  // Handle file input change
  const handleChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    e.preventDefault();

    if (e.target.files && e.target.files.length > 0) {
      handleFiles(e.target.files);
    }
  }, []);

  // Process files
  const handleFiles = useCallback((files: FileList) => {
    const file = files[0];

    // Check file type
    const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/svg+xml', 'image/webp'];
    if (!allowedTypes.includes(file.type)) {
      toast({
        title: 'Invalid file type',
        description: 'Please upload a JPEG, PNG, GIF, SVG, or WebP file.',
        variant: 'destructive',
      });
      return;
    }

    // Check file size
    if (file.size > maxSizeBytes) {
      toast({
        title: 'File too large',
        description: `Please upload an image smaller than ${maxFileSizeMB}MB.`,
        variant: 'destructive',
      });
      return;
    }

    // Upload the file
    uploadFile(file);
  }, [maxSizeBytes, maxFileSizeMB]);

  // Upload file to server
  const uploadFile = async (file: File) => {
    setIsUploading(true);
    setUploadProgress(0);

    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('type', 'image');

      // Simulate progress for better UX
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => {
          const newProgress = prev + Math.random() * 10;
          return newProgress >= 90 ? 90 : newProgress;
        });
      }, 300);

      const response = await fetch('/api/upload', {
        method: 'POST',
        body: formData,
        credentials: 'include',
      });

      clearInterval(progressInterval);
      setUploadProgress(100);

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to upload image');
      }

      const data = await response.json();

      // Wait a moment to show 100% progress
      setTimeout(() => {
        setIsUploading(false);
        setUploadProgress(0);

        toast({
          title: 'Image uploaded',
          description: 'Your image has been uploaded successfully.',
        });

        // Call the callback with the image URL
        onImageUpload({
          url: data.fileUrl,
          alt: imageAlt || file.name.split('.')[0],
          title: imageTitle || file.name.split('.')[0],
          credit: imageCredit,
        });

        // Clear metadata fields
        setImageAlt('');
        setImageTitle('');
        setImageCredit('');
      }, 500);

    } catch (error) {
      setIsUploading(false);
      setUploadProgress(0);

      const errorMessage = error instanceof Error ? error.message : 'Failed to upload image';

      toast({
        title: 'Upload failed',
        description: errorMessage,
        variant: 'destructive',
      });
    }
  };

  // Upload from URL
  const uploadFromUrl = async () => {
    if (!imageUrl.trim()) {
      toast({
        title: 'Error',
        description: 'Please enter a valid image URL.',
        variant: 'destructive',
      });
      return;
    }

    // Basic URL validation
    try {
      new URL(imageUrl);
    } catch {
      toast({
        title: 'Error',
        description: 'Please enter a valid URL.',
        variant: 'destructive',
      });
      return;
    }

    setIsUploading(true);
    setUploadProgress(0);

    try {
      // Simulate progress
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => {
          const newProgress = prev + Math.random() * 10;
          return newProgress >= 90 ? 90 : newProgress;
        });
      }, 300);

      // Upload URL to server (server will download and store the image)
      const response = await fetch('/api/upload', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          imageUrl: imageUrl.trim(),
          type: 'image',
          alt: imageAlt,
          title: imageTitle,
        }),
        credentials: 'include',
      });

      clearInterval(progressInterval);
      setUploadProgress(100);

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to upload image from URL');
      }

      const data = await response.json();

      setTimeout(() => {
        setIsUploading(false);
        setUploadProgress(0);
        setShowUrlModal(false);

        toast({
          title: 'Image uploaded',
          description: 'Image has been uploaded successfully from URL.',
        });

        // Call the callback with the uploaded image URL
        onImageUpload({
          url: data.fileUrl || imageUrl,
          alt: imageAlt || 'Image from URL',
          title: imageTitle || 'Image from URL',
          credit: imageCredit,
        });

        setImageUrl('');
        setImageAlt('');
        setImageTitle('');
        setImageCredit('');
      }, 500);

    } catch (error) {
      setIsUploading(false);
      setUploadProgress(0);

      const errorMessage = error instanceof Error ? error.message : 'Failed to upload image from URL';

      toast({
        title: 'Upload failed',
        description: errorMessage,
        variant: 'destructive',
      });
    }
  };

  return (
    <div className={`space-y-4 ${className}`}>
      <Label>{label}</Label>

      {/* Drag and drop area */}
      <div
        className={`border-2 border-dashed rounded-lg p-6 transition-all ${
          dragActive
            ? 'border-primary bg-primary/5'
            : 'border-muted-foreground/25 hover:border-primary/50'
        } ${
          isUploading ? 'opacity-50 pointer-events-none' : ''
        }`}
        onDragEnter={handleDrag}
        onDragLeave={handleDrag}
        onDragOver={handleDrag}
        onDrop={handleDrop}
      >
        <div className="flex flex-col items-center justify-center gap-4">
          <ImageIcon className="h-16 w-16 text-muted-foreground" />
          <p className="text-center text-muted-foreground">
            Drag and drop an image here, or choose an upload method
          </p>

          <div className="flex flex-wrap gap-2 justify-center">
            <Button
              type="button"
              variant="outline"
              onClick={() => fileInputRef.current?.click()}
              disabled={isUploading}
              className="flex items-center gap-2"
            >
              <Upload className="h-4 w-4" />
              Select File
            </Button>

            <Button
              type="button"
              variant="outline"
              onClick={() => setShowUrlModal(true)}
              disabled={isUploading}
              className="flex items-center gap-2"
            >
              <Link className="h-4 w-4" />
              Image URL
            </Button>

            <Button
              type="button"
              variant="outline"
              onClick={() => setShowGoogleDriveModal(true)}
              disabled={isUploading}
              className="flex items-center gap-2"
            >
              <Cloud className="h-4 w-4" />
              Google Drive
            </Button>

            <Button
              type="button"
              variant="outline"
              onClick={() => setShowDropboxModal(true)}
              disabled={isUploading}
              className="flex items-center gap-2"
            >
              <FolderOpen className="h-4 w-4" />
              Dropbox
            </Button>
          </div>
        </div>

        <Input
          ref={fileInputRef}
          type="file"
          accept="image/jpeg,image/png,image/gif,image/svg+xml,image/webp"
          onChange={handleChange}
          className="hidden"
        />
      </div>

      {/* Image metadata fields */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="file-alt">Alt Text (Optional)</Label>
          <Input
            id="file-alt"
            type="text"
            value={imageAlt}
            onChange={(e) => setImageAlt(e.target.value)}
            placeholder="Describe the image for accessibility"
            disabled={isUploading}
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="file-title">Title (Optional)</Label>
          <Input
            id="file-title"
            type="text"
            value={imageTitle}
            onChange={(e) => setImageTitle(e.target.value)}
            placeholder="Image title"
            disabled={isUploading}
          />
        </div>
      </div>
      <div className="space-y-2">
        <Label htmlFor="file-credit">Image Credit (Optional)</Label>
        <Input
          id="file-credit"
          type="text"
          value={imageCredit}
          onChange={(e) => setImageCredit(e.target.value)}
          placeholder="Photo credit or source"
          disabled={isUploading}
        />
      </div>

      {/* Upload progress */}
      {isUploading && (
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span>Uploading...</span>
            <span>{Math.round(uploadProgress)}%</span>
          </div>
          <Progress value={uploadProgress} />
        </div>
      )}

      {/* URL Upload Modal */}
      <Dialog open={showUrlModal} onOpenChange={setShowUrlModal}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Add Image from URL</DialogTitle>
          </DialogHeader>
          <div className="py-4 space-y-4">
            <div className="space-y-2">
              <Label htmlFor="image-url">Image URL</Label>
              <Input
                id="image-url"
                type="url"
                value={imageUrl}
                onChange={(e) => setImageUrl(e.target.value)}
                placeholder="https://example.com/image.jpg"
                disabled={isUploading}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="image-alt">Alt Text (Optional)</Label>
              <Input
                id="image-alt"
                type="text"
                value={imageAlt}
                onChange={(e) => setImageAlt(e.target.value)}
                placeholder="Describe the image for accessibility"
                disabled={isUploading}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="image-title">Title (Optional)</Label>
              <Input
                id="image-title"
                type="text"
                value={imageTitle}
                onChange={(e) => setImageTitle(e.target.value)}
                placeholder="Image title"
                disabled={isUploading}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="image-credit">Image Credit (Optional)</Label>
              <Input
                id="image-credit"
                type="text"
                value={imageCredit}
                onChange={(e) => setImageCredit(e.target.value)}
                placeholder="Photo credit or source"
                disabled={isUploading}
              />
            </div>
            <div className="flex justify-end gap-2">
              <Button
                type="button"
                variant="outline"
                onClick={() => {
                  setShowUrlModal(false);
                  setImageUrl('');
                  setImageAlt('');
                  setImageTitle('');
                  setImageCredit('');
                }}
                disabled={isUploading}
              >
                Cancel
              </Button>
              <Button
                type="button"
                onClick={uploadFromUrl}
                disabled={isUploading || !imageUrl.trim()}
              >
                {isUploading ? 'Adding...' : 'Add Image'}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Google Drive Modal */}
      <Dialog open={showGoogleDriveModal} onOpenChange={setShowGoogleDriveModal}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Select from Google Drive</DialogTitle>
          </DialogHeader>
          <div className="py-8 text-center space-y-4">
            <Cloud className="h-16 w-16 text-muted-foreground mx-auto" />
            <div className="space-y-2">
              <h3 className="text-lg font-medium">Google Drive Integration</h3>
              <p className="text-muted-foreground">
                Connect your Google Drive to easily select and upload images directly from your cloud storage.
              </p>
            </div>
            <div className="bg-muted/50 p-4 rounded-lg">
              <p className="text-sm text-muted-foreground">
                This feature is coming soon! You can currently upload images using the file selector or URL option.
              </p>
            </div>
            <Button
              variant="outline"
              onClick={() => setShowGoogleDriveModal(false)}
              className="mt-4"
            >
              Close
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* Dropbox Modal */}
      <Dialog open={showDropboxModal} onOpenChange={setShowDropboxModal}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Select from Dropbox</DialogTitle>
          </DialogHeader>
          <div className="py-8 text-center space-y-4">
            <FolderOpen className="h-16 w-16 text-muted-foreground mx-auto" />
            <div className="space-y-2">
              <h3 className="text-lg font-medium">Dropbox Integration</h3>
              <p className="text-muted-foreground">
                Connect your Dropbox to easily select and upload images directly from your cloud storage.
              </p>
            </div>
            <div className="bg-muted/50 p-4 rounded-lg">
              <p className="text-sm text-muted-foreground">
                This feature is coming soon! You can currently upload images using the file selector or URL option.
              </p>
            </div>
            <Button
              variant="outline"
              onClick={() => setShowDropboxModal(false)}
              className="mt-4"
            >
              Close
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
