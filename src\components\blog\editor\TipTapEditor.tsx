"use client";

import { useCallback, useEffect, useState } from "react";
import { useEditor, EditorContent } from "@tiptap/react";
import StarterKit from "@tiptap/starter-kit";
import Image from "@tiptap/extension-image";
import Link from "@tiptap/extension-link";
import Table from "@tiptap/extension-table";
import TableRow from "@tiptap/extension-table-row";
import TableCell from "@tiptap/extension-table-cell";
import TableHeader from "@tiptap/extension-table-header";
import Underline from "@tiptap/extension-underline";
import TextAlign from "@tiptap/extension-text-align";
import Placeholder from "@tiptap/extension-placeholder";
import HorizontalRule from "@tiptap/extension-horizontal-rule";
import BulletList from "@tiptap/extension-bullet-list";
import OrderedList from "@tiptap/extension-ordered-list";
import ListItem from "@tiptap/extension-list-item";
import TaskList from "@tiptap/extension-task-list";
import TaskItem from "@tiptap/extension-task-item";
import FontFamily from "@tiptap/extension-font-family";
import TextStyle from "@tiptap/extension-text-style";
import Color from "@tiptap/extension-color";
import Highlight from "@tiptap/extension-highlight";
import { ResizableImage } from "./extensions/ResizableImageExtension";
import { SimpleImageUploader } from "./SimpleImageUploader";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { toast } from "@/hooks/use-toast";
import logger from "@/lib/secureLogger";

interface TipTapEditorProps {
  content: string;
  onChange: (content: string) => void;
  placeholder?: string;
  editable?: boolean;
  onEditorReady?: (editorData: {
    editor: any;
    handleImageUpload: () => void;
  }) => void;
}



export function TipTapEditor({
  content,
  onChange,
  placeholder = "Write your content here...",
  editable = true,
  onEditorReady,
}: TipTapEditorProps) {
  const [isImageDialogOpen, setIsImageDialogOpen] = useState(false);
  const [isLinkDialogOpen, setIsLinkDialogOpen] = useState(false);
  const [linkUrl, setLinkUrl] = useState("");
  const [linkText, setLinkText] = useState("");

  const [wordCount, setWordCount] = useState({ words: 0, characters: 0 });

  const editor = useEditor({
    extensions: [
      StarterKit.configure({
        // Configure heading to preserve marks like links
        heading: {
          levels: [1, 2, 3, 4, 5, 6],
        },
        // Don't disable these list extensions since we're providing our own
        // bulletList: false,
        // orderedList: false,
        // listItem: false,
      }),
      Underline,
      // Text styling extensions
      TextStyle,
      FontFamily.configure({
        types: ['textStyle'],
      }),
      Color.configure({
        types: ['textStyle'],
      }),
      Highlight.configure({
        multicolor: true,
      }),
      // List extensions
      BulletList.configure({
        HTMLAttributes: {
          class: 'list-disc pl-6',
        },
      }),
      OrderedList.configure({
        HTMLAttributes: {
          class: 'list-decimal pl-6',
        },
      }),
      ListItem.configure({
        HTMLAttributes: {
          class: 'my-2',
        },
      }),
      // Task list extensions
      TaskList.configure({
        HTMLAttributes: {
          class: 'task-list',
        },
      }),
      TaskItem.configure({
        HTMLAttributes: {
          class: 'task-item',
        },
        nested: true,
      }),
      // Image extensions
      ResizableImage.configure({
        HTMLAttributes: {
          class: 'blog-image',
        },
      }),
      Image.configure({
        HTMLAttributes: {
          class: 'blog-image',
        },
        allowBase64: true,
        inline: true,
      }),
      // Other extensions
      Link.configure({
        openOnClick: true, // Enable direct click to open links
        HTMLAttributes: {
          class: "editor-link",
          target: "_blank",
          rel: "noopener noreferrer",
          style: "color: #2563eb; text-decoration: underline; cursor: pointer;",
        },
        validate: href => /^https?:\/\//.test(href),
      }),
      Table.configure({
        resizable: true,
      }),
      TableRow,
      TableCell,
      TableHeader,
      TextAlign.configure({
        types: ["heading", "paragraph", "image", "resizableImage"],
      }),
      HorizontalRule,
      Placeholder.configure({
        placeholder,
      }),
    ],
    content,
    editable,
    onUpdate: ({ editor }) => {
      onChange(editor.getHTML());

      // Auto-save to session storage
      if (typeof window !== 'undefined') {
        try {
          sessionStorage.setItem('blog-editor-content', editor.getHTML());
          logger.info('Content auto-saved to session storage');
        } catch (error) {
          logger.error('Failed to auto-save content');
        }
      }

      // Update word count
      const text = editor.getText();
      const words = text.split(/\s+/).filter(word => word.length > 0).length;
      const characters = text.length;
      setWordCount({ words, characters });
    },
    editorProps: {
      attributes: {
        class: "prose prose-lg dark:prose-invert focus:outline-none max-w-none min-h-[500px] p-4",
      },
    },
  });

  // Update editor content when content prop changes
  useEffect(() => {
    if (editor && content !== editor.getHTML()) {
      editor.commands.setContent(content);
    }
  }, [editor, content]);

  // Call onEditorReady when editor is initialized and functions are ready
  useEffect(() => {
    if (editor && onEditorReady) {
      // Create the functions inline to avoid hoisting issues
      const imageUploadHandler = () => setIsImageDialogOpen(true);


      onEditorReady({
        editor,
        handleImageUpload: imageUploadHandler,
      });
    }
  }, [editor, onEditorReady]);

  // Check for auto-saved content on mount
  useEffect(() => {
    if (editor && typeof window !== 'undefined') {
      try {
        const savedContent = sessionStorage.getItem('blog-editor-content');
        if (savedContent && savedContent !== '<p></p>' && savedContent !== editor.getHTML()) {
          // Ask user if they want to restore
          const restore = confirm('We found auto-saved content. Would you like to restore it?');
          if (restore) {
            editor.commands.setContent(savedContent);
            onChange(savedContent);
            toast({
              title: 'Content restored',
              description: 'Your auto-saved content has been restored.',
            });
          } else {
            // Clear the auto-saved content
            sessionStorage.removeItem('blog-editor-content');
          }
        }
      } catch (error) {
        logger.error('Failed to restore auto-saved content');
      }
    }
  }, [editor, onChange]);



  // Insert image
  const insertImage = useCallback((imageData: {
    url: string;
    alt?: string;
    title?: string;
  }) => {
    if (editor) {
      // Create image attributes with default values
      const attrs = {
        src: imageData.url,
        alt: imageData.alt || '',
        title: imageData.title || '',
        width: 500, // Default medium size
        height: undefined, // Let browser calculate aspect ratio
        alignment: 'center' as const,
        sizeOption: 'M' as const,
      };

      // Use the resizable image extension
      editor.chain().focus().setResizableImage(attrs).run();

      // Close the dialog
      setIsImageDialogOpen(false);

      // Show success message
      toast({
        title: 'Image inserted',
        description: 'The image has been inserted into your content. Click on it to resize or align.',
      });
    }
  }, [editor]);



  // Insert link
  const insertLink = useCallback(() => {
    if (editor) {
      const selection = editor.state.selection;
      const hasSelection = !selection.empty;

      // If there's no selection and we have link text, insert it
      if (!hasSelection && linkText) {
        editor
          .chain()
          .focus()
          .insertContent(linkText)
          .setTextSelection({
            from: editor.state.selection.from - linkText.length,
            to: editor.state.selection.from
          })
          .setLink({ href: linkUrl })
          .run();
      } else {
        // Otherwise just apply the link to the selection
        editor
          .chain()
          .focus()
          .setLink({ href: linkUrl })
          .run();
      }

      setIsLinkDialogOpen(false);
    }
  }, [editor, linkUrl, linkText]);





  // Memoized callbacks for dialog open state changes
  const handleImageDialogOpenChange = useCallback((open: boolean) => {
    setIsImageDialogOpen(open);
  }, []);

  const handleLinkDialogOpenChange = useCallback((open: boolean) => {
    setIsLinkDialogOpen(open);
  }, []);

  if (!editor) {
    return <div className="h-64 w-full bg-muted animate-pulse rounded-md" />;
  }

  return (
    <div className="tiptap-editor border border-input rounded-md bg-background">
      <style>{`
        .task-list {
          list-style-type: none;
          padding-left: 0;
        }
        .task-list li {
          display: flex;
          align-items: flex-start;
          margin-bottom: 0.5em;
        }
        .task-list li > label {
          margin-right: 0.5em;
          user-select: none;
        }
        .task-list li > div {
          flex: 1;
        }
        .task-list input[type="checkbox"] {
          margin-top: 0.2em;
        }

        /* Ensure links work properly in all contexts including headings */
        .ProseMirror a,
        .ProseMirror a[href] {
          color: #2563eb !important;
          text-decoration: underline !important;
          cursor: pointer !important;
          border: none !important;
          background: none !important;
        }
        .ProseMirror a:hover,
        .ProseMirror a[href]:hover {
          color: #1d4ed8 !important;
          text-decoration: underline !important;
        }

        /* Ensure links in headings maintain link styling */
        .ProseMirror h1 a,
        .ProseMirror h1 a[href],
        .ProseMirror h2 a,
        .ProseMirror h2 a[href],
        .ProseMirror h3 a,
        .ProseMirror h3 a[href],
        .ProseMirror h4 a,
        .ProseMirror h4 a[href],
        .ProseMirror h5 a,
        .ProseMirror h5 a[href],
        .ProseMirror h6 a,
        .ProseMirror h6 a[href] {
          color: #2563eb !important;
          text-decoration: underline !important;
          cursor: pointer !important;
          border: none !important;
          background: none !important;
        }
        .ProseMirror h1 a:hover,
        .ProseMirror h1 a[href]:hover,
        .ProseMirror h2 a:hover,
        .ProseMirror h2 a[href]:hover,
        .ProseMirror h3 a:hover,
        .ProseMirror h3 a[href]:hover,
        .ProseMirror h4 a:hover,
        .ProseMirror h4 a[href]:hover,
        .ProseMirror h5 a:hover,
        .ProseMirror h5 a[href]:hover,
        .ProseMirror h6 a:hover,
        .ProseMirror h6 a[href]:hover {
          color: #1d4ed8 !important;
          text-decoration: underline !important;
        }

        /* Override any prose styles that might interfere */
        .prose a,
        .prose a[href],
        .prose-lg a,
        .prose-lg a[href] {
          color: #2563eb !important;
          text-decoration: underline !important;
          cursor: pointer !important;
        }
        .prose a:hover,
        .prose a[href]:hover,
        .prose-lg a:hover,
        .prose-lg a[href]:hover {
          color: #1d4ed8 !important;
        }

        /* Dark mode link styles */
        .dark .ProseMirror a,
        .dark .ProseMirror a[href] {
          color: #60a5fa !important;
        }
        .dark .ProseMirror a:hover,
        .dark .ProseMirror a[href]:hover {
          color: #93c5fd !important;
        }

        /* Editor link class specific styling */
        .editor-link {
          color: #2563eb !important;
          text-decoration: underline !important;
          cursor: pointer !important;
        }
        .editor-link:hover {
          color: #1d4ed8 !important;
        }

        /* Force link styling on all anchor tags in editor */
        .ProseMirror-focused a,
        .ProseMirror-focused a[href] {
          color: #2563eb !important;
          text-decoration: underline !important;
        }

        /* Image alignment styles */
        .ProseMirror img {
          max-width: 100%;
          height: auto;
          border-radius: 8px;
          box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
          margin: 1rem 0;
        }

        .ProseMirror img.image-left {
          float: left;
          margin-right: 1rem;
          margin-bottom: 1rem;
          margin-left: 0;
        }

        .ProseMirror img.image-right {
          float: right;
          margin-left: 1rem;
          margin-bottom: 1rem;
          margin-right: 0;
        }

        .ProseMirror img.image-center {
          display: block;
          margin-left: auto;
          margin-right: auto;
          float: none;
        }

        /* Clear floats after images */
        .ProseMirror p:after {
          content: "";
          display: table;
          clear: both;
        }
      `}</style>

      {/* Editor Content */}
      <div className="p-4">
        <EditorContent editor={editor} />
      </div>

      {/* Word Count - Fixed at the bottom */}
      <div className="flex justify-between items-center p-3 text-xs text-muted-foreground border-t border-input bg-card sticky bottom-0 z-10">
        <div>
          {wordCount.words} words | {wordCount.characters} characters
        </div>
      </div>

      {/* Image Upload Dialog */}
      <Dialog
        open={isImageDialogOpen}
        onOpenChange={handleImageDialogOpenChange}
      >
        <DialogContent className="sm:max-w-[800px] max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Insert Image</DialogTitle>
          </DialogHeader>
          <div className="py-4">
            <SimpleImageUploader
              label="Upload or select an image"
              onImageUpload={insertImage}
              maxFileSizeMB={50}
            />
          </div>
        </DialogContent>
      </Dialog>

      {/* Link Dialog */}
      <Dialog
        open={isLinkDialogOpen}
        onOpenChange={handleLinkDialogOpenChange}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Insert Link</DialogTitle>
          </DialogHeader>
          <div className="py-4 space-y-4">
            {editor && editor.state.selection.empty && (
              <div className="space-y-2">
                <Label htmlFor="link-text">Link Text</Label>
                <Input
                  id="link-text"
                  value={linkText}
                  onChange={(e) => setLinkText(e.target.value)}
                  placeholder="Enter link text"
                />
              </div>
            )}
            <div className="space-y-2">
              <Label htmlFor="link-url">URL</Label>
              <Input
                id="link-url"
                value={linkUrl}
                onChange={(e) => setLinkUrl(e.target.value)}
                placeholder="https://example.com"
              />
            </div>
            <div className="flex justify-between items-center">
              {/* Remove Link Button - only show if editing existing link */}
              {editor?.getAttributes('link').href && (
                <Button
                  type="button"
                  variant="destructive"
                  onClick={() => {
                    editor.chain().focus().unsetLink().run();
                    setIsLinkDialogOpen(false);
                  }}
                >
                  Remove Link
                </Button>
              )}

              <div className="flex gap-2 ml-auto">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsLinkDialogOpen(false)}
                >
                  Cancel
                </Button>
                <Button
                  type="button"
                  onClick={insertLink}
                  disabled={!linkUrl}
                >
                  {editor?.getAttributes('link').href ? 'Update Link' : 'Insert Link'}
                </Button>
              </div>
            </div>
          </div>
        </DialogContent>
      </Dialog>


    </div>
  );
}
