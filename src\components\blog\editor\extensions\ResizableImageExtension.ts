import { Node, mergeAttributes } from '@tiptap/core';
import { NodeViewWrapper, ReactNodeViewRenderer } from '@tiptap/react';
import { ResizableImageComponent } from './ResizableImageComponent';

export interface ResizableImageOptions {
  HTMLAttributes: Record<string, any>;
}

declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    resizableImage: {
      /**
       * Add a resizable image
       */
      setResizableImage: (options: {
        src: string;
        alt?: string;
        title?: string;
        width?: number;
        height?: number;
        alignment?: 'left' | 'center' | 'right';
        sizeOption?: 'ES' | 'S' | 'M' | 'L' | 'XL' | 'XXL' | 'custom';
      }) => ReturnType;
    };
  }
}

export const ResizableImage = Node.create<ResizableImageOptions>({
  name: 'resizableImage',

  group: 'block',

  content: '',

  marks: '',

  selectable: true,

  draggable: true,

  atom: true,

  addOptions() {
    return {
      HTMLAttributes: {},
    };
  },

  addAttributes() {
    return {
      src: {
        default: null,
      },
      alt: {
        default: null,
      },
      title: {
        default: null,
      },
      width: {
        default: null,
      },
      height: {
        default: null,
      },
      alignment: {
        default: 'center',
        parseHTML: element => element.getAttribute('data-alignment'),
        renderHTML: attributes => {
          if (!attributes.alignment) {
            return {};
          }
          return {
            'data-alignment': attributes.alignment,
          };
        },
      },
      sizeOption: {
        default: 'custom',
        parseHTML: element => element.getAttribute('data-size-option'),
        renderHTML: attributes => {
          if (!attributes.sizeOption) {
            return {};
          }
          return {
            'data-size-option': attributes.sizeOption,
          };
        },
      },
    };
  },

  parseHTML() {
    return [
      {
        tag: 'div[data-resizable-image]',
      },
    ];
  },

  renderHTML({ HTMLAttributes }) {
    return [
      'div',
      mergeAttributes(this.options.HTMLAttributes, HTMLAttributes, {
        'data-resizable-image': '',
        'data-size-option': HTMLAttributes.sizeOption || 'custom',
        'data-alignment': HTMLAttributes.alignment || 'center',
      }),
      [
        'img',
        {
          src: HTMLAttributes.src,
          alt: HTMLAttributes.alt,
          title: HTMLAttributes.title,
          width: HTMLAttributes.width,
          height: HTMLAttributes.height,
        },
      ],
    ];
  },

  addNodeView() {
    return ReactNodeViewRenderer(ResizableImageComponent);
  },

  addCommands() {
    return {
      setResizableImage:
        (options) =>
        ({ commands }) => {
          return commands.insertContent({
            type: this.name,
            attrs: options,
          });
        },
    };
  },
});
