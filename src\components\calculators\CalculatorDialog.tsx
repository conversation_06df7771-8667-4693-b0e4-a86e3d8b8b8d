"use client";

import { useEffect, useState, useCallback } from "react";
import { useRouter } from "next/navigation";
import { motion, AnimatePresence } from "framer-motion";
import { X } from "lucide-react";
import * as LucideIcons from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { CalculatorConfig } from "@/data/calculators";
import PercentageCalculator from "./types/PercentageCalculator";
import MortgageCalculator from "./types/MortgageCalculator";
import BMICalculator from "./types/BMICalculator";
import TipCalculator from "./types/TipCalculator";
import AgeCalculator from "./types/AgeCalculator";
import UnitConverter from "./types/UnitConverter";
import LoanCalculator from "./types/LoanCalculator";
import CalorieCalculator from "./types/CalorieCalculator";
import BMRCalculator from "./types/BMRCalculator";
import EMICalculator from "./types/EMICalculator";
import AITokenCalculator from "./types/AITokenCalculator";
import SIPCalculator from "./types/SIPCalculator";
import TextToolsCalculator from "./types/TextToolsCalculator";
import GPACalculator from "./types/GPACalculator";
import DateDifferenceCalculator from "./types/DateDifferenceCalculator";
import CaloriesBurnedCalculator from "./types/CaloriesBurnedCalculator";
import CompoundInterestCalculator from "./types/CompoundInterestCalculator";

interface CalculatorDialogProps {
  calculator: CalculatorConfig;
  isModal?: boolean; // Whether to render as modal or inline
}

export default function CalculatorDialog({ calculator, isModal = false }: CalculatorDialogProps) {
  const [isOpen, setIsOpen] = useState(false);
  const router = useRouter();

  // Function to render the appropriate icon based on the icon name
  const renderIcon = (iconName: string, className: string) => {
    // Convert kebab-case to PascalCase for Lucide icons
    const pascalCaseName = iconName
      .split("-")
      .map(part => part.charAt(0).toUpperCase() + part.slice(1))
      .join("");

    // Get the icon component from Lucide
    const IconComponent = (LucideIcons as any)[pascalCaseName];

    if (IconComponent) {
      return <IconComponent className={className} />;
    }

    // Fallback to a default icon if the specified icon doesn't exist
    return <LucideIcons.Calculator className={className} />;
  };

  useEffect(() => {
    // Open the dialog when the component mounts (always true for page mode)
    setIsOpen(true);

    // Add event listener for escape key only in modal mode
    if (isModal) {
      const handleEscape = (e: KeyboardEvent) => {
        if (e.key === "Escape") {
          closeDialog();
        }
      };

      window.addEventListener("keydown", handleEscape);

      // Clean up event listener
      return () => {
        window.removeEventListener("keydown", handleEscape);
      };
    }
  }, [isModal]);

  // Use useCallback to prevent recreation of this function on each render
  const closeDialog = useCallback(() => {
    // Prevent multiple calls
    if (!isOpen) return;

    setIsOpen(false);

    // Use a ref to track if we've already started the navigation
    const navigationStarted = { current: false };

    setTimeout(() => {
      if (!navigationStarted.current) {
        navigationStarted.current = true;
        // Go back to the previous page or calculators list
        if (window.history.length > 1) {
          router.back();
        } else {
          router.push("/calculators/all");
        }
      }
    }, 300); // Wait for animation to complete
  }, [router, isOpen]);

  // Render the appropriate calculator component based on the calculator ID
  const renderCalculator = () => {
    // Check if this is a coming soon calculator
    if (calculator.comingSoon) {
      return (
        <div className="flex flex-col items-center justify-center py-12 text-center">
          <div className="w-24 h-24 bg-primary/10 rounded-full flex items-center justify-center mb-6">
            {renderIcon(calculator.icon, "w-12 h-12 text-primary")}
          </div>
          <h3 className="text-2xl font-bold mb-3">Coming Soon!</h3>
          <p className="text-muted-foreground max-w-md mx-auto mb-6">
            We're working hard to bring you this calculator. Check back soon!
          </p>
          <Button
            onClick={closeDialog}
            className="mt-4"
          >
            Back to Calculators
          </Button>
        </div>
      );
    }

    // Otherwise render the appropriate calculator
    switch (calculator.id) {
      case "percentage-calculator":
        return <PercentageCalculator />;
      case "mortgage-calculator":
        return <MortgageCalculator />;
      case "bmi-calculator":
        return <BMICalculator />;
      case "bmr-calculator":
        return <BMRCalculator />;
      case "tip-calculator":
        return <TipCalculator />;
      case "age-calculator":
        return <AgeCalculator />;
      case "unit-converter":
        return <UnitConverter />;
      case "loan-calculator":
        return <LoanCalculator />;
      case "emi-calculator":
        return <EMICalculator />;
      case "sip-calculator":
        return <SIPCalculator />;
      case "ai-token-calculator":
        return <AITokenCalculator />;
      case "text-tools-calculator":
        return <TextToolsCalculator />;
      case "gpa-calculator":
        return <GPACalculator />;
      case "date-difference-calculator":
        return <DateDifferenceCalculator />;
      case "calories-burned-calculator":
        return <CaloriesBurnedCalculator />;
      case "compound-interest-calculator":
        return <CompoundInterestCalculator />;
      case "calorie-calculator":
        return <CalorieCalculator />;
      case "fraction-calculator":
      case "scientific-calculator":
      case "statistics-calculator":
      case "probability-calculator":
      case "investment-calculator":
      case "car-loan-calculator":
      case "body-fat-calculator":
      case "water-intake-calculator":
      case "pregnancy-calculator":
      case "temperature-converter":
      case "time-zone-converter":
      case "currency-converter":
        // For all other implemented calculators, show a generic calculator UI
        return (
          <div className="p-6 bg-card rounded-lg border border-border">
            <h3 className="text-xl font-semibold mb-4">{calculator.title}</h3>
            <p className="text-muted-foreground mb-6">{calculator.description}</p>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
              <div>
                <label className="block text-sm font-medium mb-2">Input 1</label>
                <input
                  type="number"
                  className="w-full p-2 border border-border rounded-md bg-background"
                  placeholder="Enter value"
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-2">Input 2</label>
                <input
                  type="number"
                  className="w-full p-2 border border-border rounded-md bg-background"
                  placeholder="Enter value"
                />
              </div>
            </div>

            <Button className="w-full mb-6">Calculate</Button>

            <div className="p-4 bg-primary/10 rounded-md">
              <h4 className="font-medium mb-2">Result</h4>
              <p className="text-2xl font-bold text-primary">0.00</p>
            </div>
          </div>
        );
      default:
        return <div>Calculator not implemented yet</div>;
    }
  };

  // Render inline for page mode
  if (!isModal) {
    return (
      <div className="w-full">
        {renderCalculator()}
      </div>
    );
  }

  // Render as modal for modal mode
  return (
    <AnimatePresence>
      {isOpen && (
        <>
          {/* Backdrop */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50"
            onClick={closeDialog}
          />

          {/* Dialog */}
          <motion.div
            initial={{ opacity: 0, scale: 0.95, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: 20 }}
            transition={{ type: "spring", damping: 25, stiffness: 300 }}
            className="fixed inset-0 z-50 flex items-center justify-center p-4 sm:p-6"
            onClick={(e) => e.stopPropagation()}
          >
            <div className="bg-background border border-border rounded-xl shadow-xl w-full max-w-3xl max-h-[90vh] overflow-hidden flex flex-col">
              {/* Header */}
              {/* <div className="flex items-center justify-between p-4 border-b border-border">
                <div className="flex items-center gap-3">
                  <div className="bg-primary/10 p-2 rounded-md text-primary">
                    {renderIcon(calculator.icon, "w-5 h-5")}
                  </div>
                  <div>
                    <h2 className="text-xl font-semibold text-foreground">{calculator.title}</h2>
                    <p className="text-sm text-muted-foreground">{calculator.description}</p>
                  </div>
                </div>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={closeDialog}
                  className="rounded-full hover:bg-muted"
                >
                  <X className="w-5 h-5" />
                  <span className="sr-only">Close</span>
                </Button>
              </div> */}

              {/* Content */}
              <div className="flex-1 overflow-y-auto p-6">
                {renderCalculator()}
              </div>
            </div>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );
}
