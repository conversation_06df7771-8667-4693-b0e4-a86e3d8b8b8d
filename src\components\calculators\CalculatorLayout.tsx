"use client";

import { ReactNode } from "react";
import * as LucideIcons from "lucide-react";
import { Badge } from "@/components/ui/badge";
import Header from "@/components/layout/Header";
import Footer from "@/components/layout/Footer";

interface CalculatorLayoutProps {
  children: ReactNode;
  title: string;
  description: string;
  iconName: string;
}

export default function CalculatorLayout({
  children,
  title,
  description,
  iconName,
}: CalculatorLayoutProps) {
  // Function to render the appropriate icon based on the icon name
  const renderIcon = (iconName: string, className: string = "w-8 h-8") => {
    // Convert kebab-case to PascalCase for Lucide icons
    const pascalCaseName = iconName
      .split("-")
      .map(part => part.charAt(0).toUpperCase() + part.slice(1))
      .join("");

    // Get the icon component from Lucide
    const IconComponent = (LucideIcons as any)[pascalCaseName];

    if (IconComponent) {
      return <IconComponent className={className} />;
    }

    // Fallback to a default icon if the specified icon doesn't exist
    return <LucideIcons.Calculator className={className} />;
  };

  return (
    <div className="min-h-screen flex flex-col bg-background text-foreground">
      <style jsx>{`
        @keyframes badge-in {
          0% {
            opacity: 0;
            transform: scale(0.8);
          }
          100% {
            opacity: 1;
            transform: scale(1);
          }
        }
        .animate-badge-in {
          animation: badge-in 0.5s ease-out 0.3s both;
        }
      `}</style>
      <Header />

      {/* Calculator-specific header section */}
      <section className="bg-gradient-to-r from-blue-500 to-blue-700 py-12 text-white">
        <div className="container mx-auto px-4">
          <div className="flex items-center gap-4 max-w-4xl">
            <div className="bg-white/20 p-4 rounded-xl">
              {renderIcon(iconName)}
            </div>
            <div className="flex-1">
              <div className="flex items-center gap-3 mb-2">
                <h1 className="text-3xl md:text-4xl font-bold">{title}</h1>
                <div className="animate-badge-in">
                  <Badge
                    variant="secondary"
                    className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200 border-green-200 dark:border-green-700 font-semibold px-3 py-1 text-sm shadow-md"
                  >
                    ✓ Already Installed
                  </Badge>
                </div>
              </div>
              <p className="text-white/90 text-lg max-w-2xl">{description}</p>
            </div>
          </div>
        </div>
      </section>

      <main className="flex-1 container mx-auto px-4 py-8">
        {children}
      </main>

      <Footer />
    </div>
  );
}
