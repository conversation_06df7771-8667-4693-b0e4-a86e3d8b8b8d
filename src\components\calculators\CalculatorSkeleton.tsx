"use client";

import { motion } from "framer-motion";
import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card";

export default function CalculatorSkeleton() {
  return (
    <div className="container mx-auto px-4 py-8">
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5 }}
        className="max-w-4xl mx-auto"
      >
        {/* Header Skeleton */}
        <div className="text-center mb-8">
          <div className="h-8 bg-muted rounded-md w-64 mx-auto mb-4 animate-pulse" />
          <div className="h-4 bg-muted rounded-md w-96 mx-auto animate-pulse" />
        </div>

        {/* Calculator Card Skeleton */}
        <Card className="mb-8">
          <CardHeader>
            <div className="h-6 bg-muted rounded-md w-48 mb-2 animate-pulse" />
            <div className="h-4 bg-muted rounded-md w-72 animate-pulse" />
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Input Fields Skeleton */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {[...Array(4)].map((_, i) => (
                <div key={i} className="space-y-2">
                  <div className="h-4 bg-muted rounded-md w-24 animate-pulse" />
                  <div className="h-10 bg-muted rounded-md w-full animate-pulse" />
                </div>
              ))}
            </div>

            {/* Button Skeleton */}
            <div className="flex justify-center">
              <div className="h-10 bg-muted rounded-md w-32 animate-pulse" />
            </div>

            {/* Result Section Skeleton */}
            <div className="mt-8 p-4 border rounded-lg">
              <div className="h-6 bg-muted rounded-md w-32 mb-4 animate-pulse" />
              <div className="space-y-2">
                <div className="h-4 bg-muted rounded-md w-full animate-pulse" />
                <div className="h-4 bg-muted rounded-md w-3/4 animate-pulse" />
                <div className="h-4 bg-muted rounded-md w-1/2 animate-pulse" />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Additional Info Skeleton */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <div className="h-5 bg-muted rounded-md w-32 animate-pulse" />
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {[...Array(4)].map((_, i) => (
                  <div key={i} className="h-4 bg-muted rounded-md w-full animate-pulse" />
                ))}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <div className="h-5 bg-muted rounded-md w-24 animate-pulse" />
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {[...Array(3)].map((_, i) => (
                  <div key={i} className="h-4 bg-muted rounded-md w-full animate-pulse" />
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Loading Text */}
        <div className="text-center mt-8">
          <motion.div
            animate={{ opacity: [0.5, 1, 0.5] }}
            transition={{ duration: 1.5, repeat: Infinity }}
            className="text-muted-foreground"
          >
            Loading calculator...
          </motion.div>
        </div>
      </motion.div>
    </div>
  );
}
