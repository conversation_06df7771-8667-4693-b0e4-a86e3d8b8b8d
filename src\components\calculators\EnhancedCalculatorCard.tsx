"use client";

import Link from "next/link";
import { FiArrowRight } from "react-icons/fi";
import { motion } from "framer-motion";
import { useTheme } from "@/hooks/useTheme";
import * as LucideIcons from "lucide-react";
import { categoryLabels } from "@/data/calculators";

interface EnhancedCalculatorCardProps {
  id: string;
  title: string;
  description: string;
  icon: string;
  category: 'finance' | 'math' | 'conversion' | 'health';
  index?: number;
}

export default function EnhancedCalculatorCard({
  id,
  title,
  description,
  icon,
  category,
  index = 0,
}: EnhancedCalculatorCardProps) {
  const { theme } = useTheme();
  const isDark = theme === 'dark';

  // Animate with staggered delay
  const delay = 0.05 * (index % 8);

  // Convert kebab-case to PascalCase and get Lucide icon
  const renderIcon = (iconName: string, className = "w-6 h-6") => {
    const pascalCaseName = iconName
      .split("-")
      .map(part => part.charAt(0).toUpperCase() + part.slice(1))
      .join("");
    const IconComponent = (LucideIcons as any)[pascalCaseName];
    return IconComponent ? <IconComponent className={className} /> : <LucideIcons.Calculator className={className} />;
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20, scale: 0.95 }}
      animate={{ opacity: 1, y: 0, scale: 1 }}
      transition={{ duration: 0.4, delay, ease: [0.25, 0.1, 0.25, 1.0] }}
      whileHover={{ y: -8, transition: { duration: 0.2, ease: "easeOut" } }}
      className="h-full"
    >
      <Link
        href={`/calculators/${id}`}
        onClick={() => console.log("Navigating to:", `/calculators/${id}`)}
        className="group relative block h-full p-6 rounded-xl border transition-all duration-300 overflow-hidden shadow-sm hover:shadow-md"
        style={{
          backgroundColor: isDark ? 'var(--bg-secondary)' : 'white',
          borderColor: isDark ? '#374151' : '#e5e7eb',
          color: 'var(--text-primary)',
        }}
      >
        {/* Header: Icon and Category */}
        <div className="flex items-start justify-between mb-4">
          <motion.div
            className="text-4xl"
            whileHover={{ scale: 1.2, rotate: 5 }}
            transition={{ duration: 0.2 }}
          >
            {renderIcon(icon, "w-8 h-8")}
          </motion.div>
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary/10 text-primary">
            {categoryLabels[category]?.replace(' Calculators', '') || category}
          </span>
        </div>

        {/* Title */}
        <h3
          className="text-xl font-semibold mb-3 group-hover:text-blue-500 transition-colors"
          style={{ color: 'var(--text-primary)' }}
        >
          {title}
        </h3>

        {/* Description */}
        <p className="mb-4" style={{ color: 'var(--text-secondary)' }}>
          {description}
        </p>

        {/* Arrow icon */}
        <div className="flex justify-end items-center mt-auto">
          <motion.div whileHover={{ x: 4 }} transition={{ duration: 0.2 }}>
            <FiArrowRight className="w-5 h-5 text-gray-400 group-hover:text-blue-500 dark:group-hover:text-blue-400 transition-all" />
          </motion.div>
        </div>

        {/* Hover gradient */}
        <div
          className="absolute inset-0 -z-10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"
          style={{
            background: isDark
              ? 'linear-gradient(to bottom right, rgba(30, 64, 175, 0.2), rgba(31, 41, 55, 0.8))'
              : 'linear-gradient(to bottom right, rgba(219, 234, 254, 0.8), rgba(255, 255, 255, 0.8))',
          }}
        />

        {/* Hover glow border effect */}
        <div
          className="absolute inset-0 -z-20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-xl rounded-xl"
          style={{
            backgroundColor: isDark ? 'rgba(59, 130, 246, 0.1)' : 'rgba(59, 130, 246, 0.05)',
          }}
        />
      </Link>
    </motion.div>
  );
}
