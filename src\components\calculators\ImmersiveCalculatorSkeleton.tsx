"use client";

import { motion } from "framer-motion";
import { useTheme } from "@/hooks/useTheme";
import { FiArrowLeft } from "react-icons/fi";
import { Button } from "@/components/ui/button";

export default function ImmersiveCalculatorSkeleton() {
  const { theme } = useTheme();
  const isDark = theme === 'dark';

  return (
    <div className="min-h-screen relative overflow-hidden">
      {/* Enhanced Immersive Background */}
      <div className="fixed inset-0 -z-10">
        {isDark ? (
          <div className="absolute inset-0">
            <div className="absolute inset-0 bg-gradient-to-br from-black via-gray-900/95 to-gray-800/90" />
            <div className="absolute inset-0 opacity-20" style={{
              backgroundImage: `radial-gradient(circle at 1px 1px, rgba(255,255,255,0.1) 1px, transparent 0)`,
              backgroundSize: '20px 20px'
            }} />
            <div className="absolute inset-0 bg-gradient-to-t from-yellow-900/10 via-transparent to-orange-900/10" />
          </div>
        ) : (
          <div className="absolute inset-0">
            <div className="absolute inset-0 bg-gradient-to-br from-yellow-50 via-white to-orange-50" />
            <div className="absolute inset-0 opacity-30" style={{
              backgroundImage: `radial-gradient(circle at 1px 1px, rgba(251,191,36,0.1) 1px, transparent 0)`,
              backgroundSize: '24px 24px'
            }} />
          </div>
        )}
      </div>

      {/* Top Navigation Bar Skeleton */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="fixed top-0 left-0 right-0 z-50 p-4"
      >
        <div className="flex items-center justify-between max-w-7xl mx-auto">
          <Button
            variant="ghost"
            size="sm"
            disabled
            className={`${
              isDark 
                ? 'bg-black/20 text-white border border-white/10' 
                : 'bg-white/80 text-gray-900 border border-gray-200'
            } backdrop-blur-sm rounded-full px-4 py-2 opacity-50`}
          >
            <FiArrowLeft className="w-4 h-4 mr-2" />
            Back
          </Button>

          <div className="flex items-center gap-3">
            <div className={`w-10 h-10 rounded-full animate-pulse ${
              isDark ? 'bg-white/10' : 'bg-gray-200'
            }`} />
          </div>
        </div>
      </motion.div>

      {/* Main Calculator Badge/Panel Skeleton */}
      <div className="min-h-screen flex items-center justify-center p-4 pt-20">
        <motion.div
          initial={{ opacity: 0, scale: 0.8, y: 50 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          transition={{ duration: 0.6, ease: "easeOut" }}
          className="w-full max-w-4xl"
        >
          {/* Calculator Header Badge Skeleton */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className={`mb-6 p-6 rounded-2xl border backdrop-blur-sm ${
              isDark
                ? 'bg-gradient-to-br from-gray-900/80 via-gray-800/80 to-yellow-900/20 border-yellow-500/20'
                : 'bg-gradient-to-br from-yellow-50 via-white to-orange-50 border-yellow-200'
            } shadow-xl`}
          >
            <div className="flex items-start gap-4">
              {/* Icon Skeleton */}
              <div className={`w-16 h-16 rounded-xl animate-pulse ${
                isDark ? 'bg-yellow-500/20' : 'bg-yellow-100'
              }`} />
              
              <div className="flex-1 space-y-3">
                {/* Title Skeleton */}
                <div className="flex items-center gap-3">
                  <div className={`h-8 rounded-lg animate-pulse ${
                    isDark ? 'bg-white/20' : 'bg-gray-200'
                  }`} style={{ width: '280px' }} />
                  <div className={`h-6 w-20 rounded-full animate-pulse ${
                    isDark ? 'bg-yellow-500/20' : 'bg-yellow-100'
                  }`} />
                </div>
                
                {/* Description Skeleton */}
                <div className="space-y-2">
                  <div className={`h-5 rounded animate-pulse ${
                    isDark ? 'bg-white/15' : 'bg-gray-200'
                  }`} style={{ width: '100%' }} />
                  <div className={`h-5 rounded animate-pulse ${
                    isDark ? 'bg-white/15' : 'bg-gray-200'
                  }`} style={{ width: '75%' }} />
                </div>
                
                {/* Category Badge Skeleton */}
                <div className={`h-6 w-24 rounded-full animate-pulse ${
                  isDark ? 'bg-white/15' : 'bg-gray-200'
                }`} />
              </div>
            </div>
          </motion.div>

          {/* Calculator Content Skeleton */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className={`rounded-2xl border backdrop-blur-sm overflow-hidden shadow-2xl ${
              isDark
                ? 'bg-gradient-to-br from-gray-900/90 via-gray-800/90 to-gray-900/90 border-gray-700/50'
                : 'bg-white/95 border-gray-200'
            }`}
          >
            <div className="p-8 space-y-8">
              {/* Calculator Title Skeleton */}
              <div className="text-center space-y-4">
                <div className={`h-6 w-48 mx-auto rounded animate-pulse ${
                  isDark ? 'bg-white/20' : 'bg-gray-200'
                }`} />
                <div className={`h-4 w-64 mx-auto rounded animate-pulse ${
                  isDark ? 'bg-white/15' : 'bg-gray-200'
                }`} />
              </div>

              {/* Input Fields Skeleton */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {[...Array(4)].map((_, i) => (
                  <motion.div
                    key={i}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3, delay: 0.6 + i * 0.1 }}
                    className="space-y-2"
                  >
                    <div className={`h-4 w-24 rounded animate-pulse ${
                      isDark ? 'bg-white/20' : 'bg-gray-200'
                    }`} />
                    <div className={`h-12 w-full rounded-lg border animate-pulse ${
                      isDark 
                        ? 'bg-white/10 border-white/20' 
                        : 'bg-gray-50 border-gray-200'
                    }`} />
                  </motion.div>
                ))}
              </div>

              {/* Calculate Button Skeleton */}
              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.3, delay: 1.0 }}
                className="flex justify-center"
              >
                <div className={`h-12 w-40 rounded-lg animate-pulse ${
                  isDark ? 'bg-yellow-500/30' : 'bg-yellow-200'
                }`} />
              </motion.div>

              {/* Result Section Skeleton */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: 1.2 }}
                className={`p-6 rounded-xl border ${
                  isDark 
                    ? 'bg-white/5 border-white/10' 
                    : 'bg-gray-50 border-gray-200'
                }`}
              >
                <div className={`h-5 w-32 mb-4 rounded animate-pulse ${
                  isDark ? 'bg-white/20' : 'bg-gray-200'
                }`} />
                <div className="space-y-3">
                  <div className={`h-4 w-full rounded animate-pulse ${
                    isDark ? 'bg-white/15' : 'bg-gray-200'
                  }`} />
                  <div className={`h-4 w-3/4 rounded animate-pulse ${
                    isDark ? 'bg-white/15' : 'bg-gray-200'
                  }`} />
                  <div className={`h-4 w-1/2 rounded animate-pulse ${
                    isDark ? 'bg-white/15' : 'bg-gray-200'
                  }`} />
                </div>
              </motion.div>

              {/* Additional Info Skeleton */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {[...Array(2)].map((_, i) => (
                  <motion.div
                    key={i}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3, delay: 1.4 + i * 0.1 }}
                    className={`p-4 rounded-lg border ${
                      isDark 
                        ? 'bg-white/5 border-white/10' 
                        : 'bg-gray-50 border-gray-200'
                    }`}
                  >
                    <div className={`h-5 w-32 mb-3 rounded animate-pulse ${
                      isDark ? 'bg-white/20' : 'bg-gray-200'
                    }`} />
                    <div className="space-y-2">
                      {[...Array(3)].map((_, j) => (
                        <div
                          key={j}
                          className={`h-3 rounded animate-pulse ${
                            isDark ? 'bg-white/15' : 'bg-gray-200'
                          }`}
                          style={{ width: `${100 - j * 15}%` }}
                        />
                      ))}
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>
          </motion.div>
        </motion.div>
      </div>

      {/* Loading Text */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5, delay: 1.6 }}
        className="fixed bottom-8 left-1/2 transform -translate-x-1/2"
      >
        <motion.div
          animate={{ opacity: [0.5, 1, 0.5] }}
          transition={{ duration: 2, repeat: Infinity }}
          className={`px-6 py-3 rounded-full backdrop-blur-sm ${
            isDark 
              ? 'bg-black/40 text-white border border-white/10' 
              : 'bg-white/80 text-gray-900 border border-gray-200'
          } shadow-lg`}
        >
          <div className="flex items-center gap-3">
            <div className="flex space-x-1">
              {[...Array(3)].map((_, i) => (
                <motion.div
                  key={i}
                  className={`w-2 h-2 rounded-full ${
                    isDark ? 'bg-yellow-400' : 'bg-yellow-600'
                  }`}
                  animate={{ scale: [1, 1.2, 1] }}
                  transition={{
                    duration: 0.6,
                    repeat: Infinity,
                    delay: i * 0.2
                  }}
                />
              ))}
            </div>
            <span className="text-sm font-medium">Loading calculator...</span>
          </div>
        </motion.div>
      </motion.div>
    </div>
  );
}
