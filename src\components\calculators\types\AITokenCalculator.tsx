"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { <PERSON><PERSON>, <PERSON>bs<PERSON><PERSON>nt, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs";

export default function AITokenCalculator() {
  const [model, setModel] = useState<string>("gpt-4");
  const [inputTokens, setInputTokens] = useState<string>("");
  const [outputTokens, setOutputTokens] = useState<string>("");
  const [textInput, setTextInput] = useState<string>("");
  const [requests, setRequests] = useState<string>("1");

  const [cost, setCost] = useState<number | null>(null);
  const [totalTokens, setTotalTokens] = useState<number | null>(null);

  // Token pricing (per 1K tokens) - Updated as of 2024
  const tokenPricing = {
    "gpt-4": { input: 0.03, output: 0.06 },
    "gpt-4-turbo": { input: 0.01, output: 0.03 },
    "gpt-3.5-turbo": { input: 0.0015, output: 0.002 },
    "claude-3-opus": { input: 0.015, output: 0.075 },
    "claude-3-sonnet": { input: 0.003, output: 0.015 },
    "claude-3-haiku": { input: 0.00025, output: 0.00125 },
    "gemini-pro": { input: 0.0005, output: 0.0015 },
    "llama-2-70b": { input: 0.0007, output: 0.0009 },
  };

  const calculateFromTokens = () => {
    const inputTokenCount = parseFloat(inputTokens) || 0;
    const outputTokenCount = parseFloat(outputTokens) || 0;
    const requestCount = parseFloat(requests) || 1;

    if (!inputTokenCount && !outputTokenCount) return;

    const pricing = tokenPricing[model as keyof typeof tokenPricing];
    if (!pricing) return;

    const inputCost = (inputTokenCount / 1000) * pricing.input;
    const outputCost = (outputTokenCount / 1000) * pricing.output;
    const totalCostPerRequest = inputCost + outputCost;
    const totalCost = totalCostPerRequest * requestCount;

    setCost(totalCost);
    setTotalTokens(inputTokenCount + outputTokenCount);
  };

  const calculateFromText = () => {
    if (!textInput.trim()) return;

    // Rough estimation: 1 token ≈ 4 characters for English text
    const estimatedTokens = Math.ceil(textInput.length / 4);
    const requestCount = parseFloat(requests) || 1;

    // Assume output is similar length to input for estimation
    const inputTokenCount = estimatedTokens;
    const outputTokenCount = estimatedTokens;

    const pricing = tokenPricing[model as keyof typeof tokenPricing];
    if (!pricing) return;

    const inputCost = (inputTokenCount / 1000) * pricing.input;
    const outputCost = (outputTokenCount / 1000) * pricing.output;
    const totalCostPerRequest = inputCost + outputCost;
    const totalCost = totalCostPerRequest * requestCount;

    setCost(totalCost);
    setTotalTokens(inputTokenCount + outputTokenCount);
    setInputTokens(inputTokenCount.toString());
    setOutputTokens(outputTokenCount.toString());
  };

  const reset = () => {
    setInputTokens("");
    setOutputTokens("");
    setTextInput("");
    setRequests("1");
    setCost(null);
    setTotalTokens(null);
  };

  const formatCost = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 4,
      maximumFractionDigits: 6,
    }).format(amount);
  };

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="space-y-4">
          <div>
            <Label htmlFor="model">AI Model</Label>
            <Select value={model} onValueChange={setModel}>
              <SelectTrigger>
                <SelectValue placeholder="Select AI model" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="gpt-4">GPT-4</SelectItem>
                <SelectItem value="gpt-4-turbo">GPT-4 Turbo</SelectItem>
                <SelectItem value="gpt-3.5-turbo">GPT-3.5 Turbo</SelectItem>
                <SelectItem value="claude-3-opus">Claude 3 Opus</SelectItem>
                <SelectItem value="claude-3-sonnet">Claude 3 Sonnet</SelectItem>
                <SelectItem value="claude-3-haiku">Claude 3 Haiku</SelectItem>
                <SelectItem value="gemini-pro">Gemini Pro</SelectItem>
                <SelectItem value="llama-2-70b">Llama 2 70B</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label htmlFor="requests">Number of Requests</Label>
            <Input
              id="requests"
              type="number"
              value={requests}
              onChange={(e) => setRequests(e.target.value)}
              placeholder="Enter number of API calls"
            />
          </div>
        </div>

        <div className="p-4 bg-muted rounded-lg">
          <h3 className="font-semibold mb-2">Current Pricing (per 1K tokens)</h3>
          {model && tokenPricing[model as keyof typeof tokenPricing] && (
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span>Input:</span>
                <span>${tokenPricing[model as keyof typeof tokenPricing].input}</span>
              </div>
              <div className="flex justify-between">
                <span>Output:</span>
                <span>${tokenPricing[model as keyof typeof tokenPricing].output}</span>
              </div>
            </div>
          )}
          <p className="text-xs text-muted-foreground mt-2">
            Prices may vary. Check official documentation for latest rates.
          </p>
        </div>
      </div>

      <Tabs defaultValue="tokens" className="w-full">
        <TabsList className="grid grid-cols-2">
          <TabsTrigger value="tokens">Token Count</TabsTrigger>
          <TabsTrigger value="text">Text Estimation</TabsTrigger>
        </TabsList>

        <TabsContent value="tokens" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="inputTokens">Input Tokens</Label>
              <Input
                id="inputTokens"
                type="number"
                value={inputTokens}
                onChange={(e) => setInputTokens(e.target.value)}
                placeholder="Enter input token count"
              />
            </div>
            <div>
              <Label htmlFor="outputTokens">Output Tokens</Label>
              <Input
                id="outputTokens"
                type="number"
                value={outputTokens}
                onChange={(e) => setOutputTokens(e.target.value)}
                placeholder="Enter output token count"
              />
            </div>
          </div>
          <Button onClick={calculateFromTokens} className="w-full">
            Calculate Cost
          </Button>
        </TabsContent>

        <TabsContent value="text" className="space-y-4">
          <div>
            <Label htmlFor="textInput">Sample Text (for estimation)</Label>
            <textarea
              id="textInput"
              value={textInput}
              onChange={(e) => setTextInput(e.target.value)}
              placeholder="Paste your text here to estimate token count..."
              className="w-full h-32 p-3 border border-border rounded-md bg-background resize-none"
            />
            <p className="text-xs text-muted-foreground mt-1">
              Estimated tokens: ~{Math.ceil(textInput.length / 4)} (1 token ≈ 4 characters)
            </p>
          </div>
          <Button onClick={calculateFromText} className="w-full">
            Estimate Cost from Text
          </Button>
        </TabsContent>
      </Tabs>

      <div className="flex gap-4">
        <Button onClick={reset} variant="outline" className="flex-1">
          Reset
        </Button>
      </div>

      {cost !== null && totalTokens && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Total Cost</CardTitle>
              <CardDescription>For {requests} request(s)</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-2xl font-bold text-primary">{formatCost(cost)}</p>
              <p className="text-sm text-muted-foreground mt-2">
                {formatCost(cost / parseFloat(requests))} per request
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Total Tokens</CardTitle>
              <CardDescription>Input + Output</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-2xl font-bold text-primary">{totalTokens.toLocaleString()}</p>
              <p className="text-sm text-muted-foreground mt-2">
                {(totalTokens * parseFloat(requests)).toLocaleString()} total across all requests
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Cost per 1K Tokens</CardTitle>
              <CardDescription>Average rate</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-2xl font-bold text-primary">
                {formatCost((cost / parseFloat(requests)) / (totalTokens / 1000))}
              </p>
              <p className="text-sm text-muted-foreground mt-2">
                Blended rate for this usage
              </p>
            </CardContent>
          </Card>
        </div>
      )}

      {cost !== null && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Cost Breakdown</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span>Input Cost ({inputTokens} tokens):</span>
                <span className="font-semibold">
                  {formatCost((parseFloat(inputTokens) / 1000) * tokenPricing[model as keyof typeof tokenPricing].input * parseFloat(requests))}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span>Output Cost ({outputTokens} tokens):</span>
                <span className="font-semibold">
                  {formatCost((parseFloat(outputTokens) / 1000) * tokenPricing[model as keyof typeof tokenPricing].output * parseFloat(requests))}
                </span>
              </div>
              <div className="border-t pt-2">
                <div className="flex justify-between items-center font-bold">
                  <span>Total Cost:</span>
                  <span>{formatCost(cost)}</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
