"use client";

import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";

export default function BMICalculator() {
  const [activeTab, setActiveTab] = useState("metric");

  // Metric units
  const [heightCm, setHeightCm] = useState<string>("");
  const [weightKg, setWeightKg] = useState<string>("");

  // Imperial units
  const [heightFt, setHeightFt] = useState<string>("");
  const [heightIn, setHeightIn] = useState<string>("");
  const [weightLbs, setWeightLbs] = useState<string>("");

  // Results
  const [bmi, setBmi] = useState<number | null>(null);
  const [category, setCategory] = useState<string>("");
  const [gender, setGender] = useState<string>("male");

  // Calculate BMI using metric units
  const calculateMetricBMI = () => {
    const height = parseFloat(heightCm) / 100; // convert cm to meters
    const weight = parseFloat(weightKg);

    if (!isNaN(height) && !isNaN(weight) && height > 0 && weight > 0) {
      const bmiValue = weight / (height * height);
      setBmi(bmiValue);
      setCategory(getBMICategory(bmiValue));
    }
  };

  // Calculate BMI using imperial units
  const calculateImperialBMI = () => {
    const heightInches = (parseFloat(heightFt) * 12) + parseFloat(heightIn);
    const weight = parseFloat(weightLbs);

    if (!isNaN(heightInches) && !isNaN(weight) && heightInches > 0 && weight > 0) {
      // BMI formula for imperial: (weight in pounds * 703) / (height in inches)²
      const bmiValue = (weight * 703) / (heightInches * heightInches);
      setBmi(bmiValue);
      setCategory(getBMICategory(bmiValue));
    }
  };

  // Get BMI category based on BMI value
  const getBMICategory = (bmi: number): string => {
    if (bmi < 18.5) return "Underweight";
    if (bmi < 25) return "Normal weight";
    if (bmi < 30) return "Overweight";
    if (bmi < 35) return "Obesity (Class 1)";
    if (bmi < 40) return "Obesity (Class 2)";
    return "Extreme Obesity (Class 3)";
  };

  // Get color based on BMI category
  const getCategoryColor = (category: string): string => {
    switch (category) {
      case "Underweight":
        return "text-blue-500";
      case "Normal weight":
        return "text-green-500";
      case "Overweight":
        return "text-yellow-500";
      case "Obesity (Class 1)":
        return "text-orange-500";
      case "Obesity (Class 2)":
      case "Extreme Obesity (Class 3)":
        return "text-red-500";
      default:
        return "text-foreground";
    }
  };

  return (
    <div className="space-y-6">
      <div className="space-y-2">
        <Label>Gender</Label>
        <RadioGroup
          value={gender}
          onValueChange={setGender}
          className="flex space-x-4"
        >
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="male" id="male" />
            <Label htmlFor="male">Male</Label>
          </div>
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="female" id="female" />
            <Label htmlFor="female">Female</Label>
          </div>
        </RadioGroup>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid grid-cols-2">
          <TabsTrigger value="metric">Metric</TabsTrigger>
          <TabsTrigger value="imperial">Imperial</TabsTrigger>
        </TabsList>

        {/* Metric Tab */}
        <TabsContent value="metric" className="space-y-4 mt-4">
          <Card>
            <CardHeader>
              <CardTitle>Metric BMI Calculator</CardTitle>
              <CardDescription>
                Calculate your BMI using centimeters and kilograms
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="heightCm">Height (cm)</Label>
                  <Input
                    id="heightCm"
                    type="number"
                    placeholder="e.g., 175"
                    value={heightCm}
                    onChange={(e) => setHeightCm(e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="weightKg">Weight (kg)</Label>
                  <Input
                    id="weightKg"
                    type="number"
                    placeholder="e.g., 70"
                    value={weightKg}
                    onChange={(e) => setWeightKg(e.target.value)}
                  />
                </div>
              </div>

              <Button onClick={calculateMetricBMI} className="w-full">
                Calculate BMI
              </Button>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Imperial Tab */}
        <TabsContent value="imperial" className="space-y-4 mt-4">
          <Card>
            <CardHeader>
              <CardTitle>Imperial BMI Calculator</CardTitle>
              <CardDescription>
                Calculate your BMI using feet, inches, and pounds
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="heightFt">Height (ft)</Label>
                  <Input
                    id="heightFt"
                    type="number"
                    placeholder="e.g., 5"
                    value={heightFt}
                    onChange={(e) => setHeightFt(e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="heightIn">Height (in)</Label>
                  <Input
                    id="heightIn"
                    type="number"
                    placeholder="e.g., 10"
                    value={heightIn}
                    onChange={(e) => setHeightIn(e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="weightLbs">Weight (lbs)</Label>
                  <Input
                    id="weightLbs"
                    type="number"
                    placeholder="e.g., 160"
                    value={weightLbs}
                    onChange={(e) => setWeightLbs(e.target.value)}
                  />
                </div>
              </div>

              <Button onClick={calculateImperialBMI} className="w-full">
                Calculate BMI
              </Button>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Results */}
      {bmi !== null && (
        <Card className="mt-6">
          <CardHeader>
            <CardTitle>Your BMI Results</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex justify-center">
              <div className="w-32 h-32 rounded-full bg-primary/10 flex items-center justify-center">
                <span className="text-2xl font-bold text-primary">
                  {bmi.toFixed(1)}
                </span>
              </div>
            </div>

            <div className="text-center">
              <p className="text-lg">
                Your BMI indicates you are{" "}
                <span className={`font-bold ${getCategoryColor(category)}`}>
                  {category}
                </span>
              </p>
              <p className="text-sm text-muted-foreground mt-2">
                A healthy BMI for most adults is between 18.5 and 24.9
              </p>
            </div>

            <div className="bg-muted p-4 rounded-md text-sm">
              <p className="font-medium mb-2">What your BMI result means:</p>
              <ul className="list-disc pl-5 space-y-1">
                <li><span className="text-blue-500 font-medium">Below 18.5</span> - Underweight</li>
                <li><span className="text-green-500 font-medium">18.5 to 24.9</span> - Normal weight</li>
                <li><span className="text-yellow-500 font-medium">25 to 29.9</span> - Overweight</li>
                <li><span className="text-orange-500 font-medium">30 to 34.9</span> - Obesity (Class 1)</li>
                <li><span className="text-red-500 font-medium">35 to 39.9</span> - Obesity (Class 2)</li>
                <li><span className="text-red-500 font-medium">40 or higher</span> - Extreme Obesity (Class 3)</li>
              </ul>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
