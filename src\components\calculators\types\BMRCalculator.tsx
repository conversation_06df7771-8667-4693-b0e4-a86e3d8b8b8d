"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Ta<PERSON>, Tabs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";

export default function BMRCalculator() {
  const [gender, setGender] = useState<string>("male");
  const [age, setAge] = useState<string>("");
  const [weight, setWeight] = useState<string>("");
  const [height, setHeight] = useState<string>("");
  const [activityLevel, setActivityLevel] = useState<string>("moderate");
  const [unit, setUnit] = useState<string>("metric");

  const [bmr, setBmr] = useState<number | null>(null);
  const [tdee, setTdee] = useState<number | null>(null);
  const [weightGoal, setWeightGoal] = useState<string>("maintain");

  // Activity level multipliers
  const activityMultipliers = {
    sedentary: 1.2, // Little or no exercise
    light: 1.375, // Light exercise 1-3 days/week
    moderate: 1.55, // Moderate exercise 3-5 days/week
    active: 1.725, // Hard exercise 6-7 days/week
    veryActive: 1.9 // Very hard exercise & physical job
  };

  // Weight goal adjustments (calories per day)
  const goalAdjustments = {
    lose2: -1000, // Lose 2 lbs per week
    lose1: -500, // Lose 1 lb per week
    lose05: -250, // Lose 0.5 lb per week
    maintain: 0, // Maintain weight
    gain05: 250, // Gain 0.5 lb per week
    gain1: 500, // Gain 1 lb per week
    gain2: 1000 // Gain 2 lbs per week
  };

  const calculateBMR = () => {
    const ageNum = parseFloat(age);
    const weightNum = parseFloat(weight);
    const heightNum = parseFloat(height);

    if (!ageNum || !weightNum || !heightNum) return;

    let weightKg = weightNum;
    let heightCm = heightNum;

    // Convert to metric if needed
    if (unit === "imperial") {
      weightKg = weightNum * 0.453592; // lbs to kg
      heightCm = heightNum * 2.54; // inches to cm
    }

    let bmrValue: number;

    // Mifflin-St Jeor Equation (more accurate than Harris-Benedict)
    if (gender === "male") {
      bmrValue = 10 * weightKg + 6.25 * heightCm - 5 * ageNum + 5;
    } else {
      bmrValue = 10 * weightKg + 6.25 * heightCm - 5 * ageNum - 161;
    }

    // Calculate TDEE (Total Daily Energy Expenditure)
    const tdeeValue = bmrValue * activityMultipliers[activityLevel as keyof typeof activityMultipliers];

    setBmr(Math.round(bmrValue));
    setTdee(Math.round(tdeeValue));
  };

  const getCaloriesForGoal = () => {
    if (!tdee) return null;
    return tdee + goalAdjustments[weightGoal as keyof typeof goalAdjustments];
  };

  const reset = () => {
    setAge("");
    setWeight("");
    setHeight("");
    setBmr(null);
    setTdee(null);
  };

  return (
    <div className="space-y-6">
      <Tabs value={unit} onValueChange={setUnit} className="w-full">
        <TabsList className="grid grid-cols-2">
          <TabsTrigger value="metric">Metric</TabsTrigger>
          <TabsTrigger value="imperial">Imperial</TabsTrigger>
        </TabsList>

        <TabsContent value="metric" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div>
                <Label htmlFor="gender">Gender</Label>
                <Select value={gender} onValueChange={setGender}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select gender" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="male">Male</SelectItem>
                    <SelectItem value="female">Female</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="age">Age (years)</Label>
                <Input
                  id="age"
                  type="number"
                  value={age}
                  onChange={(e) => setAge(e.target.value)}
                  placeholder="Enter your age"
                />
              </div>

              <div>
                <Label htmlFor="weight">Weight (kg)</Label>
                <Input
                  id="weight"
                  type="number"
                  value={weight}
                  onChange={(e) => setWeight(e.target.value)}
                  placeholder="Enter your weight"
                />
              </div>

              <div>
                <Label htmlFor="height">Height (cm)</Label>
                <Input
                  id="height"
                  type="number"
                  value={height}
                  onChange={(e) => setHeight(e.target.value)}
                  placeholder="Enter your height"
                />
              </div>
            </div>

            <div className="space-y-4">
              <div>
                <Label htmlFor="activity">Activity Level</Label>
                <Select value={activityLevel} onValueChange={setActivityLevel}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select activity level" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="sedentary">Sedentary (little/no exercise)</SelectItem>
                    <SelectItem value="light">Light (1-3 days/week)</SelectItem>
                    <SelectItem value="moderate">Moderate (3-5 days/week)</SelectItem>
                    <SelectItem value="active">Active (6-7 days/week)</SelectItem>
                    <SelectItem value="veryActive">Very Active (2x/day, intense)</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="goal">Weight Goal</Label>
                <Select value={weightGoal} onValueChange={setWeightGoal}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select weight goal" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="lose2">Lose 2 lbs/week</SelectItem>
                    <SelectItem value="lose1">Lose 1 lb/week</SelectItem>
                    <SelectItem value="lose05">Lose 0.5 lb/week</SelectItem>
                    <SelectItem value="maintain">Maintain weight</SelectItem>
                    <SelectItem value="gain05">Gain 0.5 lb/week</SelectItem>
                    <SelectItem value="gain1">Gain 1 lb/week</SelectItem>
                    <SelectItem value="gain2">Gain 2 lbs/week</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="imperial" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div>
                <Label htmlFor="gender">Gender</Label>
                <Select value={gender} onValueChange={setGender}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select gender" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="male">Male</SelectItem>
                    <SelectItem value="female">Female</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="age">Age (years)</Label>
                <Input
                  id="age"
                  type="number"
                  value={age}
                  onChange={(e) => setAge(e.target.value)}
                  placeholder="Enter your age"
                />
              </div>

              <div>
                <Label htmlFor="weight">Weight (lbs)</Label>
                <Input
                  id="weight"
                  type="number"
                  value={weight}
                  onChange={(e) => setWeight(e.target.value)}
                  placeholder="Enter your weight"
                />
              </div>

              <div>
                <Label htmlFor="height">Height (inches)</Label>
                <Input
                  id="height"
                  type="number"
                  value={height}
                  onChange={(e) => setHeight(e.target.value)}
                  placeholder="Enter your height"
                />
              </div>
            </div>

            <div className="space-y-4">
              <div>
                <Label htmlFor="activity">Activity Level</Label>
                <Select value={activityLevel} onValueChange={setActivityLevel}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select activity level" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="sedentary">Sedentary (little/no exercise)</SelectItem>
                    <SelectItem value="light">Light (1-3 days/week)</SelectItem>
                    <SelectItem value="moderate">Moderate (3-5 days/week)</SelectItem>
                    <SelectItem value="active">Active (6-7 days/week)</SelectItem>
                    <SelectItem value="veryActive">Very Active (2x/day, intense)</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="goal">Weight Goal</Label>
                <Select value={weightGoal} onValueChange={setWeightGoal}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select weight goal" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="lose2">Lose 2 lbs/week</SelectItem>
                    <SelectItem value="lose1">Lose 1 lb/week</SelectItem>
                    <SelectItem value="lose05">Lose 0.5 lb/week</SelectItem>
                    <SelectItem value="maintain">Maintain weight</SelectItem>
                    <SelectItem value="gain05">Gain 0.5 lb/week</SelectItem>
                    <SelectItem value="gain1">Gain 1 lb/week</SelectItem>
                    <SelectItem value="gain2">Gain 2 lbs/week</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>
        </TabsContent>
      </Tabs>

      <div className="flex gap-4">
        <Button onClick={calculateBMR} className="flex-1">
          Calculate BMR
        </Button>
        <Button onClick={reset} variant="outline">
          Reset
        </Button>
      </div>

      {bmr && tdee && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">BMR</CardTitle>
              <CardDescription>Basal Metabolic Rate</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-2xl font-bold text-primary">{bmr} calories/day</p>
              <p className="text-sm text-muted-foreground mt-2">
                Calories burned at rest
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-lg">TDEE</CardTitle>
              <CardDescription>Total Daily Energy Expenditure</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-2xl font-bold text-primary">{tdee} calories/day</p>
              <p className="text-sm text-muted-foreground mt-2">
                Calories burned including activity
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Goal Calories</CardTitle>
              <CardDescription>For your weight goal</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-2xl font-bold text-primary">{getCaloriesForGoal()} calories/day</p>
              <p className="text-sm text-muted-foreground mt-2">
                Daily intake for goal
              </p>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
}
