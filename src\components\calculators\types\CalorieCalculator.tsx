"use client";

import { useState } from "react";
import { <PERSON>, CardContent, CardDescription, Card<PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

export default function CalorieCalculator() {
  const [gender, setGender] = useState<string>("male");
  const [age, setAge] = useState<string>("");
  const [weight, setWeight] = useState<string>("");
  const [height, setHeight] = useState<string>("");
  const [activityLevel, setActivityLevel] = useState<string>("moderate");
  const [goal, setGoal] = useState<string>("maintain");

  const [bmr, setBmr] = useState<number | null>(null);
  const [tdee, setTdee] = useState<number | null>(null);
  const [targetCalories, setTargetCalories] = useState<number | null>(null);

  // Activity level multipliers
  const activityMultipliers = {
    sedentary: 1.2, // Little or no exercise
    light: 1.375, // Light exercise 1-3 days/week
    moderate: 1.55, // Moderate exercise 3-5 days/week
    active: 1.725, // Hard exercise 6-7 days/week
    veryActive: 1.9 // Very hard exercise & physical job
  };

  // Goal adjustments
  const goalAdjustments = {
    lose: -500, // Lose weight (0.5-1 lb per week)
    maintain: 0, // Maintain weight
    gain: 500 // Gain weight (0.5-1 lb per week)
  };

  const calculateCalories = () => {
    const ageValue = parseFloat(age);
    const weightValue = parseFloat(weight);
    const heightValue = parseFloat(height);

    if (isNaN(ageValue) || isNaN(weightValue) || isNaN(heightValue)) {
      return;
    }

    // Calculate BMR using Mifflin-St Jeor Equation
    let bmrValue = 0;
    if (gender === "male") {
      bmrValue = 10 * weightValue + 6.25 * heightValue - 5 * ageValue + 5;
    } else {
      bmrValue = 10 * weightValue + 6.25 * heightValue - 5 * ageValue - 161;
    }

    // Calculate TDEE (Total Daily Energy Expenditure)
    const tdeeValue = bmrValue * activityMultipliers[activityLevel as keyof typeof activityMultipliers];

    // Calculate target calories based on goal
    const targetValue = tdeeValue + goalAdjustments[goal as keyof typeof goalAdjustments];

    setBmr(Math.round(bmrValue));
    setTdee(Math.round(tdeeValue));
    setTargetCalories(Math.round(targetValue));
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Calorie Calculator</CardTitle>
          <CardDescription>
            Calculate your daily calorie needs based on your activity level and goals
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="space-y-4">
            <div className="space-y-2">
              <Label>Gender</Label>
              <RadioGroup
                value={gender}
                onValueChange={setGender}
                className="flex space-x-4"
              >
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="male" id="male" />
                  <Label htmlFor="male">Male</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="female" id="female" />
                  <Label htmlFor="female">Female</Label>
                </div>
              </RadioGroup>
            </div>

            <div className="grid grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="age">Age</Label>
                <Input
                  id="age"
                  type="number"
                  placeholder="Years"
                  value={age}
                  onChange={(e) => setAge(e.target.value)}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="weight">Weight (kg)</Label>
                <Input
                  id="weight"
                  type="number"
                  placeholder="kg"
                  value={weight}
                  onChange={(e) => setWeight(e.target.value)}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="height">Height (cm)</Label>
                <Input
                  id="height"
                  type="number"
                  placeholder="cm"
                  value={height}
                  onChange={(e) => setHeight(e.target.value)}
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="activityLevel">Activity Level</Label>
              <Select value={activityLevel} onValueChange={setActivityLevel}>
                <SelectTrigger id="activityLevel">
                  <SelectValue placeholder="Select activity level" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="sedentary">Sedentary (little or no exercise)</SelectItem>
                  <SelectItem value="light">Lightly active (1-3 days/week)</SelectItem>
                  <SelectItem value="moderate">Moderately active (3-5 days/week)</SelectItem>
                  <SelectItem value="active">Very active (6-7 days/week)</SelectItem>
                  <SelectItem value="veryActive">Extra active (very physical job/training)</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="goal">Goal</Label>
              <Select value={goal} onValueChange={setGoal}>
                <SelectTrigger id="goal">
                  <SelectValue placeholder="Select your goal" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="lose">Lose weight</SelectItem>
                  <SelectItem value="maintain">Maintain weight</SelectItem>
                  <SelectItem value="gain">Gain weight</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <Button
            onClick={calculateCalories}
            className="w-full"
            disabled={!age || !weight || !height}
          >
            Calculate Calories
          </Button>

          {targetCalories !== null && (
            <div className="mt-6 space-y-4">
              <div className="p-4 bg-primary/10 rounded-lg">
                <h3 className="text-sm font-medium text-muted-foreground mb-1">Daily Calorie Target</h3>
                <p className="text-2xl font-bold text-primary">{targetCalories} calories</p>
                <p className="text-xs text-muted-foreground mt-1">
                  Based on your {goal === "lose" ? "weight loss" : goal === "gain" ? "weight gain" : "maintenance"} goal
                </p>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="p-4 bg-muted rounded-lg">
                  <h3 className="text-sm font-medium text-muted-foreground mb-1">Basal Metabolic Rate</h3>
                  <p className="text-xl font-bold">{bmr} calories</p>
                  <p className="text-xs text-muted-foreground mt-1">
                    Calories burned at complete rest
                  </p>
                </div>
                <div className="p-4 bg-muted rounded-lg">
                  <h3 className="text-sm font-medium text-muted-foreground mb-1">Total Daily Energy</h3>
                  <p className="text-xl font-bold">{tdee} calories</p>
                  <p className="text-xs text-muted-foreground mt-1">
                    Calories burned with activity
                  </p>
                </div>
              </div>

              <div className="p-4 bg-muted rounded-lg text-sm">
                <h3 className="font-medium mb-2">Macronutrient Breakdown</h3>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span>Protein (30%)</span>
                    <span className="font-medium">{Math.round(targetCalories * 0.3 / 4)}g ({Math.round(targetCalories * 0.3)} cal)</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Carbohydrates (40%)</span>
                    <span className="font-medium">{Math.round(targetCalories * 0.4 / 4)}g ({Math.round(targetCalories * 0.4)} cal)</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Fat (30%)</span>
                    <span className="font-medium">{Math.round(targetCalories * 0.3 / 9)}g ({Math.round(targetCalories * 0.3)} cal)</span>
                  </div>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
