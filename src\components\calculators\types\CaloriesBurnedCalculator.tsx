"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

export default function CaloriesBurnedCalculator() {
  const [weight, setWeight] = useState<string>("");
  const [activity, setActivity] = useState<string>("");
  const [duration, setDuration] = useState<string>("");
  const [unit, setUnit] = useState<string>("kg");

  const [caloriesBurned, setCaloriesBurned] = useState<number | null>(null);
  const [caloriesPerMinute, setCaloriesPerMinute] = useState<number | null>(null);

  // MET (Metabolic Equivalent of Task) values for different activities
  const activities = {
    // Cardio Activities
    "walking-slow": { name: "Walking (2 mph)", met: 2.5 },
    "walking-moderate": { name: "Walking (3.5 mph)", met: 4.3 },
    "walking-fast": { name: "Walking (4.5 mph)", met: 6.3 },
    "jogging": { name: "Jogging (5 mph)", met: 8.0 },
    "running-6": { name: "Running (6 mph)", met: 9.8 },
    "running-7": { name: "Running (7 mph)", met: 11.0 },
    "running-8": { name: "Running (8 mph)", met: 11.8 },
    "running-10": { name: "Running (10 mph)", met: 14.5 },
    
    // Cycling
    "cycling-leisure": { name: "Cycling (leisure, <10 mph)", met: 4.0 },
    "cycling-moderate": { name: "Cycling (12-14 mph)", met: 8.0 },
    "cycling-vigorous": { name: "Cycling (16-19 mph)", met: 12.0 },
    "cycling-racing": { name: "Cycling (>20 mph)", met: 16.0 },
    
    // Swimming
    "swimming-leisure": { name: "Swimming (leisure)", met: 6.0 },
    "swimming-moderate": { name: "Swimming (moderate)", met: 8.3 },
    "swimming-vigorous": { name: "Swimming (vigorous)", met: 10.0 },
    
    // Strength Training
    "weight-lifting-light": { name: "Weight Lifting (light)", met: 3.0 },
    "weight-lifting-moderate": { name: "Weight Lifting (moderate)", met: 5.0 },
    "weight-lifting-vigorous": { name: "Weight Lifting (vigorous)", met: 6.0 },
    
    // Sports
    "basketball": { name: "Basketball", met: 6.5 },
    "tennis": { name: "Tennis", met: 7.3 },
    "soccer": { name: "Soccer", met: 7.0 },
    "volleyball": { name: "Volleyball", met: 4.0 },
    "badminton": { name: "Badminton", met: 5.5 },
    
    // Other Activities
    "yoga": { name: "Yoga", met: 2.5 },
    "pilates": { name: "Pilates", met: 3.0 },
    "dancing": { name: "Dancing", met: 4.8 },
    "hiking": { name: "Hiking", met: 6.0 },
    "rock-climbing": { name: "Rock Climbing", met: 8.0 },
    "rowing": { name: "Rowing", met: 7.0 },
    "elliptical": { name: "Elliptical Trainer", met: 5.0 },
    "stair-climbing": { name: "Stair Climbing", met: 8.8 },
  };

  const calculateCalories = () => {
    const weightNum = parseFloat(weight);
    const durationNum = parseFloat(duration);

    if (!weightNum || !durationNum || !activity) return;

    // Convert weight to kg if needed
    const weightKg = unit === "lbs" ? weightNum * 0.453592 : weightNum;

    // Get MET value for selected activity
    const selectedActivity = activities[activity as keyof typeof activities];
    if (!selectedActivity) return;

    // Calculate calories burned using MET formula
    // Calories = MET × weight (kg) × time (hours)
    const timeHours = durationNum / 60;
    const calories = selectedActivity.met * weightKg * timeHours;
    const caloriesPerMin = calories / durationNum;

    setCaloriesBurned(Math.round(calories));
    setCaloriesPerMinute(Math.round(caloriesPerMin * 10) / 10);
  };

  const reset = () => {
    setWeight("");
    setActivity("");
    setDuration("");
    setCaloriesBurned(null);
    setCaloriesPerMinute(null);
  };

  const getActivityCategory = (activityKey: string) => {
    if (activityKey.includes("walking") || activityKey.includes("jogging") || activityKey.includes("running")) {
      return "Running & Walking";
    }
    if (activityKey.includes("cycling")) {
      return "Cycling";
    }
    if (activityKey.includes("swimming")) {
      return "Swimming";
    }
    if (activityKey.includes("weight")) {
      return "Strength Training";
    }
    if (["basketball", "tennis", "soccer", "volleyball", "badminton"].includes(activityKey)) {
      return "Sports";
    }
    return "Other Activities";
  };

  const groupedActivities = Object.entries(activities).reduce((acc, [key, value]) => {
    const category = getActivityCategory(key);
    if (!acc[category]) {
      acc[category] = [];
    }
    acc[category].push({ key, ...value });
    return acc;
  }, {} as Record<string, Array<{ key: string; name: string; met: number }>>);

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="space-y-4">
          <div>
            <Label htmlFor="weight">Body Weight</Label>
            <div className="flex gap-2">
              <Input
                id="weight"
                type="number"
                value={weight}
                onChange={(e) => setWeight(e.target.value)}
                placeholder="Enter your weight"
                className="flex-1"
              />
              <Select value={unit} onValueChange={setUnit}>
                <SelectTrigger className="w-20">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="kg">kg</SelectItem>
                  <SelectItem value="lbs">lbs</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div>
            <Label htmlFor="activity">Activity</Label>
            <Select value={activity} onValueChange={setActivity}>
              <SelectTrigger>
                <SelectValue placeholder="Select an activity" />
              </SelectTrigger>
              <SelectContent className="max-h-60">
                {Object.entries(groupedActivities).map(([category, activities]) => (
                  <div key={category}>
                    <div className="px-2 py-1 text-sm font-semibold text-muted-foreground">
                      {category}
                    </div>
                    {activities.map((activity) => (
                      <SelectItem key={activity.key} value={activity.key}>
                        {activity.name} (MET: {activity.met})
                      </SelectItem>
                    ))}
                  </div>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label htmlFor="duration">Duration (minutes)</Label>
            <Input
              id="duration"
              type="number"
              value={duration}
              onChange={(e) => setDuration(e.target.value)}
              placeholder="Enter duration in minutes"
            />
          </div>
        </div>

        <div className="space-y-4">
          <div className="p-4 bg-muted rounded-lg">
            <h3 className="font-semibold mb-2">About MET Values</h3>
            <p className="text-sm text-muted-foreground">
              MET (Metabolic Equivalent of Task) represents the energy cost of activities. 
              1 MET = resting metabolic rate. Higher MET values indicate more intense activities.
            </p>
          </div>

          {activity && activities[activity as keyof typeof activities] && (
            <div className="p-4 bg-primary/10 rounded-lg">
              <h3 className="font-semibold mb-2">Selected Activity</h3>
              <p className="text-sm">
                <span className="font-medium">
                  {activities[activity as keyof typeof activities].name}
                </span>
              </p>
              <p className="text-sm text-muted-foreground">
                MET Value: {activities[activity as keyof typeof activities].met}
              </p>
            </div>
          )}

          <div className="p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
            <h3 className="font-semibold mb-2">Tips for Accuracy</h3>
            <ul className="text-sm space-y-1">
              <li>• Use your actual body weight</li>
              <li>• Consider your fitness level</li>
              <li>• Account for rest periods</li>
              <li>• Values are estimates</li>
            </ul>
          </div>
        </div>
      </div>

      <div className="flex gap-4">
        <Button onClick={calculateCalories} className="flex-1">
          Calculate Calories Burned
        </Button>
        <Button onClick={reset} variant="outline">
          Reset
        </Button>
      </div>

      {caloriesBurned !== null && caloriesPerMinute !== null && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Calories Burned</CardTitle>
              <CardDescription>Total for {duration} minutes</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-3xl font-bold text-primary">{caloriesBurned}</p>
              <p className="text-sm text-muted-foreground mt-2">
                {activities[activity as keyof typeof activities]?.name}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Rate</CardTitle>
              <CardDescription>Calories per minute</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-3xl font-bold text-primary">{caloriesPerMinute}</p>
              <p className="text-sm text-muted-foreground mt-2">
                cal/min
              </p>
            </CardContent>
          </Card>
        </div>
      )}

      {caloriesBurned !== null && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Calorie Equivalents</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span>Apples (medium):</span>
                  <span className="font-semibold">{Math.round(caloriesBurned / 95)}</span>
                </div>
                <div className="flex justify-between">
                  <span>Bananas (medium):</span>
                  <span className="font-semibold">{Math.round(caloriesBurned / 105)}</span>
                </div>
                <div className="flex justify-between">
                  <span>Slices of bread:</span>
                  <span className="font-semibold">{Math.round(caloriesBurned / 80)}</span>
                </div>
              </div>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span>Chocolate bars:</span>
                  <span className="font-semibold">{Math.round(caloriesBurned / 250)}</span>
                </div>
                <div className="flex justify-between">
                  <span>Cans of soda:</span>
                  <span className="font-semibold">{Math.round(caloriesBurned / 150)}</span>
                </div>
                <div className="flex justify-between">
                  <span>Cups of coffee:</span>
                  <span className="font-semibold">{Math.round(caloriesBurned / 5)}</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
