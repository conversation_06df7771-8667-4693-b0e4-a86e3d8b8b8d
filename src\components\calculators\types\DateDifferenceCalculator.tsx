"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";

export default function DateDifferenceCalculator() {
  const [startDate, setStartDate] = useState<string>("");
  const [endDate, setEndDate] = useState<string>("");
  const [activeTab, setActiveTab] = useState<string>("difference");

  // Add/subtract date states
  const [baseDate, setBaseDate] = useState<string>("");
  const [yearsToAdd, setYearsToAdd] = useState<string>("");
  const [monthsToAdd, setMonthsToAdd] = useState<string>("");
  const [daysToAdd, setDaysToAdd] = useState<string>("");
  const [operation, setOperation] = useState<string>("add");

  const [result, setResult] = useState<any>(null);
  const [calculatedDate, setCalculatedDate] = useState<string>("");

  const calculateDateDifference = () => {
    if (!startDate || !endDate) return;

    const start = new Date(startDate);
    const end = new Date(endDate);

    if (start > end) {
      // Swap dates if start is after end
      const temp = start;
      start.setTime(end.getTime());
      end.setTime(temp.getTime());
    }

    // Calculate differences
    const timeDiff = end.getTime() - start.getTime();
    const daysDiff = Math.floor(timeDiff / (1000 * 3600 * 24));
    const weeksDiff = Math.floor(daysDiff / 7);
    const monthsDiff = (end.getFullYear() - start.getFullYear()) * 12 + (end.getMonth() - start.getMonth());
    const yearsDiff = end.getFullYear() - start.getFullYear();

    // Calculate precise years, months, days
    let years = yearsDiff;
    let months = end.getMonth() - start.getMonth();
    let days = end.getDate() - start.getDate();

    if (days < 0) {
      months--;
      const lastMonth = new Date(end.getFullYear(), end.getMonth(), 0);
      days += lastMonth.getDate();
    }

    if (months < 0) {
      years--;
      months += 12;
    }

    // Calculate hours, minutes, seconds
    const hours = Math.floor((timeDiff % (1000 * 3600 * 24)) / (1000 * 3600));
    const minutes = Math.floor((timeDiff % (1000 * 3600)) / (1000 * 60));
    const seconds = Math.floor((timeDiff % (1000 * 60)) / 1000);

    setResult({
      totalDays: daysDiff,
      totalWeeks: weeksDiff,
      totalMonths: monthsDiff,
      totalYears: Math.floor(daysDiff / 365.25),
      totalHours: Math.floor(timeDiff / (1000 * 3600)),
      totalMinutes: Math.floor(timeDiff / (1000 * 60)),
      totalSeconds: Math.floor(timeDiff / 1000),
      precise: {
        years,
        months,
        days,
        hours,
        minutes,
        seconds
      }
    });
  };

  const calculateDateAddSubtract = () => {
    if (!baseDate) return;

    const base = new Date(baseDate);
    const years = parseInt(yearsToAdd) || 0;
    const months = parseInt(monthsToAdd) || 0;
    const days = parseInt(daysToAdd) || 0;

    const newDate = new Date(base);

    if (operation === "add") {
      newDate.setFullYear(newDate.getFullYear() + years);
      newDate.setMonth(newDate.getMonth() + months);
      newDate.setDate(newDate.getDate() + days);
    } else {
      newDate.setFullYear(newDate.getFullYear() - years);
      newDate.setMonth(newDate.getMonth() - months);
      newDate.setDate(newDate.getDate() - days);
    }

    setCalculatedDate(newDate.toISOString().split('T')[0]);
  };

  const reset = () => {
    setStartDate("");
    setEndDate("");
    setBaseDate("");
    setYearsToAdd("");
    setMonthsToAdd("");
    setDaysToAdd("");
    setResult(null);
    setCalculatedDate("");
  };

  const setToday = (field: string) => {
    const today = new Date().toISOString().split('T')[0];
    if (field === "start") setStartDate(today);
    else if (field === "end") setEndDate(today);
    else if (field === "base") setBaseDate(today);
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <div className="space-y-6">
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid grid-cols-2">
          <TabsTrigger value="difference">Date Difference</TabsTrigger>
          <TabsTrigger value="calculate">Add/Subtract Dates</TabsTrigger>
        </TabsList>

        <TabsContent value="difference" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div>
                <div className="flex justify-between items-center mb-2">
                  <Label htmlFor="startDate">Start Date</Label>
                  <Button onClick={() => setToday("start")} variant="outline" size="sm">
                    Today
                  </Button>
                </div>
                <Input
                  id="startDate"
                  type="date"
                  value={startDate}
                  onChange={(e) => setStartDate(e.target.value)}
                />
                {startDate && (
                  <p className="text-sm text-muted-foreground mt-1">
                    {formatDate(startDate)}
                  </p>
                )}
              </div>

              <div>
                <div className="flex justify-between items-center mb-2">
                  <Label htmlFor="endDate">End Date</Label>
                  <Button onClick={() => setToday("end")} variant="outline" size="sm">
                    Today
                  </Button>
                </div>
                <Input
                  id="endDate"
                  type="date"
                  value={endDate}
                  onChange={(e) => setEndDate(e.target.value)}
                />
                {endDate && (
                  <p className="text-sm text-muted-foreground mt-1">
                    {formatDate(endDate)}
                  </p>
                )}
              </div>
            </div>

            <div className="p-4 bg-muted rounded-lg">
              <h3 className="font-semibold mb-2">Quick Examples</h3>
              <div className="space-y-2 text-sm">
                <p>• Calculate age from birth date</p>
                <p>• Days until an event</p>
                <p>• Project duration</p>
                <p>• Relationship anniversary</p>
                <p>• Time since graduation</p>
              </div>
            </div>
          </div>

          <Button onClick={calculateDateDifference} className="w-full">
            Calculate Difference
          </Button>

          {result && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Total Days</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-2xl font-bold text-primary">{result.totalDays.toLocaleString()}</p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Total Weeks</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-2xl font-bold text-primary">{result.totalWeeks.toLocaleString()}</p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Total Months</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-2xl font-bold text-primary">{result.totalMonths.toLocaleString()}</p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Total Years</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-2xl font-bold text-primary">{result.totalYears.toLocaleString()}</p>
                </CardContent>
              </Card>
            </div>
          )}

          {result && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Precise Difference</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-lg">
                  <span className="font-bold">{result.precise.years}</span> years, {" "}
                  <span className="font-bold">{result.precise.months}</span> months, {" "}
                  <span className="font-bold">{result.precise.days}</span> days
                </p>
                <div className="mt-4 grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                  <div>
                    <span className="font-semibold">Hours:</span> {result.totalHours.toLocaleString()}
                  </div>
                  <div>
                    <span className="font-semibold">Minutes:</span> {result.totalMinutes.toLocaleString()}
                  </div>
                  <div>
                    <span className="font-semibold">Seconds:</span> {result.totalSeconds.toLocaleString()}
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="calculate" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div>
                <div className="flex justify-between items-center mb-2">
                  <Label htmlFor="baseDate">Base Date</Label>
                  <Button onClick={() => setToday("base")} variant="outline" size="sm">
                    Today
                  </Button>
                </div>
                <Input
                  id="baseDate"
                  type="date"
                  value={baseDate}
                  onChange={(e) => setBaseDate(e.target.value)}
                />
                {baseDate && (
                  <p className="text-sm text-muted-foreground mt-1">
                    {formatDate(baseDate)}
                  </p>
                )}
              </div>

              <div className="grid grid-cols-2 gap-2">
                <Button
                  onClick={() => setOperation("add")}
                  variant={operation === "add" ? "default" : "outline"}
                >
                  Add Time
                </Button>
                <Button
                  onClick={() => setOperation("subtract")}
                  variant={operation === "subtract" ? "default" : "outline"}
                >
                  Subtract Time
                </Button>
              </div>

              <div className="grid grid-cols-3 gap-4">
                <div>
                  <Label htmlFor="years">Years</Label>
                  <Input
                    id="years"
                    type="number"
                    value={yearsToAdd}
                    onChange={(e) => setYearsToAdd(e.target.value)}
                    placeholder="0"
                  />
                </div>
                <div>
                  <Label htmlFor="months">Months</Label>
                  <Input
                    id="months"
                    type="number"
                    value={monthsToAdd}
                    onChange={(e) => setMonthsToAdd(e.target.value)}
                    placeholder="0"
                  />
                </div>
                <div>
                  <Label htmlFor="days">Days</Label>
                  <Input
                    id="days"
                    type="number"
                    value={daysToAdd}
                    onChange={(e) => setDaysToAdd(e.target.value)}
                    placeholder="0"
                  />
                </div>
              </div>
            </div>

            <div className="p-4 bg-muted rounded-lg">
              <h3 className="font-semibold mb-2">Common Uses</h3>
              <div className="space-y-2 text-sm">
                <p>• Calculate due dates</p>
                <p>• Project deadlines</p>
                <p>• Subscription renewals</p>
                <p>• Event planning</p>
                <p>• Age calculations</p>
              </div>
            </div>
          </div>

          <Button onClick={calculateDateAddSubtract} className="w-full">
            Calculate New Date
          </Button>

          {calculatedDate && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Calculated Date</CardTitle>
                <CardDescription>
                  {operation === "add" ? "Adding" : "Subtracting"} {yearsToAdd || 0} years, {monthsToAdd || 0} months, {daysToAdd || 0} days
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-2xl font-bold text-primary mb-2">{calculatedDate}</p>
                <p className="text-muted-foreground">{formatDate(calculatedDate)}</p>
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>

      <Button onClick={reset} variant="outline" className="w-full">
        Reset All
      </Button>
    </div>
  );
}
