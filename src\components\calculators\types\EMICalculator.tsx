"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

export default function EMICalculator() {
  const [loanAmount, setLoanAmount] = useState<string>("");
  const [interestRate, setInterestRate] = useState<string>("");
  const [loanTenure, setLoanTenure] = useState<string>("");
  const [tenureType, setTenureType] = useState<string>("years");

  const [emi, setEmi] = useState<number | null>(null);
  const [totalAmount, setTotalAmount] = useState<number | null>(null);
  const [totalInterest, setTotalInterest] = useState<number | null>(null);

  const calculateEMI = () => {
    const principal = parseFloat(loanAmount);
    const rate = parseFloat(interestRate);
    const tenure = parseFloat(loanTenure);

    if (!principal || !rate || !tenure) return;

    // Convert annual rate to monthly rate
    const monthlyRate = rate / (12 * 100);
    
    // Convert tenure to months
    const tenureInMonths = tenureType === "years" ? tenure * 12 : tenure;

    // EMI calculation using formula: P * r * (1 + r)^n / ((1 + r)^n - 1)
    const emiValue = (principal * monthlyRate * Math.pow(1 + monthlyRate, tenureInMonths)) / 
                     (Math.pow(1 + monthlyRate, tenureInMonths) - 1);

    const totalAmountValue = emiValue * tenureInMonths;
    const totalInterestValue = totalAmountValue - principal;

    setEmi(Math.round(emiValue));
    setTotalAmount(Math.round(totalAmountValue));
    setTotalInterest(Math.round(totalInterestValue));
  };

  const reset = () => {
    setLoanAmount("");
    setInterestRate("");
    setLoanTenure("");
    setEmi(null);
    setTotalAmount(null);
    setTotalInterest(null);
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      maximumFractionDigits: 0,
    }).format(amount);
  };

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="space-y-4">
          <div>
            <Label htmlFor="loanAmount">Loan Amount (₹)</Label>
            <Input
              id="loanAmount"
              type="number"
              value={loanAmount}
              onChange={(e) => setLoanAmount(e.target.value)}
              placeholder="Enter loan amount"
            />
          </div>

          <div>
            <Label htmlFor="interestRate">Interest Rate (% per annum)</Label>
            <Input
              id="interestRate"
              type="number"
              step="0.1"
              value={interestRate}
              onChange={(e) => setInterestRate(e.target.value)}
              placeholder="Enter interest rate"
            />
          </div>

          <div>
            <Label htmlFor="loanTenure">Loan Tenure</Label>
            <div className="flex gap-2">
              <Input
                id="loanTenure"
                type="number"
                value={loanTenure}
                onChange={(e) => setLoanTenure(e.target.value)}
                placeholder="Enter tenure"
                className="flex-1"
              />
              <Select value={tenureType} onValueChange={setTenureType}>
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="years">Years</SelectItem>
                  <SelectItem value="months">Months</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>

        <div className="space-y-4">
          <div className="p-4 bg-muted rounded-lg">
            <h3 className="font-semibold mb-2">Quick Examples</h3>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span>Personal Loan:</span>
                <span>10-15% interest</span>
              </div>
              <div className="flex justify-between">
                <span>Home Loan:</span>
                <span>7-9% interest</span>
              </div>
              <div className="flex justify-between">
                <span>Car Loan:</span>
                <span>8-12% interest</span>
              </div>
              <div className="flex justify-between">
                <span>Education Loan:</span>
                <span>9-13% interest</span>
              </div>
            </div>
          </div>

          <div className="p-4 bg-primary/10 rounded-lg">
            <h3 className="font-semibold mb-2">EMI Formula</h3>
            <p className="text-sm text-muted-foreground">
              EMI = P × r × (1 + r)ⁿ / ((1 + r)ⁿ - 1)
            </p>
            <div className="text-xs text-muted-foreground mt-2">
              <p>P = Principal loan amount</p>
              <p>r = Monthly interest rate</p>
              <p>n = Number of monthly installments</p>
            </div>
          </div>
        </div>
      </div>

      <div className="flex gap-4">
        <Button onClick={calculateEMI} className="flex-1">
          Calculate EMI
        </Button>
        <Button onClick={reset} variant="outline">
          Reset
        </Button>
      </div>

      {emi && totalAmount && totalInterest && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Monthly EMI</CardTitle>
              <CardDescription>Equated Monthly Installment</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-2xl font-bold text-primary">{formatCurrency(emi)}</p>
              <p className="text-sm text-muted-foreground mt-2">
                Per month for {loanTenure} {tenureType}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Total Amount</CardTitle>
              <CardDescription>Principal + Interest</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-2xl font-bold text-primary">{formatCurrency(totalAmount)}</p>
              <p className="text-sm text-muted-foreground mt-2">
                Total payable amount
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Total Interest</CardTitle>
              <CardDescription>Interest component</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-2xl font-bold text-primary">{formatCurrency(totalInterest)}</p>
              <p className="text-sm text-muted-foreground mt-2">
                {((totalInterest / parseFloat(loanAmount)) * 100).toFixed(1)}% of principal
              </p>
            </CardContent>
          </Card>
        </div>
      )}

      {emi && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Loan Breakdown</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span>Principal Amount:</span>
                <span className="font-semibold">{formatCurrency(parseFloat(loanAmount))}</span>
              </div>
              <div className="flex justify-between items-center">
                <span>Interest Amount:</span>
                <span className="font-semibold">{formatCurrency(totalInterest)}</span>
              </div>
              <div className="border-t pt-2">
                <div className="flex justify-between items-center font-bold">
                  <span>Total Amount:</span>
                  <span>{formatCurrency(totalAmount)}</span>
                </div>
              </div>
              <div className="mt-4">
                <div className="flex justify-between text-sm mb-2">
                  <span>Principal</span>
                  <span>Interest</span>
                </div>
                <div className="w-full bg-muted rounded-full h-4 flex overflow-hidden">
                  <div 
                    className="bg-primary h-full" 
                    style={{ width: `${(parseFloat(loanAmount) / totalAmount) * 100}%` }}
                  ></div>
                  <div 
                    className="bg-destructive h-full" 
                    style={{ width: `${(totalInterest / totalAmount) * 100}%` }}
                  ></div>
                </div>
                <div className="flex justify-between text-xs text-muted-foreground mt-1">
                  <span>{((parseFloat(loanAmount) / totalAmount) * 100).toFixed(1)}%</span>
                  <span>{((totalInterest / totalAmount) * 100).toFixed(1)}%</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
