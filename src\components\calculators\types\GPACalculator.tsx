"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Plus, Trash2 } from "lucide-react";

interface Course {
  id: string;
  name: string;
  grade: string;
  credits: string;
}

export default function GPACalculator() {
  const [courses, setCourses] = useState<Course[]>([
    { id: '1', name: '', grade: '', credits: '' }
  ]);
  const [gpaScale, setGpaScale] = useState<string>("4.0");
  const [gpa, setGpa] = useState<number | null>(null);
  const [totalCredits, setTotalCredits] = useState<number | null>(null);

  // Grade point scales
  const gradeScales = {
    "4.0": {
      "A+": 4.0, "A": 4.0, "A-": 3.7,
      "B+": 3.3, "B": 3.0, "B-": 2.7,
      "C+": 2.3, "C": 2.0, "C-": 1.7,
      "D+": 1.3, "D": 1.0, "D-": 0.7,
      "F": 0.0
    },
    "10.0": {
      "O": 10.0, "A+": 9.0, "A": 8.0, "B+": 7.0,
      "B": 6.0, "C": 5.0, "D": 4.0, "F": 0.0
    },
    "5.0": {
      "A": 5.0, "B": 4.0, "C": 3.0, "D": 2.0, "F": 0.0
    }
  };

  const addCourse = () => {
    const newCourse: Course = {
      id: Date.now().toString(),
      name: '',
      grade: '',
      credits: ''
    };
    setCourses([...courses, newCourse]);
  };

  const removeCourse = (id: string) => {
    if (courses.length > 1) {
      setCourses(courses.filter(course => course.id !== id));
    }
  };

  const updateCourse = (id: string, field: keyof Course, value: string) => {
    setCourses(courses.map(course => 
      course.id === id ? { ...course, [field]: value } : course
    ));
  };

  const calculateGPA = () => {
    const validCourses = courses.filter(course => 
      course.grade && course.credits && parseFloat(course.credits) > 0
    );

    if (validCourses.length === 0) return;

    const scale = gradeScales[gpaScale as keyof typeof gradeScales];
    let totalGradePoints = 0;
    let totalCreditHours = 0;

    validCourses.forEach(course => {
      const gradePoint = scale[course.grade as keyof typeof scale];
      const credits = parseFloat(course.credits);
      
      if (gradePoint !== undefined && credits > 0) {
        totalGradePoints += gradePoint * credits;
        totalCreditHours += credits;
      }
    });

    if (totalCreditHours > 0) {
      const calculatedGPA = totalGradePoints / totalCreditHours;
      setGpa(Math.round(calculatedGPA * 100) / 100);
      setTotalCredits(totalCreditHours);
    }
  };

  const reset = () => {
    setCourses([{ id: '1', name: '', grade: '', credits: '' }]);
    setGpa(null);
    setTotalCredits(null);
  };

  const getGradeOptions = () => {
    const scale = gradeScales[gpaScale as keyof typeof gradeScales];
    return Object.keys(scale);
  };

  const getGPAInterpretation = (gpa: number) => {
    if (gpaScale === "4.0") {
      if (gpa >= 3.7) return { label: "Excellent", color: "text-green-600" };
      if (gpa >= 3.3) return { label: "Good", color: "text-blue-600" };
      if (gpa >= 3.0) return { label: "Satisfactory", color: "text-yellow-600" };
      if (gpa >= 2.0) return { label: "Below Average", color: "text-orange-600" };
      return { label: "Poor", color: "text-red-600" };
    } else if (gpaScale === "10.0") {
      if (gpa >= 8.5) return { label: "Excellent", color: "text-green-600" };
      if (gpa >= 7.0) return { label: "Good", color: "text-blue-600" };
      if (gpa >= 6.0) return { label: "Satisfactory", color: "text-yellow-600" };
      if (gpa >= 4.0) return { label: "Below Average", color: "text-orange-600" };
      return { label: "Poor", color: "text-red-600" };
    } else {
      if (gpa >= 4.0) return { label: "Excellent", color: "text-green-600" };
      if (gpa >= 3.0) return { label: "Good", color: "text-blue-600" };
      if (gpa >= 2.0) return { label: "Satisfactory", color: "text-yellow-600" };
      return { label: "Poor", color: "text-red-600" };
    }
  };

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <Label htmlFor="gpaScale">GPA Scale</Label>
          <Select value={gpaScale} onValueChange={setGpaScale}>
            <SelectTrigger>
              <SelectValue placeholder="Select GPA scale" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="4.0">4.0 Scale (US)</SelectItem>
              <SelectItem value="10.0">10.0 Scale (India)</SelectItem>
              <SelectItem value="5.0">5.0 Scale</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="flex items-end gap-2">
          <Button onClick={addCourse} className="flex items-center gap-2">
            <Plus className="w-4 h-4" />
            Add Course
          </Button>
          <Button onClick={reset} variant="outline">
            Reset All
          </Button>
        </div>
      </div>

      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Courses</h3>
        
        {courses.map((course, index) => (
          <Card key={course.id}>
            <CardContent className="pt-6">
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                  <Label htmlFor={`course-name-${course.id}`}>Course Name</Label>
                  <Input
                    id={`course-name-${course.id}`}
                    value={course.name}
                    onChange={(e) => updateCourse(course.id, 'name', e.target.value)}
                    placeholder="e.g., Mathematics"
                  />
                </div>

                <div>
                  <Label htmlFor={`course-grade-${course.id}`}>Grade</Label>
                  <Select 
                    value={course.grade} 
                    onValueChange={(value) => updateCourse(course.id, 'grade', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select grade" />
                    </SelectTrigger>
                    <SelectContent>
                      {getGradeOptions().map(grade => (
                        <SelectItem key={grade} value={grade}>{grade}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor={`course-credits-${course.id}`}>Credits</Label>
                  <Input
                    id={`course-credits-${course.id}`}
                    type="number"
                    step="0.5"
                    value={course.credits}
                    onChange={(e) => updateCourse(course.id, 'credits', e.target.value)}
                    placeholder="3"
                  />
                </div>

                <div className="flex items-end">
                  <Button
                    onClick={() => removeCourse(course.id)}
                    variant="outline"
                    size="icon"
                    disabled={courses.length === 1}
                    className="w-full md:w-auto"
                  >
                    <Trash2 className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      <Button onClick={calculateGPA} className="w-full">
        Calculate GPA
      </Button>

      {gpa !== null && totalCredits && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Your GPA</CardTitle>
              <CardDescription>Grade Point Average</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-3xl font-bold text-primary">{gpa}</p>
              <p className={`text-sm mt-2 ${getGPAInterpretation(gpa).color}`}>
                {getGPAInterpretation(gpa).label}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Total Credits</CardTitle>
              <CardDescription>Credit hours completed</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-3xl font-bold text-primary">{totalCredits}</p>
              <p className="text-sm text-muted-foreground mt-2">
                {courses.filter(c => c.grade && c.credits).length} courses
              </p>
            </CardContent>
          </Card>
        </div>
      )}

      {gpa !== null && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Grade Breakdown</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {courses
                .filter(course => course.grade && course.credits)
                .map((course, index) => {
                  const scale = gradeScales[gpaScale as keyof typeof gradeScales];
                  const gradePoint = scale[course.grade as keyof typeof scale];
                  const credits = parseFloat(course.credits);
                  
                  return (
                    <div key={course.id} className="flex justify-between items-center py-2 border-b">
                      <div>
                        <span className="font-medium">
                          {course.name || `Course ${index + 1}`}
                        </span>
                        <span className="text-sm text-muted-foreground ml-2">
                          ({credits} credits)
                        </span>
                      </div>
                      <div className="text-right">
                        <span className="font-semibold">{course.grade}</span>
                        <span className="text-sm text-muted-foreground ml-2">
                          ({gradePoint} points)
                        </span>
                      </div>
                    </div>
                  );
                })}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
