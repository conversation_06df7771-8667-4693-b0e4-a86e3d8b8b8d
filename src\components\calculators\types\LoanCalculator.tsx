"use client";

import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { But<PERSON> } from "@/components/ui/button";
import { Slider } from "@/components/ui/slider";
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

export default function LoanCalculator() {
  const [loanAmount, setLoanAmount] = useState<string>("");
  const [interestRate, setInterestRate] = useState<number>(5);
  const [loanTerm, setLoanTerm] = useState<string>("3");
  const [monthlyPayment, setMonthlyPayment] = useState<number | null>(null);
  const [totalPayment, setTotalPayment] = useState<number | null>(null);
  const [totalInterest, setTotalInterest] = useState<number | null>(null);

  // For amortization schedule
  const [amortizationSchedule, setAmortizationSchedule] = useState<Array<{
    month: number;
    payment: number;
    principal: number;
    interest: number;
    balance: number;
  }>>([]);

  const calculateLoan = () => {
    const principal = parseFloat(loanAmount);
    const interest = interestRate / 100 / 12;
    const payments = parseFloat(loanTerm) * 12;

    if (!isNaN(principal) && !isNaN(interest) && !isNaN(payments) && principal > 0 && interest > 0 && payments > 0) {
      // Monthly payment formula: P * (r * (1 + r)^n) / ((1 + r)^n - 1)
      const x = Math.pow(1 + interest, payments);
      const monthly = (principal * x * interest) / (x - 1);

      setMonthlyPayment(monthly);
      setTotalPayment(monthly * payments);
      setTotalInterest((monthly * payments) - principal);

      // Calculate amortization schedule
      let balance = principal;
      const schedule = [];

      for (let month = 1; month <= Math.min(payments, 360); month++) {
        const interestPayment = balance * interest;
        const principalPayment = monthly - interestPayment;
        balance -= principalPayment;

        schedule.push({
          month,
          payment: monthly,
          principal: principalPayment,
          interest: interestPayment,
          balance: Math.max(0, balance)
        });

        // Only show first 12 months for performance
        if (month >= 12) break;
      }

      setAmortizationSchedule(schedule);
    }
  };

  return (
    <div className="space-y-6">
      <Tabs defaultValue="calculator" className="w-full">
        <TabsList className="grid grid-cols-2">
          <TabsTrigger value="calculator">Calculator</TabsTrigger>
          <TabsTrigger value="schedule" disabled={amortizationSchedule.length === 0}>
            Amortization Schedule
          </TabsTrigger>
        </TabsList>

        <TabsContent value="calculator" className="space-y-4 mt-4">
          <Card>
            <CardHeader>
              <CardTitle>Loan Calculator</CardTitle>
              <CardDescription>
                Calculate your loan payments, total interest, and amortization schedule
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="loanAmount">Loan Amount ($)</Label>
                  <div className="relative">
                    <span className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground">
                      $
                    </span>
                    <Input
                      id="loanAmount"
                      type="number"
                      placeholder="e.g., 10000"
                      className="pl-8"
                      value={loanAmount}
                      onChange={(e) => setLoanAmount(e.target.value)}
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="flex justify-between">
                    <Label htmlFor="interestRate">Interest Rate (%)</Label>
                    <span className="font-medium text-primary">{interestRate}%</span>
                  </div>
                  <Slider
                    value={[interestRate]}
                    min={0.5}
                    max={20}
                    step={0.1}
                    onValueChange={(value) => setInterestRate(value[0])}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="loanTerm">Loan Term (Years)</Label>
                  <div className="grid grid-cols-4 gap-2">
                    {[1, 2, 3, 5].map((term) => (
                      <Button
                        key={term}
                        type="button"
                        variant={loanTerm === term.toString() ? "default" : "outline"}
                        onClick={() => setLoanTerm(term.toString())}
                      >
                        {term} {term === 1 ? "Year" : "Years"}
                      </Button>
                    ))}
                  </div>
                </div>
              </div>

              <Button
                onClick={calculateLoan}
                className="w-full"
              >
                Calculate Loan
              </Button>

              {monthlyPayment !== null && (
                <div className="mt-6 space-y-4">
                  <div className="p-4 bg-primary/10 rounded-lg">
                    <h3 className="text-sm font-medium text-muted-foreground mb-1">Monthly Payment</h3>
                    <p className="text-2xl font-bold text-primary">${monthlyPayment.toFixed(2)}</p>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div className="p-4 bg-muted rounded-lg">
                      <h3 className="text-sm font-medium text-muted-foreground mb-1">Total Payment</h3>
                      <p className="text-xl font-bold">${totalPayment?.toFixed(2)}</p>
                    </div>
                    <div className="p-4 bg-muted rounded-lg">
                      <h3 className="text-sm font-medium text-muted-foreground mb-1">Total Interest</h3>
                      <p className="text-xl font-bold">${totalInterest?.toFixed(2)}</p>
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="schedule" className="space-y-4 mt-4">
          <Card>
            <CardHeader>
              <CardTitle>Amortization Schedule</CardTitle>
              <CardDescription>
                Monthly breakdown of your loan payments
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <table className="w-full border-collapse">
                  <thead>
                    <tr className="border-b">
                      <th className="text-left py-2 px-4">Month</th>
                      <th className="text-right py-2 px-4">Payment</th>
                      <th className="text-right py-2 px-4">Principal</th>
                      <th className="text-right py-2 px-4">Interest</th>
                      <th className="text-right py-2 px-4">Remaining</th>
                    </tr>
                  </thead>
                  <tbody>
                    {amortizationSchedule.map((row) => (
                      <tr key={row.month} className="border-b border-border">
                        <td className="py-2 px-4">{row.month}</td>
                        <td className="text-right py-2 px-4">${row.payment.toFixed(2)}</td>
                        <td className="text-right py-2 px-4">${row.principal.toFixed(2)}</td>
                        <td className="text-right py-2 px-4">${row.interest.toFixed(2)}</td>
                        <td className="text-right py-2 px-4">${row.balance.toFixed(2)}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
              <p className="text-xs text-muted-foreground mt-4">
                Showing first 12 months of payments. Total loan term: {loanTerm} years ({parseInt(loanTerm) * 12} payments)
              </p>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
