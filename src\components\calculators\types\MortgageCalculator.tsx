"use client";

import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Slider } from "@/components/ui/slider";

export default function MortgageCalculator() {
  const [loanAmount, setLoanAmount] = useState<string>("");
  const [interestRate, setInterestRate] = useState<number>(4.5);
  const [loanTerm, setLoanTerm] = useState<string>("30");
  const [monthlyPayment, setMonthlyPayment] = useState<number | null>(null);
  const [totalPayment, setTotalPayment] = useState<number | null>(null);
  const [totalInterest, setTotalInterest] = useState<number | null>(null);

  const calculateMortgage = () => {
    const principal = parseFloat(loanAmount);
    const interest = interestRate / 100 / 12;
    const payments = parseFloat(loanTerm) * 12;

    if (!isNaN(principal) && !isNaN(interest) && !isNaN(payments) && principal > 0 && interest > 0 && payments > 0) {
      // Monthly payment formula: P * (r * (1 + r)^n) / ((1 + r)^n - 1)
      const x = Math.pow(1 + interest, payments);
      const monthly = (principal * x * interest) / (x - 1);

      setMonthlyPayment(monthly);
      setTotalPayment(monthly * payments);
      setTotalInterest((monthly * payments) - principal);
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Mortgage Calculator</CardTitle>
          <CardDescription>
            Calculate your monthly mortgage payments and total interest
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="loanAmount">Loan Amount ($)</Label>
              <div className="relative">
                <span className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground">
                  $
                </span>
                <Input
                  id="loanAmount"
                  type="number"
                  placeholder="e.g., 250000"
                  className="pl-8"
                  value={loanAmount}
                  onChange={(e) => setLoanAmount(e.target.value)}
                />
              </div>
            </div>

            <div className="space-y-2">
              <div className="flex justify-between">
                <Label htmlFor="interestRate">Interest Rate (%)</Label>
                <span className="font-medium text-primary">{interestRate}%</span>
              </div>
              <Slider
                value={[interestRate]}
                min={0.5}
                max={15}
                step={0.1}
                onValueChange={(value) => setInterestRate(value[0])}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="loanTerm">Loan Term (Years)</Label>
              <div className="grid grid-cols-3 gap-2">
                {[15, 20, 30].map((term) => (
                  <Button
                    key={term}
                    type="button"
                    variant={loanTerm === term.toString() ? "default" : "outline"}
                    onClick={() => setLoanTerm(term.toString())}
                  >
                    {term} Years
                  </Button>
                ))}
              </div>
            </div>
          </div>

          <Button
            onClick={calculateMortgage}
            className="w-full"
          >
            Calculate Mortgage
          </Button>

          {monthlyPayment !== null && (
            <div className="mt-6 space-y-4">
              <div className="p-4 bg-primary/10 rounded-lg">
                <h3 className="text-sm font-medium text-muted-foreground mb-1">Monthly Payment</h3>
                <p className="text-2xl font-bold text-primary">${monthlyPayment.toFixed(2)}</p>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="p-4 bg-muted rounded-lg">
                  <h3 className="text-sm font-medium text-muted-foreground mb-1">Total Payment</h3>
                  <p className="text-xl font-bold">${totalPayment?.toFixed(2)}</p>
                </div>
                <div className="p-4 bg-muted rounded-lg">
                  <h3 className="text-sm font-medium text-muted-foreground mb-1">Total Interest</h3>
                  <p className="text-xl font-bold">${totalInterest?.toFixed(2)}</p>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
