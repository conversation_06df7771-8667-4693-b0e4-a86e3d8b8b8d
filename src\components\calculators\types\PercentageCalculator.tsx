"use client";

import { useState } from "react";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";

export default function PercentageCalculator() {
  const [activeTab, setActiveTab] = useState("basic");
  
  // Basic percentage calculation
  const [value, setValue] = useState<string>("");
  const [percentage, setPercentage] = useState<string>("");
  const [basicResult, setBasicResult] = useState<number | null>(null);
  
  // Percentage increase/decrease
  const [originalValue, setOriginalValue] = useState<string>("");
  const [changePercentage, setChangePercentage] = useState<string>("");
  const [changeResult, setChangeResult] = useState<number | null>(null);
  
  // Percentage difference
  const [value1, setValue1] = useState<string>("");
  const [value2, setValue2] = useState<string>("");
  const [differenceResult, setDifferenceResult] = useState<number | null>(null);

  // Calculate basic percentage
  const calculateBasicPercentage = () => {
    const numValue = parseFloat(value);
    const numPercentage = parseFloat(percentage);
    
    if (!isNaN(numValue) && !isNaN(numPercentage)) {
      setBasicResult((numValue * numPercentage) / 100);
    }
  };
  
  // Calculate percentage increase/decrease
  const calculatePercentageChange = () => {
    const numOriginal = parseFloat(originalValue);
    const numPercentage = parseFloat(changePercentage);
    
    if (!isNaN(numOriginal) && !isNaN(numPercentage)) {
      const change = numOriginal * (numPercentage / 100);
      setChangeResult(numOriginal + change);
    }
  };
  
  // Calculate percentage difference
  const calculatePercentageDifference = () => {
    const num1 = parseFloat(value1);
    const num2 = parseFloat(value2);
    
    if (!isNaN(num1) && !isNaN(num2) && num1 !== 0) {
      const difference = ((num2 - num1) / num1) * 100;
      setDifferenceResult(difference);
    }
  };

  return (
    <div className="space-y-6">
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid grid-cols-3">
          <TabsTrigger value="basic">Basic Percentage</TabsTrigger>
          <TabsTrigger value="change">Increase/Decrease</TabsTrigger>
          <TabsTrigger value="difference">Percentage Difference</TabsTrigger>
        </TabsList>
        
        {/* Basic Percentage Tab */}
        <TabsContent value="basic" className="space-y-4 mt-4">
          <Card>
            <CardHeader>
              <CardTitle>Basic Percentage Calculation</CardTitle>
              <CardDescription>
                Calculate a percentage of a number
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="value">Value</Label>
                  <Input
                    id="value"
                    type="number"
                    placeholder="Enter a number"
                    value={value}
                    onChange={(e) => setValue(e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="percentage">Percentage (%)</Label>
                  <Input
                    id="percentage"
                    type="number"
                    placeholder="Enter percentage"
                    value={percentage}
                    onChange={(e) => setPercentage(e.target.value)}
                  />
                </div>
              </div>
              
              <Button onClick={calculateBasicPercentage} className="w-full">
                Calculate
              </Button>
              
              {basicResult !== null && (
                <div className="mt-4 p-4 bg-primary/10 rounded-md">
                  <p className="text-center">
                    <span className="font-semibold">{percentage}%</span> of{" "}
                    <span className="font-semibold">{value}</span> is{" "}
                    <span className="font-bold text-primary">{basicResult.toFixed(2)}</span>
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
        
        {/* Percentage Increase/Decrease Tab */}
        <TabsContent value="change" className="space-y-4 mt-4">
          <Card>
            <CardHeader>
              <CardTitle>Percentage Increase/Decrease</CardTitle>
              <CardDescription>
                Calculate a value after percentage increase or decrease
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="originalValue">Original Value</Label>
                  <Input
                    id="originalValue"
                    type="number"
                    placeholder="Enter original value"
                    value={originalValue}
                    onChange={(e) => setOriginalValue(e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="changePercentage">
                    Percentage Change (%)
                  </Label>
                  <Input
                    id="changePercentage"
                    type="number"
                    placeholder="Enter percentage (negative for decrease)"
                    value={changePercentage}
                    onChange={(e) => setChangePercentage(e.target.value)}
                  />
                </div>
              </div>
              
              <Button onClick={calculatePercentageChange} className="w-full">
                Calculate
              </Button>
              
              {changeResult !== null && (
                <div className="mt-4 p-4 bg-primary/10 rounded-md">
                  <p className="text-center">
                    <span className="font-semibold">{originalValue}</span>{" "}
                    {parseFloat(changePercentage) >= 0 ? "increased" : "decreased"} by{" "}
                    <span className="font-semibold">
                      {Math.abs(parseFloat(changePercentage))}%
                    </span>{" "}
                    is{" "}
                    <span className="font-bold text-primary">
                      {changeResult.toFixed(2)}
                    </span>
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
        
        {/* Percentage Difference Tab */}
        <TabsContent value="difference" className="space-y-4 mt-4">
          <Card>
            <CardHeader>
              <CardTitle>Percentage Difference</CardTitle>
              <CardDescription>
                Calculate the percentage difference between two values
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="value1">First Value</Label>
                  <Input
                    id="value1"
                    type="number"
                    placeholder="Enter first value"
                    value={value1}
                    onChange={(e) => setValue1(e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="value2">Second Value</Label>
                  <Input
                    id="value2"
                    type="number"
                    placeholder="Enter second value"
                    value={value2}
                    onChange={(e) => setValue2(e.target.value)}
                  />
                </div>
              </div>
              
              <Button onClick={calculatePercentageDifference} className="w-full">
                Calculate
              </Button>
              
              {differenceResult !== null && (
                <div className="mt-4 p-4 bg-primary/10 rounded-md">
                  <p className="text-center">
                    The percentage difference between{" "}
                    <span className="font-semibold">{value1}</span> and{" "}
                    <span className="font-semibold">{value2}</span> is{" "}
                    <span className="font-bold text-primary">
                      {differenceResult.toFixed(2)}%
                    </span>
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
