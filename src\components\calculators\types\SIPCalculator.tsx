"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

export default function SIPCalculator() {
  const [monthlyInvestment, setMonthlyInvestment] = useState<string>("");
  const [expectedReturn, setExpectedReturn] = useState<string>("");
  const [timePeriod, setTimePeriod] = useState<string>("");
  const [frequency, setFrequency] = useState<string>("monthly");

  const [maturityAmount, setMaturityAmount] = useState<number | null>(null);
  const [totalInvestment, setTotalInvestment] = useState<number | null>(null);
  const [totalReturns, setTotalReturns] = useState<number | null>(null);

  const calculateSIP = () => {
    const monthlyAmount = parseFloat(monthlyInvestment);
    const annualRate = parseFloat(expectedReturn);
    const years = parseFloat(timePeriod);

    if (!monthlyAmount || !annualRate || !years) return;

    // Calculate number of payments based on frequency
    let paymentsPerYear = 12;
    let paymentAmount = monthlyAmount;

    switch (frequency) {
      case "weekly":
        paymentsPerYear = 52;
        paymentAmount = monthlyAmount / 4.33; // Approximate weekly amount
        break;
      case "quarterly":
        paymentsPerYear = 4;
        paymentAmount = monthlyAmount * 3;
        break;
      case "yearly":
        paymentsPerYear = 1;
        paymentAmount = monthlyAmount * 12;
        break;
      default: // monthly
        paymentsPerYear = 12;
        paymentAmount = monthlyAmount;
    }

    const totalPayments = years * paymentsPerYear;
    const ratePerPeriod = annualRate / (paymentsPerYear * 100);

    // SIP Future Value Formula: PMT * [((1 + r)^n - 1) / r] * (1 + r)
    const futureValue = paymentAmount *
      (((Math.pow(1 + ratePerPeriod, totalPayments) - 1) / ratePerPeriod) * (1 + ratePerPeriod));

    const totalInvested = paymentAmount * totalPayments;
    const totalGains = futureValue - totalInvested;

    setMaturityAmount(Math.round(futureValue));
    setTotalInvestment(Math.round(totalInvested));
    setTotalReturns(Math.round(totalGains));
  };

  const reset = () => {
    setMonthlyInvestment("");
    setExpectedReturn("");
    setTimePeriod("");
    setMaturityAmount(null);
    setTotalInvestment(null);
    setTotalReturns(null);
  };

  const formatCurrency = (amount: number | null) => {
    if (amount === null) return '₹0';
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const getFrequencyLabel = () => {
    switch (frequency) {
      case "weekly": return "Weekly";
      case "quarterly": return "Quarterly";
      case "yearly": return "Yearly";
      default: return "Monthly";
    }
  };

  const getPaymentAmount = () => {
    const monthlyAmount = parseFloat(monthlyInvestment) || 0;
    switch (frequency) {
      case "weekly": return Math.round(monthlyAmount / 4.33);
      case "quarterly": return monthlyAmount * 3;
      case "yearly": return monthlyAmount * 12;
      default: return monthlyAmount;
    }
  };

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="space-y-4">
          <div>
            <Label htmlFor="monthlyInvestment">Monthly Investment Amount (₹)</Label>
            <Input
              id="monthlyInvestment"
              type="number"
              value={monthlyInvestment}
              onChange={(e) => setMonthlyInvestment(e.target.value)}
              placeholder="Enter monthly investment"
            />
          </div>

          <div>
            <Label htmlFor="expectedReturn">Expected Annual Return (%)</Label>
            <Input
              id="expectedReturn"
              type="number"
              step="0.1"
              value={expectedReturn}
              onChange={(e) => setExpectedReturn(e.target.value)}
              placeholder="Enter expected return rate"
            />
          </div>

          <div>
            <Label htmlFor="timePeriod">Investment Period (Years)</Label>
            <Input
              id="timePeriod"
              type="number"
              value={timePeriod}
              onChange={(e) => setTimePeriod(e.target.value)}
              placeholder="Enter investment period"
            />
          </div>

          <div>
            <Label htmlFor="frequency">Investment Frequency</Label>
            <Select value={frequency} onValueChange={setFrequency}>
              <SelectTrigger>
                <SelectValue placeholder="Select frequency" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="weekly">Weekly</SelectItem>
                <SelectItem value="monthly">Monthly</SelectItem>
                <SelectItem value="quarterly">Quarterly</SelectItem>
                <SelectItem value="yearly">Yearly</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <div className="space-y-4">
          <div className="p-4 bg-muted rounded-lg">
            <h3 className="font-semibold mb-2">Investment Summary</h3>
            {monthlyInvestment && (
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span>{getFrequencyLabel()} Amount:</span>
                  <span className="font-semibold">₹{getPaymentAmount().toLocaleString()}</span>
                </div>
                <div className="flex justify-between">
                  <span>Annual Investment:</span>
                  <span className="font-semibold">₹{(parseFloat(monthlyInvestment) * 12).toLocaleString()}</span>
                </div>
              </div>
            )}
          </div>

          <div className="p-4 bg-primary/10 rounded-lg">
            <h3 className="font-semibold mb-2">SIP Benefits</h3>
            <ul className="text-sm space-y-1">
              <li>• Rupee Cost Averaging</li>
              <li>• Power of Compounding</li>
              <li>• Disciplined Investing</li>
              <li>• Flexibility to start/stop</li>
              <li>• Lower risk through averaging</li>
            </ul>
          </div>

          <div className="p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
            <h3 className="font-semibold mb-2">Typical Returns</h3>
            <div className="space-y-1 text-sm">
              <div className="flex justify-between">
                <span>Equity Funds:</span>
                <span>12-15% p.a.</span>
              </div>
              <div className="flex justify-between">
                <span>Hybrid Funds:</span>
                <span>8-12% p.a.</span>
              </div>
              <div className="flex justify-between">
                <span>Debt Funds:</span>
                <span>6-8% p.a.</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="flex gap-4">
        <Button onClick={calculateSIP} className="flex-1">
          Calculate SIP Returns
        </Button>
        <Button onClick={reset} variant="outline">
          Reset
        </Button>
      </div>

      {maturityAmount && totalInvestment && totalReturns && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Maturity Amount</CardTitle>
              <CardDescription>Total value at maturity</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-2xl font-bold text-primary">{formatCurrency(maturityAmount)}</p>
              <p className="text-sm text-muted-foreground mt-2">
                After {timePeriod} years
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Total Investment</CardTitle>
              <CardDescription>Amount you will invest</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-2xl font-bold text-primary">{formatCurrency(totalInvestment)}</p>
              <p className="text-sm text-muted-foreground mt-2">
                Principal amount
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Total Returns</CardTitle>
              <CardDescription>Wealth gained</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-2xl font-bold text-primary">{formatCurrency(totalReturns)}</p>
              <p className="text-sm text-muted-foreground mt-2">
                {totalReturns && totalInvestment ? ((totalReturns / totalInvestment) * 100).toFixed(1) : '0'}% gain
              </p>
            </CardContent>
          </Card>
        </div>
      )}

      {maturityAmount && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Investment Breakdown</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span>Principal Investment:</span>
                <span className="font-semibold">{formatCurrency(totalInvestment)}</span>
              </div>
              <div className="flex justify-between items-center">
                <span>Returns Generated:</span>
                <span className="font-semibold">{formatCurrency(totalReturns)}</span>
              </div>
              <div className="border-t pt-2">
                <div className="flex justify-between items-center font-bold">
                  <span>Total Maturity Value:</span>
                  <span>{formatCurrency(maturityAmount)}</span>
                </div>
              </div>
              <div className="mt-4">
                <div className="flex justify-between text-sm mb-2">
                  <span>Investment</span>
                  <span>Returns</span>
                </div>
                <div className="w-full bg-muted rounded-full h-4 flex overflow-hidden">
                  <div
                    className="bg-primary h-full"
                    style={{ width: `${totalInvestment && maturityAmount ? (totalInvestment / maturityAmount) * 100 : 0}%` }}
                  ></div>
                  <div
                    className="bg-green-500 h-full"
                    style={{ width: `${totalReturns && maturityAmount ? (totalReturns / maturityAmount) * 100 : 0}%` }}
                  ></div>
                </div>
                <div className="flex justify-between text-xs text-muted-foreground mt-1">
                  <span>{totalInvestment && maturityAmount ? ((totalInvestment / maturityAmount) * 100).toFixed(1) : '0'}%</span>
                  <span>{totalReturns && maturityAmount ? ((totalReturns / maturityAmount) * 100).toFixed(1) : '0'}%</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
