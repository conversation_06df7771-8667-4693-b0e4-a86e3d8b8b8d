"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";

export default function TextToolsCalculator() {
  const [inputText, setInputText] = useState<string>("");
  const [outputText, setOutputText] = useState<string>("");
  const [activeTab, setActiveTab] = useState<string>("case");

  // Text statistics
  const [stats, setStats] = useState({
    characters: 0,
    charactersNoSpaces: 0,
    words: 0,
    sentences: 0,
    paragraphs: 0,
    lines: 0,
  });

  // Calculate text statistics
  useEffect(() => {
    const text = inputText;
    const charactersNoSpaces = text.replace(/\s/g, '').length;
    const words = text.trim() ? text.trim().split(/\s+/).length : 0;
    const sentences = text.trim() ? text.split(/[.!?]+/).filter(s => s.trim().length > 0).length : 0;
    const paragraphs = text.trim() ? text.split(/\n\s*\n/).filter(p => p.trim().length > 0).length : 0;
    const lines = text ? text.split('\n').length : 0;

    setStats({
      characters: text.length,
      charactersNoSpaces,
      words,
      sentences,
      paragraphs,
      lines,
    });
  }, [inputText]);

  // Case conversion functions
  const convertToUpperCase = () => {
    setOutputText(inputText.toUpperCase());
  };

  const convertToLowerCase = () => {
    setOutputText(inputText.toLowerCase());
  };

  const convertToTitleCase = () => {
    const titleCase = inputText.replace(/\w\S*/g, (txt) => 
      txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase()
    );
    setOutputText(titleCase);
  };

  const convertToSentenceCase = () => {
    const sentenceCase = inputText.toLowerCase().replace(/(^\s*\w|[.!?]\s*\w)/g, (c) => 
      c.toUpperCase()
    );
    setOutputText(sentenceCase);
  };

  const convertToCamelCase = () => {
    const camelCase = inputText
      .replace(/(?:^\w|[A-Z]|\b\w)/g, (word, index) => 
        index === 0 ? word.toLowerCase() : word.toUpperCase()
      )
      .replace(/\s+/g, '');
    setOutputText(camelCase);
  };

  const convertToPascalCase = () => {
    const pascalCase = inputText
      .replace(/(?:^\w|[A-Z]|\b\w)/g, (word) => word.toUpperCase())
      .replace(/\s+/g, '');
    setOutputText(pascalCase);
  };

  const convertToSnakeCase = () => {
    const snakeCase = inputText
      .replace(/\W+/g, ' ')
      .split(/ |\B(?=[A-Z])/)
      .map(word => word.toLowerCase())
      .join('_');
    setOutputText(snakeCase);
  };

  const convertToKebabCase = () => {
    const kebabCase = inputText
      .replace(/\W+/g, ' ')
      .split(/ |\B(?=[A-Z])/)
      .map(word => word.toLowerCase())
      .join('-');
    setOutputText(kebabCase);
  };

  // Text manipulation functions
  const reverseText = () => {
    setOutputText(inputText.split('').reverse().join(''));
  };

  const removeExtraSpaces = () => {
    setOutputText(inputText.replace(/\s+/g, ' ').trim());
  };

  const removeAllSpaces = () => {
    setOutputText(inputText.replace(/\s/g, ''));
  };

  const addLineNumbers = () => {
    const lines = inputText.split('\n');
    const numberedLines = lines.map((line, index) => `${index + 1}. ${line}`);
    setOutputText(numberedLines.join('\n'));
  };

  const sortLines = () => {
    const lines = inputText.split('\n');
    const sortedLines = lines.sort();
    setOutputText(sortedLines.join('\n'));
  };

  const removeDuplicateLines = () => {
    const lines = inputText.split('\n');
    const uniqueLines = [...new Set(lines)];
    setOutputText(uniqueLines.join('\n'));
  };

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(outputText);
      // You could add a toast notification here
    } catch (err) {
      console.error('Failed to copy text: ', err);
    }
  };

  const clearAll = () => {
    setInputText("");
    setOutputText("");
  };

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Input Area */}
        <div className="lg:col-span-2 space-y-4">
          <div>
            <Label htmlFor="inputText">Input Text</Label>
            <textarea
              id="inputText"
              value={inputText}
              onChange={(e) => setInputText(e.target.value)}
              placeholder="Enter your text here..."
              className="w-full h-40 p-3 border border-border rounded-md bg-background resize-none"
            />
          </div>

          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid grid-cols-3">
              <TabsTrigger value="case">Case Conversion</TabsTrigger>
              <TabsTrigger value="manipulation">Text Manipulation</TabsTrigger>
              <TabsTrigger value="formatting">Formatting</TabsTrigger>
            </TabsList>

            <TabsContent value="case" className="space-y-2">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                <Button onClick={convertToUpperCase} variant="outline" size="sm">
                  UPPERCASE
                </Button>
                <Button onClick={convertToLowerCase} variant="outline" size="sm">
                  lowercase
                </Button>
                <Button onClick={convertToTitleCase} variant="outline" size="sm">
                  Title Case
                </Button>
                <Button onClick={convertToSentenceCase} variant="outline" size="sm">
                  Sentence case
                </Button>
                <Button onClick={convertToCamelCase} variant="outline" size="sm">
                  camelCase
                </Button>
                <Button onClick={convertToPascalCase} variant="outline" size="sm">
                  PascalCase
                </Button>
                <Button onClick={convertToSnakeCase} variant="outline" size="sm">
                  snake_case
                </Button>
                <Button onClick={convertToKebabCase} variant="outline" size="sm">
                  kebab-case
                </Button>
              </div>
            </TabsContent>

            <TabsContent value="manipulation" className="space-y-2">
              <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                <Button onClick={reverseText} variant="outline" size="sm">
                  Reverse Text
                </Button>
                <Button onClick={removeExtraSpaces} variant="outline" size="sm">
                  Remove Extra Spaces
                </Button>
                <Button onClick={removeAllSpaces} variant="outline" size="sm">
                  Remove All Spaces
                </Button>
                <Button onClick={sortLines} variant="outline" size="sm">
                  Sort Lines
                </Button>
                <Button onClick={removeDuplicateLines} variant="outline" size="sm">
                  Remove Duplicates
                </Button>
              </div>
            </TabsContent>

            <TabsContent value="formatting" className="space-y-2">
              <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                <Button onClick={addLineNumbers} variant="outline" size="sm">
                  Add Line Numbers
                </Button>
              </div>
            </TabsContent>
          </Tabs>

          {/* Output Area */}
          <div>
            <div className="flex justify-between items-center mb-2">
              <Label htmlFor="outputText">Output Text</Label>
              <div className="flex gap-2">
                <Button onClick={copyToClipboard} variant="outline" size="sm" disabled={!outputText}>
                  Copy
                </Button>
                <Button onClick={clearAll} variant="outline" size="sm">
                  Clear All
                </Button>
              </div>
            </div>
            <textarea
              id="outputText"
              value={outputText}
              readOnly
              placeholder="Converted text will appear here..."
              className="w-full h-40 p-3 border border-border rounded-md bg-muted resize-none"
            />
          </div>
        </div>

        {/* Statistics Panel */}
        <div className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Text Statistics</CardTitle>
              <CardDescription>Real-time text analysis</CardDescription>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex justify-between">
                <span>Characters:</span>
                <span className="font-semibold">{stats.characters.toLocaleString()}</span>
              </div>
              <div className="flex justify-between">
                <span>Characters (no spaces):</span>
                <span className="font-semibold">{stats.charactersNoSpaces.toLocaleString()}</span>
              </div>
              <div className="flex justify-between">
                <span>Words:</span>
                <span className="font-semibold">{stats.words.toLocaleString()}</span>
              </div>
              <div className="flex justify-between">
                <span>Sentences:</span>
                <span className="font-semibold">{stats.sentences.toLocaleString()}</span>
              </div>
              <div className="flex justify-between">
                <span>Paragraphs:</span>
                <span className="font-semibold">{stats.paragraphs.toLocaleString()}</span>
              </div>
              <div className="flex justify-between">
                <span>Lines:</span>
                <span className="font-semibold">{stats.lines.toLocaleString()}</span>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Reading Time</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span>Average (200 WPM):</span>
                  <span className="font-semibold">
                    {Math.ceil(stats.words / 200)} min
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>Fast (300 WPM):</span>
                  <span className="font-semibold">
                    {Math.ceil(stats.words / 300)} min
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>Slow (150 WPM):</span>
                  <span className="font-semibold">
                    {Math.ceil(stats.words / 150)} min
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
