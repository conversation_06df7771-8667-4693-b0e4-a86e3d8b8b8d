"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Slider } from "@/components/ui/slider";

export default function TipCalculator() {
  const [billAmount, setBillAmount] = useState<string>("");
  const [tipPercentage, setTipPercentage] = useState<number>(15);
  const [numberOfPeople, setNumberOfPeople] = useState<string>("1");

  const [tipAmount, setTipAmount] = useState<number>(0);
  const [totalAmount, setTotalAmount] = useState<number>(0);
  const [amountPerPerson, setAmountPerPerson] = useState<number>(0);

  // Calculate tip and total whenever inputs change
  useEffect(() => {
    calculateTip();
  }, [billAmount, tipPercentage, numberOfPeople]);

  const calculateTip = () => {
    const bill = parseFloat(billAmount);
    const people = parseInt(numberOfPeople) || 1;

    if (!isNaN(bill) && bill > 0) {
      const tip = bill * (tipPercentage / 100);
      const total = bill + tip;
      const perPerson = total / people;

      setTipAmount(tip);
      setTotalAmount(total);
      setAmountPerPerson(perPerson);
    } else {
      setTipAmount(0);
      setTotalAmount(0);
      setAmountPerPerson(0);
    }
  };

  // Handle tip percentage presets
  const handleTipPreset = (percentage: number) => {
    setTipPercentage(percentage);
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Tip Calculator</CardTitle>
          <CardDescription>
            Calculate tip amount and split the bill among people
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Bill Amount */}
          <div className="space-y-2">
            <Label htmlFor="billAmount">Bill Amount</Label>
            <div className="relative">
              <span className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground">
                $
              </span>
              <Input
                id="billAmount"
                type="number"
                placeholder="0.00"
                className="pl-8"
                value={billAmount}
                onChange={(e) => setBillAmount(e.target.value)}
              />
            </div>
          </div>

          {/* Tip Percentage */}
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <Label>Tip Percentage</Label>
              <span className="text-lg font-semibold text-primary">
                {tipPercentage}%
              </span>
            </div>

            <Slider
              value={[tipPercentage]}
              min={0}
              max={30}
              step={1}
              onValueChange={(value) => setTipPercentage(value[0])}
              className="my-4"
            />

            <div className="flex justify-between gap-2">
              {[10, 15, 18, 20, 25].map((percentage) => (
                <Button
                  key={percentage}
                  variant={tipPercentage === percentage ? "default" : "outline"}
                  size="sm"
                  onClick={() => handleTipPreset(percentage)}
                  className="flex-1"
                >
                  {percentage}%
                </Button>
              ))}
            </div>
          </div>

          {/* Number of People */}
          <div className="space-y-2">
            <Label htmlFor="numberOfPeople">Number of People</Label>
            <Input
              id="numberOfPeople"
              type="number"
              min="1"
              placeholder="1"
              value={numberOfPeople}
              onChange={(e) => setNumberOfPeople(e.target.value)}
            />
          </div>
        </CardContent>
      </Card>

      {/* Results */}
      <Card className="bg-primary/5 border-primary/20">
        <CardHeader>
          <CardTitle>Summary</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-1">
              <p className="text-sm text-muted-foreground">Tip Amount</p>
              <p className="text-2xl font-bold text-primary">
                ${tipAmount.toFixed(2)}
              </p>
            </div>
            <div className="space-y-1">
              <p className="text-sm text-muted-foreground">Total Amount</p>
              <p className="text-2xl font-bold">
                ${totalAmount.toFixed(2)}
              </p>
            </div>
          </div>

          {parseInt(numberOfPeople) > 1 && (
            <div className="pt-4 border-t border-border">
              <div className="space-y-1">
                <p className="text-sm text-muted-foreground">
                  Amount Per Person
                </p>
                <p className="text-2xl font-bold text-primary">
                  ${amountPerPerson.toFixed(2)}
                </p>
                <p className="text-xs text-muted-foreground">
                  Split evenly among {numberOfPeople} people
                </p>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
