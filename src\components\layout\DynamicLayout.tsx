'use client';

import { ReactNode } from 'react';
import { motion } from 'framer-motion';
import { useTheme } from '@/hooks/useTheme';
import * as LucideIcons from 'lucide-react';
import { PageData, ContentType } from '@/lib/routing';

interface DynamicLayoutProps {
  type: ContentType;
  data: PageData;
  children: ReactNode;
}

export default function DynamicLayout({ type, data, children }: DynamicLayoutProps) {
  const { theme } = useTheme();

  // Get the appropriate icon component
  const getIconComponent = (iconName: string) => {
    if (!iconName) return null;
    
    // Convert icon name to PascalCase for Lucide icons
    const iconComponentName = iconName
      .split('-')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join('');
    
    const IconComponent = (LucideIcons as any)[iconComponentName];
    return IconComponent ? <IconComponent className="w-8 h-8" /> : null;
  };

  // Type-specific styling
  const getTypeStyles = () => {
    switch (type) {
      case 'calculators':
        return {
          gradient: theme === 'dark' 
            ? 'from-yellow-900/20 to-orange-900/20' 
            : 'from-yellow-50 to-orange-50',
          iconBg: theme === 'dark' 
            ? 'bg-yellow-900/30 text-yellow-400' 
            : 'bg-yellow-100 text-yellow-600',
          badge: theme === 'dark' 
            ? 'bg-yellow-900/50 text-yellow-300' 
            : 'bg-yellow-100 text-yellow-700'
        };
      case 'tools':
        return {
          gradient: theme === 'dark' 
            ? 'from-blue-900/20 to-indigo-900/20' 
            : 'from-blue-50 to-indigo-50',
          iconBg: theme === 'dark' 
            ? 'bg-blue-900/30 text-blue-400' 
            : 'bg-blue-100 text-blue-600',
          badge: theme === 'dark' 
            ? 'bg-blue-900/50 text-blue-300' 
            : 'bg-blue-100 text-blue-700'
        };
      case 'blogs':
        return {
          gradient: theme === 'dark' 
            ? 'from-green-900/20 to-emerald-900/20' 
            : 'from-green-50 to-emerald-50',
          iconBg: theme === 'dark' 
            ? 'bg-green-900/30 text-green-400' 
            : 'bg-green-100 text-green-600',
          badge: theme === 'dark' 
            ? 'bg-green-900/50 text-green-300' 
            : 'bg-green-100 text-green-700'
        };
      default:
        return {
          gradient: theme === 'dark' 
            ? 'from-gray-900/20 to-slate-900/20' 
            : 'from-gray-50 to-slate-50',
          iconBg: theme === 'dark' 
            ? 'bg-gray-900/30 text-gray-400' 
            : 'bg-gray-100 text-gray-600',
          badge: theme === 'dark' 
            ? 'bg-gray-900/50 text-gray-300' 
            : 'bg-gray-100 text-gray-700'
        };
    }
  };

  const styles = getTypeStyles();

  return (
    <div className={`min-h-screen bg-gradient-to-br ${styles.gradient} ${
      theme === 'dark' ? 'text-white' : 'text-gray-900'
    }`}>
      {/* Header Section */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="relative overflow-hidden"
      >
        <div className="container mx-auto px-4 py-12 sm:py-16">
          <div className="max-w-4xl mx-auto text-center">
            {/* Icon */}
            {data.icon && (
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
                className={`inline-flex items-center justify-center w-16 h-16 rounded-2xl ${styles.iconBg} mb-6`}
              >
                {getIconComponent(data.icon)}
              </motion.div>
            )}

            {/* Title */}
            <motion.h1
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3, duration: 0.5 }}
              className="text-3xl sm:text-4xl lg:text-5xl font-bold mb-4"
            >
              {data.title}
            </motion.h1>

            {/* Description */}
            <motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4, duration: 0.5 }}
              className={`text-lg sm:text-xl mb-6 ${
                theme === 'dark' ? 'text-gray-300' : 'text-gray-600'
              }`}
            >
              {data.description}
            </motion.p>

            {/* Metadata Badges */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.5, duration: 0.5 }}
              className="flex flex-wrap justify-center gap-3 mb-8"
            >
              {/* Category Badge */}
              {data.category && (
                <span className={`px-3 py-1 rounded-full text-sm font-medium ${styles.badge}`}>
                  {data.category.charAt(0).toUpperCase() + data.category.slice(1)}
                </span>
              )}

              {/* Popular Badge */}
              {data.popular && (
                <span className="px-3 py-1 rounded-full text-sm font-medium bg-gradient-to-r from-purple-500 to-pink-500 text-white">
                  Popular
                </span>
              )}

              {/* Coming Soon Badge */}
              {data.comingSoon && (
                <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                  theme === 'dark' 
                    ? 'bg-orange-900/50 text-orange-300' 
                    : 'bg-orange-100 text-orange-700'
                }`}>
                  Coming Soon
                </span>
              )}

              {/* Tool-specific badges */}
              {type === 'tools' && data.inputFormat && data.outputFormat && (
                <span className={`px-3 py-1 rounded-full text-sm font-medium ${styles.badge}`}>
                  {data.inputFormat} → {data.outputFormat}
                </span>
              )}

              {/* Blog-specific badges */}
              {type === 'blogs' && data.author && (
                <span className={`px-3 py-1 rounded-full text-sm font-medium ${styles.badge}`}>
                  By {data.author}
                </span>
              )}

              {type === 'blogs' && data.date && (
                <span className={`px-3 py-1 rounded-full text-sm font-medium ${styles.badge}`}>
                  {data.date}
                </span>
              )}
            </motion.div>
          </div>
        </div>

        {/* Decorative background elements */}
        <div className="absolute inset-0 -z-10 overflow-hidden">
          <div className={`absolute -top-40 -right-40 w-80 h-80 rounded-full opacity-20 ${
            theme === 'dark' ? 'bg-white' : 'bg-gray-900'
          }`} />
          <div className={`absolute -bottom-40 -left-40 w-80 h-80 rounded-full opacity-10 ${
            theme === 'dark' ? 'bg-white' : 'bg-gray-900'
          }`} />
        </div>
      </motion.div>

      {/* Content Section */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.6, duration: 0.5 }}
        className="container mx-auto px-4 pb-12"
      >
        <div className="max-w-6xl mx-auto">
          {children}
        </div>
      </motion.div>
    </div>
  );
}
