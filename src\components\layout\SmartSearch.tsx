"use client";

import { useState, useEffect, useRef } from "react";
import { useRouter } from "next/navigation";
import { motion, AnimatePresence } from "framer-motion";
import { Search, X, FileText, Calculator, Hammer, User, Loader2 } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { useDebounce } from "@/hooks/useDebounce";
import { useAuth } from "@/hooks/useAuth";

interface SearchResult {
  id: string;
  type: "blog" | "calculator" | "tool" | "user";
  title: string;
  description: string;
  url: string;
  category?: string;
}

interface SmartSearchProps {
  className?: string;
}

export default function SmartSearch({ className = "" }: SmartSearchProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [query, setQuery] = useState("");
  const [results, setResults] = useState<SearchResult[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(-1);
  
  const router = useRouter();
  const searchRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const { isAdmin } = useAuth();
  
  const debouncedQuery = useDebounce(query, 300);

  // Close search when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        setIsOpen(false);
        setQuery("");
        setResults([]);
        setSelectedIndex(-1);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  // Handle keyboard navigation
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (!isOpen) return;

      switch (event.key) {
        case "ArrowDown":
          event.preventDefault();
          setSelectedIndex(prev => 
            prev < results.length - 1 ? prev + 1 : prev
          );
          break;
        case "ArrowUp":
          event.preventDefault();
          setSelectedIndex(prev => prev > 0 ? prev - 1 : -1);
          break;
        case "Enter":
          event.preventDefault();
          if (selectedIndex >= 0 && results[selectedIndex]) {
            handleResultClick(results[selectedIndex]);
          } else if (query.trim()) {
            handleSearchSubmit();
          }
          break;
        case "Escape":
          setIsOpen(false);
          setQuery("");
          setResults([]);
          setSelectedIndex(-1);
          break;
      }
    };

    document.addEventListener("keydown", handleKeyDown);
    return () => document.removeEventListener("keydown", handleKeyDown);
  }, [isOpen, results, selectedIndex, query]);

  // Perform search when debounced query changes
  useEffect(() => {
    const performSearch = async () => {
      if (!debouncedQuery.trim()) {
        setResults([]);
        setIsLoading(false);
        return;
      }

      setIsLoading(true);
      try {
        const endpoint = isAdmin ? "/api/admin/search" : "/api/search";
        const response = await fetch(
          `${endpoint}?q=${encodeURIComponent(debouncedQuery)}&limit=6`
        );

        if (response.ok) {
          const data = await response.json();
          setResults(data.results || []);
        } else {
          setResults([]);
        }
      } catch (error) {
        console.error("Search error:", error);
        setResults([]);
      } finally {
        setIsLoading(false);
      }
    };

    performSearch();
  }, [debouncedQuery, isAdmin]);

  const handleSearchClick = () => {
    setIsOpen(true);
    setTimeout(() => inputRef.current?.focus(), 100);
  };

  const handleResultClick = (result: SearchResult) => {
    router.push(result.url);
    setIsOpen(false);
    setQuery("");
    setResults([]);
    setSelectedIndex(-1);
  };

  const handleSearchSubmit = () => {
    if (query.trim()) {
      const searchUrl = isAdmin 
        ? `/admin/search?q=${encodeURIComponent(query)}`
        : `/search?q=${encodeURIComponent(query)}`;
      router.push(searchUrl);
      setIsOpen(false);
      setQuery("");
      setResults([]);
      setSelectedIndex(-1);
    }
  };

  const getResultIcon = (type: string) => {
    switch (type) {
      case "blog":
        return <FileText className="h-4 w-4 text-blue-500" />;
      case "calculator":
        return <Calculator className="h-4 w-4 text-green-500" />;
      case "tool":
        return <Hammer className="h-4 w-4 text-purple-500" />;
      case "user":
        return <User className="h-4 w-4 text-orange-500" />;
      default:
        return <Search className="h-4 w-4 text-gray-500" />;
    }
  };

  const highlightMatch = (text: string, query: string) => {
    if (!query.trim()) return text;
    
    const regex = new RegExp(`(${query.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');
    const parts = text.split(regex);
    
    return parts.map((part, index) => 
      regex.test(part) ? (
        <mark key={index} className="bg-yellow-200 dark:bg-yellow-800 px-0.5 rounded">
          {part}
        </mark>
      ) : part
    );
  };

  return (
    <div ref={searchRef} className={`relative ${className}`}>
      {/* Search Trigger Button */}
      <AnimatePresence>
        {!isOpen && (
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.8 }}
            transition={{ duration: 0.2 }}
          >
            <Button
              variant="ghost"
              size="icon"
              onClick={handleSearchClick}
              className="relative"
              aria-label="Search"
            >
              <Search className="h-5 w-5" />
            </Button>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Expanded Search Input */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, width: 0 }}
            animate={{ opacity: 1, width: "320px" }}
            exit={{ opacity: 0, width: 0 }}
            transition={{ duration: 0.3, ease: "easeInOut" }}
            className="absolute right-0 top-0 z-50"
          >
            <div className="relative">
              <Input
                ref={inputRef}
                type="text"
                placeholder={isAdmin ? "Search everything..." : "Search blogs & tools..."}
                value={query}
                onChange={(e) => setQuery(e.target.value)}
                onKeyDown={(e) => e.key === "Enter" && handleSearchSubmit()}
                className="pl-10 pr-10 w-full"
              />
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Button
                variant="ghost"
                size="sm"
                onClick={() => {
                  setIsOpen(false);
                  setQuery("");
                  setResults([]);
                  setSelectedIndex(-1);
                }}
                className="absolute right-1 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>

            {/* Search Results Dropdown */}
            <AnimatePresence>
              {(results.length > 0 || isLoading) && (
                <motion.div
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -10 }}
                  transition={{ duration: 0.2 }}
                  className="absolute top-full left-0 right-0 mt-2 z-50"
                >
                  <Card className="shadow-lg border">
                    <CardContent className="p-0">
                      {isLoading ? (
                        <div className="flex items-center justify-center py-4">
                          <Loader2 className="h-4 w-4 animate-spin mr-2" />
                          <span className="text-sm text-muted-foreground">Searching...</span>
                        </div>
                      ) : (
                        <div className="max-h-80 overflow-y-auto">
                          {results.map((result, index) => (
                            <motion.button
                              key={result.id}
                              initial={{ opacity: 0, x: -10 }}
                              animate={{ opacity: 1, x: 0 }}
                              transition={{ delay: index * 0.05 }}
                              onClick={() => handleResultClick(result)}
                              className={`w-full text-left px-4 py-3 hover:bg-muted/50 transition-colors border-b last:border-b-0 ${
                                selectedIndex === index ? "bg-muted/50" : ""
                              }`}
                            >
                              <div className="flex items-start space-x-3">
                                {getResultIcon(result.type)}
                                <div className="flex-1 min-w-0">
                                  <div className="font-medium text-sm truncate">
                                    {highlightMatch(result.title, query)}
                                  </div>
                                  <div className="text-xs text-muted-foreground truncate">
                                    {highlightMatch(result.description, query)}
                                  </div>
                                  {result.category && (
                                    <div className="text-xs text-primary mt-1">
                                      {result.category}
                                    </div>
                                  )}
                                </div>
                              </div>
                            </motion.button>
                          ))}
                          
                          {query.trim() && (
                            <motion.button
                              initial={{ opacity: 0 }}
                              animate={{ opacity: 1 }}
                              onClick={handleSearchSubmit}
                              className="w-full text-left px-4 py-3 hover:bg-muted/50 transition-colors border-t text-sm text-primary"
                            >
                              <div className="flex items-center space-x-2">
                                <Search className="h-4 w-4" />
                                <span>Search for "{query}"</span>
                              </div>
                            </motion.button>
                          )}
                        </div>
                      )}
                    </CardContent>
                  </Card>
                </motion.div>
              )}
            </AnimatePresence>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
