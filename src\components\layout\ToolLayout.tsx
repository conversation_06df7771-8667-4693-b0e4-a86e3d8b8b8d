// components/layout/ToolLayout.tsx
import Navbar from "@/components/layout/Header";
import Footer from "@/components/layout/Footer";

export default function ToolLayout({ children }: { children: React.ReactNode }) {
  return (
    <div className="min-h-screen flex flex-col bg-gray-50">
      <Navbar />
      <main className="flex-grow">
        {children}
      </main>
      <Footer />
    </div>
  );
}