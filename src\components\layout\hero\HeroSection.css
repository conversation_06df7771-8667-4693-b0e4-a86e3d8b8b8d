/* components/HeroSection.css */

.hero-section {
  @apply relative h-[90vh] w-full bg-cover bg-center flex items-center justify-start px-16 pt-0 mt-0 text-white;
}

.hero-content {
  @apply max-w-xl space-y-5 z-10 ml-4 mt-0;
}

.hero-title {
  @apply text-5xl font-bold mb-2 drop-shadow-lg;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.hero-subtitle {
  @apply text-2xl text-orange-400 font-bold mb-4 drop-shadow-md;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.hero-description {
  @apply text-lg mb-6 max-w-md leading-relaxed text-gray-100;
}

.hero-button {
  @apply bg-white text-black px-6 py-3 font-medium rounded-md hover:bg-gray-200 transition shadow-lg transform hover:scale-105 hover:-translate-y-1;
  transition: all 0.3s ease;
}

.hero-thumbnails {
  @apply absolute bottom-10 left-1/2 transform -translate-x-1/2 items-center flex space-x-6 z-10;
}

.thumb-button {
  @apply overflow-hidden rounded-xl transition-all duration-300 hover:scale-110 border-2 border-transparent transform hover:rotate-1 shadow-lg;
  transform-style: preserve-3d;
  perspective: 1000px;
}

.thumb-active {
  @apply border-white shadow-xl;
  transform: translateZ(10px);
}

.thumb-image {
  @apply object-cover h-20 w-32;
}
