// components/HeroSection.tsx
import React, { useEffect, useState } from "react";
import Image from "next/image";
import { motion } from "framer-motion";
import Link from "next/link";
import { slides } from "./HeroData";
import { useHeroSlider } from "./useHeroSlider";
import "./HeroSection.css";

export default function HeroSection() {
  const { activeIndex, setActiveIndex } = useHeroSlider(slides);
  const activeSlide = slides[activeIndex];
  const [imageLoaded, setImageLoaded] = useState(false);

  // Function to get the appropriate button text based on the slide content
  const getButtonText = () => {
    if (activeSlide.link.includes('/tools')) {
      return "All Tools";
    } else if (activeSlide.link.includes('/calculators/all')) {
      return "All Calculators";
    } else if (activeSlide.link.includes('/blog')) {
      return "All Blogs";
    }
    return "Learn More";
  };

  // Preload the background image
  useEffect(() => {
    const img = new window.Image();
    img.src = activeSlide.image;
    img.onload = () => {
      setImageLoaded(true);
    };

    // Reset image loaded state when slide changes
    return () => {
      setImageLoaded(false);
    };
  }, [activeSlide.image]);

  // Animation variants for content elements
  const contentVariants = {
    hidden: { opacity: 0, x: -30 },
    visible: (delay: number) => ({
      opacity: 1,
      x: 0,
      transition: {
        duration: 0.6,
        delay: delay,
        ease: "easeOut"
      }
    })
  };

  return (
    <section
      className="hero-section"
      style={{
        backgroundImage: `linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.7)), url(${activeSlide.image})`, // Add your background image URL here
        opacity: imageLoaded ? 1 : 0,
        transition: "opacity 1.2s ease-in-out"
      }}
    >
      <div className="w-full">
        <div className="hero-content mx-auto max-w-3xl">
          <motion.h2
            key={`title-${activeSlide.id}`}
            className="hero-title"
            custom={0.3}
            initial="hidden"
            animate={imageLoaded ? "visible" : "hidden"}
            variants={contentVariants}
          >
            {activeSlide.title}
          </motion.h2>

          <motion.h4
            key={`subtitle-${activeSlide.id}`}
            className="hero-subtitle"
            custom={0.5}
            initial="hidden"
            animate={imageLoaded ? "visible" : "hidden"}
            variants={contentVariants}
          >
            {activeSlide.subtitle}
          </motion.h4>

          <motion.p
            key={`desc-${activeSlide.id}`}
            className="hero-description"
            custom={0.7}
            initial="hidden"
            animate={imageLoaded ? "visible" : "hidden"}
            variants={contentVariants}
          >
            {activeSlide.description}
          </motion.p>

          <motion.div
            key={`button-${activeSlide.id}`}
            custom={0.9}
            initial="hidden"
            animate={imageLoaded ? "visible" : "hidden"}
            variants={contentVariants}
          >
            <Link href={activeSlide.link} className="hero-button">
              {getButtonText()} →
            </Link>
          </motion.div>
        </div>
      </div>

      <div className="hero-thumbnails">
        {slides.map((slide, i) => (
          <motion.button
            key={slide.id}
            onClick={() => setActiveIndex(i)}
            className={`thumb-button ${
              activeIndex === i ? "thumb-active" : ""
            }`}
            initial={{ opacity: 0, y: 20, rotateY: 15 }}
            animate={{
              opacity: imageLoaded ? 1 : 0,
              y: imageLoaded ? 0 : 20,
              rotateY: imageLoaded ? 0 : 15,
              scale: activeIndex === i ? 1.1 : 1
            }}
            whileHover={{
              scale: 1.15,
              rotateY: -5,
              transition: { duration: 0.3 }
            }}
            transition={{
              duration: 0.5,
              delay: imageLoaded ? 1 + (i * 0.1) : 0
            }}
            style={{
              transformStyle: 'preserve-3d',
              perspective: '1000px'
            }}
          >
            <div className="relative overflow-hidden">
              <Image
                src={slide.thumbnail}
                alt={slide.title}
                width={128}
                height={80}
                className="thumb-image"
                style={{
                  filter: activeIndex === i ? 'brightness(1.2)' : 'brightness(0.9)',
                  transition: 'all 0.3s ease'
                }}
              />
              {activeIndex === i && (
                <motion.div
                  className="absolute inset-0 border-2 border-white rounded-xl"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ duration: 0.3 }}
                />
              )}
            </div>
          </motion.button>
        ))}
      </div>
    </section>
  );
}
