import { useEffect, useState } from "react";

export function useHeroSlider(slides: any) {
  const [activeIndex, setActiveIndex] = useState(0);

  useEffect(() => {
    let interval: NodeJS.Timeout;
    const timeout = setTimeout(() => {
      interval = setInterval(() => {
        setActiveIndex((prevIndex) => (prevIndex + 1) % slides.length);
      }, 10000);
    }, 1000);

    // Proper cleanup for both timeout and interval
    return () => {
      clearTimeout(timeout);
      if (interval) clearInterval(interval);
    };
  }, [slides.length]);

  return { activeIndex, setActiveIndex };
}
