"use client";

import { SessionProvider as NextAuthSessionProvider } from "next-auth/react";
import { useSession } from "next-auth/react";
import { useEffect } from "react";
import { useAppDispatch } from "@/redux/hooks";
import { setUser, clearUser } from "@/redux/slices/authSlice";

interface SessionProviderProps {
  children: React.ReactNode;
}

// Component to sync NextAuth session with Redux
function SessionSync() {
  const { data: session, status } = useSession();
  const dispatch = useAppDispatch();

  useEffect(() => {
    if (status === "loading") return;

    if (session?.user) {
      // Sync session user to Redux
      dispatch(setUser({
        id: session.user.id,
        name: session.user.name,
        email: session.user.email,
        role: session.user.role,
      }));
    } else {
      // Clear user from Redux when no session
      dispatch(clearUser());
    }
  }, [session, status, dispatch]);

  return null;
}

export function SessionProvider({ children }: SessionProviderProps) {
  return (
    <NextAuthSessionProvider>
      <SessionSync />
      {children}
    </NextAuthSessionProvider>
  );
}
