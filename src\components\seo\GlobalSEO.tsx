"use client";

import { useEffect, useState } from "react";
import Head from "next/head";

interface SEOSettings {
  siteTitle: string;
  siteDescription: string;
  metaKeywords: string;
  defaultShareImage: string;
  robotsAllowed: boolean;
  canonicalUrl: string;
  ogTitle: string;
  ogDescription: string;
  twitterCardType: string;
  twitterSite: string;
  favicon: string;
  sitemapEnabled: boolean;
  googleAnalyticsId: string;
  googleSearchConsoleId: string;
}

interface GlobalSEOProps {
  pageTitle?: string;
  pageDescription?: string;
  pageImage?: string;
  pageUrl?: string;
  pageType?: string;
  noIndex?: boolean;
}

export default function GlobalSEO({
  pageTitle,
  pageDescription,
  pageImage,
  pageUrl,
  pageType = "website",
  noIndex = false
}: GlobalSEOProps) {
  const [seoSettings, setSeoSettings] = useState<SEOSettings | null>(null);

  useEffect(() => {
    const fetchSEOSettings = async () => {
      try {
        const response = await fetch("/api/seo-settings");
        if (response.ok) {
          const settings = await response.json();
          setSeoSettings(settings);
        }
      } catch (error) {
        console.error("Failed to fetch SEO settings:", error);
      }
    };

    fetchSEOSettings();
  }, []);

  if (!seoSettings) {
    return null; // Or return default meta tags
  }

  const title = pageTitle 
    ? `${pageTitle} | ${seoSettings.siteTitle}`
    : seoSettings.siteTitle;
  
  const description = pageDescription || seoSettings.siteDescription;
  const image = pageImage || seoSettings.defaultShareImage;
  const url = pageUrl || seoSettings.canonicalUrl;
  const ogTitle = seoSettings.ogTitle || title;
  const ogDescription = seoSettings.ogDescription || description;

  return (
    <Head>
      {/* Basic Meta Tags */}
      <title>{title}</title>
      <meta name="description" content={description} />
      {seoSettings.metaKeywords && (
        <meta name="keywords" content={seoSettings.metaKeywords} />
      )}
      
      {/* Robots */}
      <meta 
        name="robots" 
        content={noIndex ? "noindex,nofollow" : seoSettings.robotsAllowed ? "index,follow" : "noindex,nofollow"} 
      />
      
      {/* Canonical URL */}
      <link rel="canonical" href={url} />
      
      {/* Favicon */}
      {seoSettings.favicon && (
        <>
          <link rel="icon" href={seoSettings.favicon} />
          <link rel="shortcut icon" href={seoSettings.favicon} />
        </>
      )}
      
      {/* Open Graph */}
      <meta property="og:type" content={pageType} />
      <meta property="og:title" content={ogTitle} />
      <meta property="og:description" content={ogDescription} />
      <meta property="og:url" content={url} />
      {image && <meta property="og:image" content={image} />}
      <meta property="og:site_name" content={seoSettings.siteTitle} />
      
      {/* Twitter Cards */}
      <meta name="twitter:card" content={seoSettings.twitterCardType} />
      {seoSettings.twitterSite && (
        <meta name="twitter:site" content={seoSettings.twitterSite} />
      )}
      <meta name="twitter:title" content={ogTitle} />
      <meta name="twitter:description" content={ogDescription} />
      {image && <meta name="twitter:image" content={image} />}
      
      {/* Google Analytics */}
      {seoSettings.googleAnalyticsId && (
        <>
          <script
            async
            src={`https://www.googletagmanager.com/gtag/js?id=${seoSettings.googleAnalyticsId}`}
          />
          <script
            dangerouslySetInnerHTML={{
              __html: `
                window.dataLayer = window.dataLayer || [];
                function gtag(){dataLayer.push(arguments);}
                gtag('js', new Date());
                gtag('config', '${seoSettings.googleAnalyticsId}');
              `,
            }}
          />
        </>
      )}
      
      {/* Google Search Console */}
      {seoSettings.googleSearchConsoleId && (
        <meta name="google-site-verification" content={seoSettings.googleSearchConsoleId} />
      )}
      
      {/* Structured Data */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "WebSite",
            "name": seoSettings.siteTitle,
            "description": seoSettings.siteDescription,
            "url": seoSettings.canonicalUrl,
            "potentialAction": {
              "@type": "SearchAction",
              "target": `${seoSettings.canonicalUrl}/search?q={search_term_string}`,
              "query-input": "required name=search_term_string"
            }
          })
        }}
      />
    </Head>
  );
}
