"use client";

import { useEffect } from 'react';

export function ColorSchemeInitializer() {
  useEffect(() => {
    // Only run on client-side
    if (typeof window === 'undefined') return;

    try {
      // Load saved color scheme from localStorage
      const savedColorScheme = localStorage.getItem('colorScheme');
      
      if (savedColorScheme) {
        const colors = JSON.parse(savedColorScheme);
        
        // Apply saved colors to CSS variables
        Object.entries(colors).forEach(([key, value]) => {
          document.documentElement.style.setProperty(`--${key}`, value as string);
        });
        
        console.log('Color scheme loaded from localStorage');
      } else {
        console.log('No saved color scheme found');
      }
    } catch (error) {
      console.error('Error loading color scheme:', error);
    }
  }, []);

  // This component doesn't render anything
  return null;
}
