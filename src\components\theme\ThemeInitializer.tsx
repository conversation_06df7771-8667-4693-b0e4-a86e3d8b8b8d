"use client";

import { useEffect } from 'react';
import { useAppDispatch } from '@/redux/hooks';
import { setTheme, Theme } from '@/redux/slices/themeSlice';

export function ThemeInitializer() {
  const dispatch = useAppDispatch();

  // Initialize theme from localStorage on mount
  useEffect(() => {
    if (typeof window !== 'undefined') {
      try {
        const savedTheme = localStorage.getItem('theme') as Theme | null;
        const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';

        // Apply theme to document with smooth transition
        const theme = savedTheme || systemTheme;
        document.documentElement.style.transition = 'background-color 0.3s ease, color 0.3s ease';
        document.documentElement.classList.remove('light', 'dark');
        document.documentElement.classList.add(theme);
        document.documentElement.setAttribute('data-theme', theme);

        // Store the theme
        localStorage.setItem('theme', theme);
        dispatch(setTheme(theme));

        // Dispatch custom event for theme change
        window.dispatchEvent(new CustomEvent('themeChange', { detail: { theme } }));
      } catch (error) {
        console.error('Error initializing theme:', error);
      }
    }

    // Listen for system theme changes
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    const handleSystemThemeChange = () => {
      const savedTheme = localStorage.getItem('theme');
      if (!savedTheme) {
        const systemTheme = mediaQuery.matches ? 'dark' : 'light';
        document.documentElement.classList.remove('light', 'dark');
        document.documentElement.classList.add(systemTheme);
        document.documentElement.setAttribute('data-theme', systemTheme);
        localStorage.setItem('theme', systemTheme);
        dispatch(setTheme(systemTheme as Theme));
        window.dispatchEvent(new CustomEvent('themeChange', { detail: { theme: systemTheme } }));
      }
    };

    // Listen for manual theme changes from other tabs
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'theme' && e.newValue) {
        const newTheme = e.newValue as Theme;
        document.documentElement.classList.remove('light', 'dark');
        document.documentElement.classList.add(newTheme);
        document.documentElement.setAttribute('data-theme', newTheme);
        dispatch(setTheme(newTheme));
        window.dispatchEvent(new CustomEvent('themeChange', { detail: { theme: newTheme } }));
      }
    };

    mediaQuery.addEventListener('change', handleSystemThemeChange);
    window.addEventListener('storage', handleStorageChange);

    return () => {
      mediaQuery.removeEventListener('change', handleSystemThemeChange);
      window.removeEventListener('storage', handleStorageChange);
    };
  }, [dispatch]);

  // This component doesn't render anything
  return null;
}

