"use client";

import { useTheme } from "@/hooks/useTheme";
import { Button } from "@/components/ui/button";
import { MoonIcon, SunIcon } from "lucide-react";
import { motion } from "framer-motion";

interface ThemeToggleProps {
  variant?: "icon" | "button" | "switch";
  className?: string;
}

export function ThemeToggle({
  variant = "icon",
  className = ""
}: ThemeToggleProps) {
  const { theme, toggleTheme } = useTheme();
  const isDark = theme === "dark";

  // Icon-only toggle
  if (variant === "icon") {
    return (
      <Button
        variant="outline"
        size="icon"
        onClick={toggleTheme}
        className={`btn-outline focus-ring ${className}`}
        aria-label={isDark ? 'Switch to light mode' : 'Switch to dark mode'}
      >
        <motion.div
          initial={{ scale: 0.5, opacity: 0, rotate: -30 }}
          animate={{ scale: 1, opacity: 1, rotate: 0 }}
          exit={{ scale: 0.5, opacity: 0, rotate: 30 }}
          transition={{ duration: 0.2 }}
          key={theme}
        >
          {isDark ? (
            <SunIcon className="h-5 w-5 text-yellow-400" />
          ) : (
            <MoonIcon className="h-5 w-5 text-indigo-600 dark:text-indigo-400" />
          )}
        </motion.div>
      </Button>
    );
  }

  // Button with text
  if (variant === "button") {
    return (
      <Button
        variant="outline"
        onClick={toggleTheme}
        className={`btn-outline focus-ring ${className}`}
      >
        {isDark ? (
          <>
            <SunIcon className="h-4 w-4 mr-2 text-yellow-400" />
            Light Mode
          </>
        ) : (
          <>
            <MoonIcon className="h-4 w-4 mr-2 text-indigo-600 dark:text-indigo-400" />
            Dark Mode
          </>
        )}
      </Button>
    );
  }

  // Switch-style toggle
  return (
    <button
      onClick={toggleTheme}
      className={`relative inline-flex h-6 w-11 items-center rounded-full focus-ring ${className}`}
      style={{
        backgroundColor: isDark
          ? 'rgb(59, 130, 246)' // Blue for dark mode
          : 'rgb(148, 163, 184)'  // Gray for light mode
      }}
      aria-label={isDark ? 'Switch to light mode' : 'Switch to dark mode'}
    >
      <span className="sr-only">Toggle theme</span>
      <motion.span
        className="inline-block h-5 w-5 rounded-full bg-white shadow-lg"
        animate={{
          x: isDark ? 20 : 2
        }}
        transition={{
          type: "spring",
          stiffness: 500,
          damping: 30
        }}
      />
    </button>
  );
}
