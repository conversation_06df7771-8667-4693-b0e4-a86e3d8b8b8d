"use client"

import Link from "next/link";
import { FiArrowRight } from "react-icons/fi";
import { motion } from "framer-motion";
import { useTheme } from "@/hooks/useTheme";

interface EnhancedToolCardProps {
  name: string;
  description: string;
  icon: string;
  path: string;
  index?: number;
}

export default function EnhancedToolCard({
  name,
  description,
  icon,
  path,
  index = 0,
}: EnhancedToolCardProps) {
  const { theme } = useTheme();
  const isDark = theme === 'dark';

  // Calculate staggered delay based on index
  const delay = 0.05 * (index % 8); // Reset delay every 8 items for better UX

  return (
    <motion.div
      initial={{ opacity: 0, y: 20, scale: 0.95 }}
      animate={{ opacity: 1, y: 0, scale: 1 }}
      transition={{
        duration: 0.4,
        delay,
        ease: [0.25, 0.1, 0.25, 1.0] // Custom easing for smoother animation
      }}
      whileHover={{
        y: -8,
        transition: { duration: 0.2, ease: "easeOut" }
      }}
      className="h-full"
    >
      <Link
        href={path}
        className="group relative block h-full p-6 rounded-xl border transition-all duration-300 overflow-hidden shadow-sm hover:shadow-md"
        style={{
          backgroundColor: isDark ? 'var(--bg-secondary)' : 'white',
          borderColor: isDark ? '#374151' : '#e5e7eb',
          color: 'var(--text-primary)'
        }}
      >
        <div className="flex items-start justify-between mb-4">
          <motion.div
            className="text-4xl"
            whileHover={{ scale: 1.2, rotate: 5 }}
            transition={{ duration: 0.2 }}
          >
            {icon}
          </motion.div>
        </div>

        <h3 className="text-xl font-semibold mb-3 group-hover:text-blue-500 transition-colors"
          style={{ color: 'var(--text-primary)' }}
        >
          {name}
        </h3>

        <p className="mb-4" style={{ color: 'var(--text-secondary)' }}>
          {description}
        </p>

        <div className="flex justify-end items-center mt-auto">
          <motion.div
            whileHover={{ x: 4 }}
            transition={{ duration: 0.2 }}
          >
            <FiArrowRight className="w-5 h-5 text-gray-400 group-hover:text-blue-500 dark:group-hover:text-blue-400 transition-all" />
          </motion.div>
        </div>

        {/* Gradient background effect on hover */}
        <div className="absolute inset-0 -z-10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"
          style={{
            background: isDark
              ? 'linear-gradient(to bottom right, rgba(30, 64, 175, 0.2), rgba(31, 41, 55, 0.8))'
              : 'linear-gradient(to bottom right, rgba(219, 234, 254, 0.8), rgba(255, 255, 255, 0.8))'
          }}
        />

        {/* Subtle border glow effect on hover */}
        <div className="absolute inset-0 -z-20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-xl rounded-xl"
          style={{
            backgroundColor: isDark ? 'rgba(59, 130, 246, 0.1)' : 'rgba(59, 130, 246, 0.05)'
          }}
        />
      </Link>
    </motion.div>
  );
}
