'use client';

import { useState, useRef, useCallback, useEffect, Drag<PERSON>vent, ChangeEvent } from 'react';
import { FiUploadCloud, FiX, FiCheck, FiDroplet } from 'react-icons/fi';
import { FaGoogleDrive, FaDropbox } from 'react-icons/fa';

interface FileUploaderProps {
  acceptedFileTypes: string;
  maxFileSizeMB?: number;
  onFileSelect: (file: File) => void;
  multiple?: boolean;
  label?: string;
  disabled?: boolean;
  showCloudOptions?: boolean;
}

export default function FileUploader({
  acceptedFileTypes,
  maxFileSizeMB = 50,
  onFileSelect,
  multiple = false,
  label = 'Click to upload or drag and drop',
  disabled = false,
  showCloudOptions = true,
}: FileUploaderProps) {
  const [dragActive, setDragActive] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [fileInfo, setFileInfo] = useState<{
    name: string;
    size: string;
    type: string;
  } | null>(null);
  const [showGoogleDriveModal, setShowGoogleDriveModal] = useState(false);
  const [showDropboxModal, setShowDropboxModal] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);

  const maxSizeBytes = maxFileSizeMB * 1024 * 1024;

  const formatFileSize = useCallback((bytes: number): string => {
    if (bytes < 1024) return `${bytes} B`;
    if (bytes < 1048576) return `${(bytes / 1024).toFixed(1)} KB`;
    return `${(bytes / 1048576).toFixed(1)} MB`;
  }, []);

  const validateFile = useCallback((file: File): boolean => {
    setError(null);
    setFileInfo(null);

    if (file.size > maxSizeBytes) {
      setError(`File exceeds ${maxFileSizeMB}MB limit (${formatFileSize(file.size)})`);
      return false;
    }

    const acceptedPatterns = acceptedFileTypes
      .split(',')
      .map(type => type.trim())
      .map(type => {
        if (type.startsWith('.')) return `\\${type}$`;
        if (type.includes('/*')) return type.replace('/*', '/.*');
        return type;
      });

    const fileExtension = `.${file.name.split('.').pop()?.toLowerCase()}`;
    const isAccepted = acceptedPatterns.some(pattern => {
      const regex = new RegExp(pattern, 'i');
      return regex.test(file.type) || regex.test(fileExtension);
    });

    if (!isAccepted) {
      setError(`Invalid file type. Allowed: ${acceptedFileTypes}`);
      return false;
    }

    setFileInfo({
      name: file.name,
      size: formatFileSize(file.size),
      type: file.type.split('/')[1]?.toUpperCase() || fileExtension.toUpperCase(),
    });

    return true;
  }, [acceptedFileTypes, maxSizeBytes, formatFileSize, maxFileSizeMB]);

  const handleDrag = (e: DragEvent<HTMLDivElement>) => {
    if (disabled) return;
    e.preventDefault();
    e.stopPropagation();
    setDragActive(e.type === 'dragenter' || e.type === 'dragover');
  };

  const handleDrop = (e: DragEvent<HTMLDivElement>) => {
    if (disabled) return;
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      processFiles(e.dataTransfer.files);

      // Auto-open file dialog when files are dropped
      if (inputRef.current) {
        inputRef.current.click();
      }
    }
  };

  const handleChange = (e: ChangeEvent<HTMLInputElement>) => {
    if (disabled || !e.target.files || e.target.files.length === 0) return;
    processFiles(e.target.files);
  };

  const processFiles = (files: FileList | File) => {
    const fileArray = files instanceof FileList ? Array.from(files) : [files];

    if (!multiple && fileArray.length > 1) {
      setError('Multiple files not allowed');
      return;
    }

    const validFiles = fileArray.filter(validateFile);
    if (validFiles.length > 0) onFileSelect(validFiles[0]);
  };

  const resetFiles = () => {
    if (inputRef.current) inputRef.current.value = '';
    setFileInfo(null);
    setError(null);
  };

  const onButtonClick = () => {
    if (!disabled && inputRef.current) {
      inputRef.current.click();

      // Close the dropdown after selecting an option
      const dropdown = document.getElementById('upload-dropdown');
      if (dropdown) {
        dropdown.classList.add('hidden');
      }
    }
  };

  // Handle clicks outside the dropdown to close it
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const dropdown = document.getElementById('upload-dropdown');
      const dropdownButton = document.getElementById('dropdown-button');

      if (dropdown && !dropdown.classList.contains('hidden') &&
          dropdownButton && !dropdownButton.contains(event.target as Node) &&
          !dropdown.contains(event.target as Node)) {
        dropdown.classList.add('hidden');
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Google Drive integration
  const handleGoogleDriveClick = () => {
    // Close the dropdown after selecting an option
    const dropdown = document.getElementById('upload-dropdown');
    if (dropdown) {
      dropdown.classList.add('hidden');
    }

    // In a real implementation, this would open the Google Drive picker
    setShowGoogleDriveModal(true);
    // For now, we'll just show a mock modal and simulate file selection
    setTimeout(() => {
      setShowGoogleDriveModal(false);
      // Create a mock file from Google Drive
      const mockFile = new File(["Google Drive file content"], "google-drive-file.pdf", { type: "application/pdf" });
      if (validateFile(mockFile)) {
        onFileSelect(mockFile);
      }
    }, 1500);
  };

  // Dropbox integration
  const handleDropboxClick = () => {
    // Close the dropdown after selecting an option
    const dropdown = document.getElementById('upload-dropdown');
    if (dropdown) {
      dropdown.classList.add('hidden');
    }

    // In a real implementation, this would open the Dropbox chooser
    setShowDropboxModal(true);
    // For now, we'll just show a mock modal and simulate file selection
    setTimeout(() => {
      setShowDropboxModal(false);
      // Create a mock file from Dropbox
      const mockFile = new File(["Dropbox file content"], "dropbox-file.pdf", { type: "application/pdf" });
      if (validateFile(mockFile)) {
        onFileSelect(mockFile);
      }
    }, 1500);
  };

  return (
    <div className="w-full space-y-4">
      {/* Main upload area */}
      <div
        className={`relative flex flex-col items-center justify-center w-full min-h-[180px] border-2 border-dashed rounded-lg transition-all ${
          dragActive
            ? 'border-red-500 bg-red-50/50'
            : 'border-gray-300 hover:border-red-400'
        } ${
          disabled
            ? 'cursor-not-allowed bg-gray-100 opacity-70'
            : 'cursor-pointer'
        }`}
        onDragEnter={handleDrag}
        onDragLeave={handleDrag}
        onDragOver={handleDrag}
        onDrop={handleDrop}
        aria-disabled={disabled}
      >
        <input
          ref={inputRef}
          type="file"
          className="hidden"
          accept={acceptedFileTypes}
          onChange={handleChange}
          multiple={multiple}
          disabled={disabled}
        />

        <div className="flex flex-col items-center justify-center p-6 text-center">
          {fileInfo ? (
            <div className="text-center">
              <p className="font-medium text-gray-900 truncate max-w-xs">
                {fileInfo.name}
              </p>
              <p className="text-sm text-gray-500">
                {fileInfo.type} • {fileInfo.size}
              </p>
            </div>
          ) : (
            <>
              <div className="bg-red-500 text-white p-4 rounded-full mb-4">
                <FiUploadCloud className="w-8 h-8" />
              </div>
              <p className="mb-4 text-lg font-semibold text-gray-800">
                Drop file here or
              </p>

              {/* Dropdown button for upload options */}
              <div className="relative inline-block">
                <button
                  id="dropdown-button"
                  type="button"
                  onClick={() => {
                    const dropdown = document.getElementById('upload-dropdown');
                    if (dropdown) {
                      dropdown.classList.toggle('hidden');
                    }
                  }}
                  className="flex items-center justify-center gap-2 px-4 py-2 bg-red-500 hover:bg-red-600 text-white rounded-md transition-colors"
                >
                  <span>Upload File</span>
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7"></path>
                  </svg>
                </button>

                <div id="upload-dropdown" className="hidden absolute z-10 mt-2 w-48 bg-white rounded-md shadow-lg py-1">
                  <button
                    onClick={onButtonClick}
                    className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                  >
                    <span className="mr-2">📁</span> Choose File
                  </button>

                  <button
                    onClick={handleGoogleDriveClick}
                    disabled={disabled || showGoogleDriveModal}
                    className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                  >
                    <FaGoogleDrive className="w-4 h-4 mr-2 text-blue-600" /> Google Drive
                  </button>

                  <button
                    onClick={handleDropboxClick}
                    disabled={disabled || showDropboxModal}
                    className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                  >
                    <FaDropbox className="w-4 h-4 mr-2 text-blue-600" /> Dropbox
                  </button>
                </div>
              </div>

              <p className="mt-4 text-xs text-gray-500">
                {acceptedFileTypes.toUpperCase()} (Max {maxFileSizeMB}MB)
              </p>
            </>
          )}
        </div>

        {fileInfo && (
          <button
            type="button"
            className="absolute top-2 right-2 p-1 rounded-full bg-gray-100 hover:bg-gray-200 text-gray-500"
            onClick={(e) => {
              e.stopPropagation();
              resetFiles();
            }}
            aria-label="Remove file"
          >
            <FiX className="w-4 h-4" />
          </button>
        )}
      </div>

      {/* Loading indicators for cloud services */}
      {(showGoogleDriveModal || showDropboxModal) && (
        <div className="text-center py-2">
          <div className="inline-block animate-spin rounded-full h-5 w-5 border-b-2 border-red-500"></div>
          <p className="text-sm text-gray-600 mt-2">
            {showGoogleDriveModal ? 'Connecting to Google Drive...' : 'Connecting to Dropbox...'}
          </p>
        </div>
      )}

      {error && (
        <div className="text-sm text-red-500 flex items-center">
          <FiX className="w-4 h-4 mr-1" />
          {error}
        </div>
      )}

      {fileInfo && !error && (
        <div className="text-sm text-green-500 flex items-center">
          <FiCheck className="w-4 h-4 mr-1" />
          File ready for upload
        </div>
      )}
    </div>
  );
}
