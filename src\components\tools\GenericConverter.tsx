"use client";

import { useState, useEffect } from "react";
import FileUploader from "./FileUploader";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { useToolCompletion } from "@/components/analytics/ToolTracker";
import { trackPageView } from "@/lib/analytics";

export interface GenericConverterProps {
  title: string;
  description: string;
  steps: string[];
  acceptedFileTypes: string;
  maxFileSizeMB?: number;
  convertButtonText: string;
  downloadButtonText: string;
  aboutTitle: string;
  aboutDescription: string;
  noteTitle?: string;
  noteDescription?: string;
  fileNameHandler?: (fileName: string) => string;
  multiple?: boolean;
  conversionType:
    | "file-to-file"
    | "file-to-files"
    | "files-to-file"
    | "url-or-html";
}

export default function GenericConverter({
  title,
  description,
  steps,
  acceptedFileTypes,
  maxFileSizeMB = 50,
  convertButtonText,
  downloadButtonText,
  aboutTitle,
  aboutDescription,
  noteTitle = "Please Note:",
  noteDescription,
  fileNameHandler,
  multiple = false,
  conversionType,
}: GenericConverterProps) {
  const [file, setFile] = useState<File | null>(null);
  const [files, setFiles] = useState<File[]>([]);
  const [url, setUrl] = useState<string>("");
  const [htmlContent, setHtmlContent] = useState<string>("");
  const [activeTab, setActiveTab] = useState<"url" | "html">("url");
  const [isConverting, setIsConverting] = useState(false);
  const [convertedFileUrl, setConvertedFileUrl] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);

  // Extract tool ID from title (convert to kebab-case)
  const toolId = title.toLowerCase().replace(/\s+/g, '-').replace(/[^a-z0-9-]/g, '');

  // Use the tool completion tracker
  const { trackCompletion, trackFailure } = useToolCompletion(toolId, title);

  // Track page view when component mounts
  useEffect(() => {
    const path = window.location.pathname;
    console.log(`Tracking page view for tool: ${title} at path: ${path}`);
    trackPageView(path);
  }, [title]);
  const [conversionProgress, setConversionProgress] = useState(0);

  const handleFileSelect = (selectedFile: File) => {
    if (conversionType === "files-to-file" || multiple) {
      setFiles((prevFiles) => [...prevFiles, selectedFile]);
    } else {
      setFile(selectedFile);
    }
    setConvertedFileUrl(null);
    setError(null);
    setConversionProgress(0);
  };

  const removeFile = (index: number) => {
    if (conversionType === "files-to-file" || multiple) {
      setFiles((prevFiles) => prevFiles.filter((_, i) => i !== index));
      if (files.length === 1) {
        setConvertedFileUrl(null);
      }
    } else {
      setFile(null);
      setConvertedFileUrl(null);
    }
  };

  const handleConvert = async () => {
    // Validate input based on conversion type
    if (
      (conversionType === "file-to-file" ||
        conversionType === "file-to-files") &&
      !file
    ) {
      return;
    }

    if (conversionType === "files-to-file" && files.length === 0) {
      return;
    }

    if (
      conversionType === "url-or-html" &&
      ((activeTab === "url" && !url) || (activeTab === "html" && !htmlContent))
    ) {
      setError("Please provide a URL or HTML content to convert");
      return;
    }

    // Check if user is authenticated
    try {
      const res = await fetch("/api/auth/me");
      if (!res.ok) {
        // User is not authenticated, redirect to login with converting parameter
        const currentUrl = new URL(window.location.href);
        currentUrl.searchParams.set("converting", "true");
        window.location.href = `/login?callbackUrl=${encodeURIComponent(
          currentUrl.pathname,
        )}&converting=true`;
        return;
      }
    } catch (error) {
      console.error("Failed to check authentication status:", error);
      setError("Failed to check authentication status. Please try again.");
      return;
    }

    setIsConverting(true);
    setError(null);
    setConversionProgress(0);

    try {
      // Track that the tool usage has started
      console.log(`Starting conversion for tool: ${title}`);

      // Simulate conversion process with progress
      const totalSteps = 10;
      for (let step = 1; step <= totalSteps; step++) {
        await new Promise((resolve) => setTimeout(resolve, 300));
        setConversionProgress(Math.floor((step / totalSteps) * 100));
      }

      // In a real implementation, we would send the file to the server for conversion
      // For now, we'll simulate a successful conversion
      const outputUrl = URL.createObjectURL(
        new Blob(["Simulated converted content"], {
          type:
            conversionType === "file-to-files" ||
            conversionType === "files-to-file"
              ? "application/zip"
              : "application/pdf",
        }),
      );

      setConvertedFileUrl(outputUrl);

      // Track successful completion
      trackCompletion({
        conversionType,
        fileSize: file?.size || files.reduce((sum, f) => sum + f.size, 0) || 0,
        inputType: file?.type || (files[0]?.type || "unknown"),
        outputType: conversionType === "file-to-files" || conversionType === "files-to-file" ? "application/zip" : "application/pdf",
        success: true
      });

      console.log(`Conversion completed successfully for tool: ${title}`);
    } catch (err) {
      setError("An error occurred during conversion. Please try again.");
      console.error(err);

      // Track failure
      trackFailure(err);

      console.log(`Conversion failed for tool: ${title}`, err);
    } finally {
      setIsConverting(false);
      setConversionProgress(100);
    }
  };

  const handleDownload = () => {
    if (convertedFileUrl) {
      const link = document.createElement("a");
      link.href = convertedFileUrl;

      let fileName = "converted-document.pdf";

      if (conversionType === "file-to-file" && file && fileNameHandler) {
        fileName = fileNameHandler(file.name);
      } else if (
        conversionType === "url-or-html" &&
        activeTab === "url" &&
        url
      ) {
        try {
          fileName = `${new URL(url).hostname}.pdf`;
        } catch {
          fileName = "converted-document.pdf";
        }
      } else if (conversionType === "files-to-file") {
        fileName = "merged-document.pdf";
      } else if (conversionType === "file-to-files") {
        fileName = file
          ? `${file.name.split(".")[0]}_converted.zip`
          : "converted-files.zip";
      }

      link.download = fileName;

      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // Track the download as a user activity
      try {
        const { trackUserActivity } = require('@/lib/analytics');
        trackUserActivity('download', 'file', fileName, {
          toolName: title,
          conversionType,
          fileType: fileName.split('.').pop() || 'unknown'
        });
        console.log(`Tracked download for ${fileName} from tool: ${title}`);
      } catch (error) {
        console.error('Failed to track download:', error);
      }
    }
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes < 1024) return bytes + " bytes";
    else if (bytes < 1048576) return (bytes / 1024).toFixed(2) + " KB";
    else return (bytes / 1048576).toFixed(2) + " MB";
  };

  return (
    <div className="space-y-6 bg-white p-6 rounded-lg shadow-sm">
      <div className="bg-blue-50 p-4 rounded-lg border border-blue-100">
        <h2 className="text-lg font-semibold mb-2 text-blue-800">{title}</h2>
        <ol className="list-decimal list-inside space-y-2 text-gray-700">
          {steps.map((step, index) => (
            <li key={index}>{step}</li>
          ))}
        </ol>
      </div>

      <div className="space-y-4">
        {conversionType === "url-or-html" ? (
          <div>
            <div className="border-b border-gray-200">
              <nav className="-mb-px flex space-x-8">
                <button
                  onClick={() => setActiveTab("url")}
                  className={cn(
                    "py-2 px-1 border-b-2 font-medium text-sm",
                    activeTab === "url"
                      ? "border-blue-500 text-blue-600"
                      : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300",
                  )}
                >
                  URL
                </button>
                <button
                  onClick={() => setActiveTab("html")}
                  className={cn(
                    "py-2 px-1 border-b-2 font-medium text-sm",
                    activeTab === "html"
                      ? "border-blue-500 text-blue-600"
                      : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300",
                  )}
                >
                  HTML
                </button>
              </nav>
            </div>

            {activeTab === "url" ? (
              <div>
                <label
                  htmlFor="url"
                  className="block text-sm font-medium text-gray-700 mb-1"
                >
                  Website URL
                </label>
                <input
                  type="url"
                  id="url"
                  value={url}
                  onChange={(e) => setUrl(e.target.value)}
                  placeholder="https://example.com"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
            ) : (
              <div>
                <label
                  htmlFor="html"
                  className="block text-sm font-medium text-gray-700 mb-1"
                >
                  HTML Content
                </label>
                <textarea
                  id="html"
                  value={htmlContent}
                  onChange={(e) => setHtmlContent(e.target.value)}
                  placeholder="<html><body><h1>Hello World</h1></body></html>"
                  rows={8}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 font-mono text-sm"
                />
              </div>
            )}
          </div>
        ) : (
          <FileUploader
            acceptedFileTypes={acceptedFileTypes}
            maxFileSizeMB={maxFileSizeMB}
            onFileSelect={handleFileSelect}
            multiple={conversionType === "files-to-file" || multiple}
          />
        )}

        {file &&
          conversionType !== "files-to-file" &&
          conversionType !== "url-or-html" && (
            <div className="flex items-center space-x-2 p-3 bg-gray-50 rounded-lg border border-gray-200">
              <svg
                className="w-6 h-6 text-gray-500"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                ></path>
              </svg>
              <span className="flex-1 truncate">{file.name}</span>
              <span className="text-sm text-gray-500">
                {formatFileSize(file.size)}
              </span>
              <button
                onClick={() => setFile(null)}
                className="text-red-500 hover:text-red-700"
                aria-label="Remove file"
              >
                <svg
                  className="w-5 h-5"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M6 18L18 6M6 6l12 12"
                  ></path>
                </svg>
              </button>
            </div>
          )}

        {files.length > 0 &&
          (conversionType === "files-to-file" || multiple) && (
            <div className="space-y-2">
              <h3 className="text-sm font-medium text-gray-700">
                Selected Files ({files.length})
              </h3>
              <div className="max-h-60 overflow-y-auto space-y-2 border rounded-md p-2">
                {files.map((file, index) => (
                  <div
                    key={index}
                    className="flex items-center space-x-2 p-2 bg-gray-50 rounded-lg"
                  >
                    <div className="flex-shrink-0 text-gray-500">
                      {index + 1}.
                    </div>
                    <svg
                      className="w-6 h-6 text-gray-500 flex-shrink-0"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                      ></path>
                    </svg>
                    <span className="flex-1 truncate">{file.name}</span>
                    <span className="text-sm text-gray-500 flex-shrink-0">
                      {formatFileSize(file.size)}
                    </span>
                    <button
                      onClick={() => removeFile(index)}
                      className="text-red-500 hover:text-red-700"
                      aria-label="Remove file"
                    >
                      <svg
                        className="w-5 h-5"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth="2"
                          d="M6 18L18 6M6 6l12 12"
                        ></path>
                      </svg>
                    </button>
                  </div>
                ))}
              </div>
            </div>
          )}

        {((file &&
          (conversionType === "file-to-file" ||
            conversionType === "file-to-files")) ||
          (files.length > 0 && conversionType === "files-to-file") ||
          (conversionType === "url-or-html" &&
            ((activeTab === "url" && url) ||
              (activeTab === "html" && htmlContent)))) && (
          <Button
            onClick={handleConvert}
            disabled={isConverting}
            className={cn(
              "w-full",
              isConverting
                ? "bg-gray-400 cursor-not-allowed"
                : "bg-blue-600 hover:bg-blue-700 text-white",
            )}
          >
            {isConverting ? "Converting..." : convertButtonText}
          </Button>
        )}

        {isConverting && (
          <div className="space-y-2">
            <div className="w-full bg-gray-200 rounded-full h-2.5">
              <div
                className="bg-blue-600 h-2.5 rounded-full"
                style={{ width: `${conversionProgress}%` }}
              ></div>
            </div>
            <p className="text-sm text-gray-600 text-center">
              {conversionProgress}% complete
            </p>
          </div>
        )}

        {error && (
          <div className="p-4 bg-red-50 text-red-700 rounded-lg border border-red-200">
            {error}
          </div>
        )}

        {convertedFileUrl && (
          <div className="p-4 bg-green-50 rounded-lg space-y-4 border border-green-200">
            <div className="flex items-center text-green-700">
              <svg
                className="w-5 h-5 mr-2"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M5 13l4 4L19 7"
                ></path>
              </svg>
              <span>Conversion completed successfully!</span>
            </div>
            <Button
              onClick={handleDownload}
              className="w-full bg-green-600 hover:bg-green-700 text-white flex items-center justify-center"
            >
              <svg
                className="w-5 h-5 mr-2"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"
                ></path>
              </svg>
              {downloadButtonText}
            </Button>
          </div>
        )}
      </div>

      <div className="mt-8 border-t pt-6">
        <h3 className="text-lg font-semibold mb-4 text-gray-800">
          {aboutTitle}
        </h3>
        <p className="text-gray-700 mb-4">{aboutDescription}</p>
        {noteDescription && (
          <div className="bg-yellow-50 p-4 rounded-lg border border-yellow-200">
            <h4 className="font-medium text-yellow-800 mb-2">{noteTitle}</h4>
            <p className="text-yellow-700 text-sm">{noteDescription}</p>
          </div>
        )}
      </div>
    </div>
  );
}
