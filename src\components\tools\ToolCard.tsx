"use client"

import Link from "next/link";
import { FiArrowRight } from "react-icons/fi";
import { motion } from "framer-motion";
import { useState } from "react";
import { useRouter } from "next/navigation";

interface ToolCardProps {
  title: string;
  description: string;
  icon: string;
  href: string;
  inputFormat?: string;
  outputFormat?: string;
  category: string;
  delay?: number;
  id?: string; // Add id for consistent routing
  // componentName:string;
}

export default function ToolCard({
  title,
  description,
  icon,
  href,
  inputFormat,
  outputFormat,
  category,
  id,
  // componentName,
  delay = 0,
}: ToolCardProps) {
  const router = useRouter();
  const [isNavigating, setIsNavigating] = useState(false);

  const categoryColors = {
    pdf: 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300',
    office: 'bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300',
    image: 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300',
    web: 'bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-300'
  };

  // Always use id-based routing for consistency
  const finalHref = id ? `/tools/${id}` : (href || `/tools/${title.toLowerCase().replace(/\s+/g, '-')}`);

  const handleClick = (e: React.MouseEvent) => {
    e.preventDefault();
    setIsNavigating(true);

    try {
      router.push(finalHref);
    } catch (error) {
      console.error('Navigation error:', error);
      // Fallback to window.location
      window.location.href = finalHref;
    }

    // Reset loading state after navigation
    setTimeout(() => setIsNavigating(false), 1000);
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3, delay }}
      whileHover={{ y: -5, scale: 1.02 }}
      className="h-full"
    >
      <div
        onClick={handleClick}
        className={`group relative block h-full p-6 bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 hover:border-blue-500 dark:hover:border-blue-400 transition-all shadow-sm hover:shadow-md overflow-hidden cursor-pointer ${
          isNavigating ? 'opacity-75' : ''
        }`}
      >
        <div className="flex items-start justify-between mb-4">
          <span className="text-3xl" aria-hidden="true">
            {icon}
          </span>
          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${categoryColors[category as keyof typeof categoryColors]}`}>
            {category}
          </span>
        </div>

        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
          {title}
        </h3>
        <p className="text-gray-600 dark:text-gray-300 mb-4">{description}</p>

        <div className="flex justify-between items-center mt-auto">
          {inputFormat && outputFormat && (
            <div className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200">
              {inputFormat} → {outputFormat}
            </div>
          )}
          <motion.div
            whileHover={{ x: 4 }}
            transition={{ duration: 0.2 }}
            className="ml-auto"
          >
            <FiArrowRight className="w-5 h-5 text-gray-400 group-hover:text-blue-500 dark:group-hover:text-blue-400 transition-all" />
          </motion.div>
        </div>

        <div className="absolute inset-0 -z-10 bg-gradient-to-br from-blue-50/50 to-white/50 dark:from-blue-900/20 dark:to-gray-800/50 opacity-0 group-hover:opacity-100 transition-opacity" />
      </div>
    </motion.div>
  );
}