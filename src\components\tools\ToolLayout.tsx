"use client";

import { ReactNode } from "react";
import Header from "@/components/layout/Header";
import Footer from "@/components/layout/Footer";

interface ToolLayoutProps {
  children: ReactNode;
  title: string;
  description: string;
  icon: string;
  inputFormat?: string;
  outputFormat?: string;
}

export default function ToolLayout({
  children,
  title,
  description,
  icon,
  inputFormat,
  outputFormat,
}: ToolLayoutProps) {
  return (
    <div className="min-h-screen flex flex-col bg-background">
      <Header />

      <main className="flex-grow container mx-auto px-4 py-8">
        <div className="flex items-start gap-4 mb-6 p-4 bg-card rounded-lg shadow-sm border">
          <span className="text-4xl" aria-hidden="true">
            {icon}
          </span>
          <div>
            <h1 className="text-2xl font-bold text-foreground">{title}</h1>
            <p className="text-muted-foreground mt-1">{description}</p>
            {inputFormat && outputFormat && (
              <div className="mt-2 inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-primary/10 text-primary">
                {inputFormat} → {outputFormat}
              </div>
            )}
          </div>
        </div>

        <div className="bg-card rounded-lg shadow-md overflow-hidden border">
          {children}
        </div>
      </main>

      <Footer />
    </div>
  );
}