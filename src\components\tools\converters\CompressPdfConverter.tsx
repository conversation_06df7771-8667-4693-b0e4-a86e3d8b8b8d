"use client";

import { useState } from "react";
import FileUploader from "../FileUploader";

export default function CompressPdfConverter() {
  const [file, setFile] = useState<File | null>(null);
  const [compressionLevel, setCompressionLevel] = useState<
    "low" | "medium" | "high"
  >("medium");
  const [isConverting, setIsConverting] = useState(false);
  const [convertedFileUrl, setConvertedFileUrl] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [conversionProgress, setConversionProgress] = useState(0);
  const [originalSize, setOriginalSize] = useState<number | null>(null);
  const [compressedSize, setCompressedSize] = useState<number | null>(null);

  const handleFileSelect = (selectedFile: File) => {
    setFile(selectedFile);
    setOriginalSize(selectedFile.size);
    setConvertedFileUrl(null);
    setError(null);
    setConversionProgress(0);
    setCompressedSize(null);
  };

  const handleCompress = async () => {
    if (!file) return;

    // Check if user is authenticated
    try {
      const res = await fetch("/api/auth/me");
      if (!res.ok) {
        // User is not authenticated, redirect to login with converting parameter
        const currentUrl = new URL(window.location.href);
        currentUrl.searchParams.set("converting", "true");
        window.location.href = `/login?callbackUrl=${encodeURIComponent(currentUrl.pathname)}&converting=true`;
        return;
      }
    } catch (error) {
      console.error("Failed to check authentication status:", error);
      setError("Failed to check authentication status. Please try again.");
      return;
    }

    setIsConverting(true);
    setError(null);
    setConversionProgress(0);

    try {
      // Simulate conversion process with progress
      const totalSteps = 10;
      for (let step = 1; step <= totalSteps; step++) {
        await new Promise((resolve) => setTimeout(resolve, 300));
        setConversionProgress(Math.floor((step / totalSteps) * 100));
      }

      // In a real implementation, we would send the file to the server for compression
      // For now, we'll simulate a successful compression
      // Calculate a simulated compressed size based on compression level
      let compressionRatio;
      switch (compressionLevel) {
        case "low":
          compressionRatio = 0.8; // 20% reduction
          break;
        case "medium":
          compressionRatio = 0.5; // 50% reduction
          break;
        case "high":
          compressionRatio = 0.3; // 70% reduction
          break;
        default:
          compressionRatio = 0.5;
      }

      const simulatedCompressedSize = Math.floor(file.size * compressionRatio);
      setCompressedSize(simulatedCompressedSize);

      setConvertedFileUrl(
        URL.createObjectURL(
          new Blob(["Simulated compressed PDF content"], {
            type: "application/pdf",
          }),
        ),
      );
    } catch (err) {
      setError("An error occurred during compression. Please try again.");
      console.error(err);
    } finally {
      setIsConverting(false);
      setConversionProgress(100);
    }
  };

  const handleDownload = () => {
    if (convertedFileUrl) {
      const link = document.createElement("a");
      link.href = convertedFileUrl;
      link.download = file
        ? file.name.replace(".pdf", "_compressed.pdf")
        : "compressed_document.pdf";
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes < 1024) return bytes + " bytes";
    else if (bytes < 1048576) return (bytes / 1024).toFixed(2) + " KB";
    else return (bytes / 1048576).toFixed(2) + " MB";
  };

  return (
    <div className="space-y-6">
      <div className="bg-blue-50 p-4 rounded-lg">
        <h2 className="text-lg font-semibold mb-2">
          How to Compress PDF Files
        </h2>
        <ol className="list-decimal list-inside space-y-2 text-gray-700">
          <li>Upload your PDF file using the uploader below.</li>
          <li>Select your desired compression level.</li>
          <li>Click the "Compress PDF" button to start the process.</li>
          <li>Download your compressed PDF document when ready.</li>
        </ol>
      </div>

      <div className="space-y-4">
        <FileUploader
          acceptedFileTypes=".pdf,application/pdf"
          maxFileSizeMB={10}
          onFileSelect={handleFileSelect}
        />

        {file && (
          <div className="flex items-center space-x-2 p-3 bg-gray-50 rounded-lg">
            <svg
              className="w-6 h-6 text-gray-500"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
              ></path>
            </svg>
            <span className="flex-1 truncate">{file.name}</span>
            <span className="text-sm text-gray-500">
              {formatFileSize(file.size)}
            </span>
            <button
              onClick={() => setFile(null)}
              className="text-red-500 hover:text-red-700"
              aria-label="Remove file"
            >
              <svg
                className="w-5 h-5"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M6 18L18 6M6 6l12 12"
                ></path>
              </svg>
            </button>
          </div>
        )}

        {file && (
          <div className="p-4 bg-gray-50 rounded-lg">
            <h3 className="text-sm font-medium text-gray-700 mb-3">
              Compression Level
            </h3>
            <div className="space-y-3">
              <div className="flex items-center">
                <input
                  id="compression-low"
                  type="radio"
                  name="compression-level"
                  checked={compressionLevel === "low"}
                  onChange={() => setCompressionLevel("low")}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                />
                <label
                  htmlFor="compression-low"
                  className="ml-2 block text-sm text-gray-700"
                >
                  Low compression (better quality, larger file size)
                </label>
              </div>
              <div className="flex items-center">
                <input
                  id="compression-medium"
                  type="radio"
                  name="compression-level"
                  checked={compressionLevel === "medium"}
                  onChange={() => setCompressionLevel("medium")}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                />
                <label
                  htmlFor="compression-medium"
                  className="ml-2 block text-sm text-gray-700"
                >
                  Medium compression (balanced quality and size)
                </label>
              </div>
              <div className="flex items-center">
                <input
                  id="compression-high"
                  type="radio"
                  name="compression-level"
                  checked={compressionLevel === "high"}
                  onChange={() => setCompressionLevel("high")}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                />
                <label
                  htmlFor="compression-high"
                  className="ml-2 block text-sm text-gray-700"
                >
                  High compression (smaller file size, may affect quality)
                </label>
              </div>
            </div>
          </div>
        )}

        {file && (
          <button
            onClick={handleCompress}
            disabled={isConverting}
            className={`w-full py-2 px-4 rounded-md font-medium ${isConverting ? "bg-gray-400 cursor-not-allowed" : "bg-blue-600 hover:bg-blue-700 text-white"}`}
          >
            {isConverting ? "Compressing..." : "Compress PDF"}
          </button>
        )}

        {isConverting && (
          <div className="space-y-2">
            <div className="w-full bg-gray-200 rounded-full h-2.5">
              <div
                className="bg-blue-600 h-2.5 rounded-full"
                style={{ width: `${conversionProgress}%` }}
              ></div>
            </div>
            <p className="text-sm text-gray-600 text-center">
              {conversionProgress}% complete
            </p>
          </div>
        )}

        {error && (
          <div className="p-4 bg-red-50 text-red-700 rounded-lg">{error}</div>
        )}

        {convertedFileUrl && compressedSize && originalSize && (
          <div className="p-4 bg-green-50 rounded-lg space-y-4">
            <div className="flex items-center text-green-700">
              <svg
                className="w-5 h-5 mr-2"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M5 13l4 4L19 7"
                ></path>
              </svg>
              <span>Compression completed successfully!</span>
            </div>

            <div className="bg-white p-3 rounded border border-green-100">
              <div className="flex justify-between mb-2">
                <span className="text-sm text-gray-600">Original size:</span>
                <span className="text-sm font-medium">
                  {formatFileSize(originalSize)}
                </span>
              </div>
              <div className="flex justify-between mb-2">
                <span className="text-sm text-gray-600">Compressed size:</span>
                <span className="text-sm font-medium">
                  {formatFileSize(compressedSize)}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Reduction:</span>
                <span className="text-sm font-medium text-green-600">
                  {Math.round((1 - compressedSize / originalSize) * 100)}%
                </span>
              </div>
            </div>

            <button
              onClick={handleDownload}
              className="w-full py-2 px-4 bg-green-600 hover:bg-green-700 text-white rounded-md font-medium flex items-center justify-center"
            >
              <svg
                className="w-5 h-5 mr-2"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"
                ></path>
              </svg>
              Download Compressed PDF
            </button>
          </div>
        )}
      </div>

      <div className="mt-8 border-t pt-6">
        <h3 className="text-lg font-semibold mb-4">About PDF Compression</h3>
        <p className="text-gray-700 mb-4">
          Our PDF compressor reduces the file size of your PDF documents while
          maintaining readability and quality. This is useful for sharing
          documents via email, uploading to websites with file size limits, or
          saving storage space. The compression process optimizes images,
          removes redundant information, and applies efficient compression
          algorithms.
        </p>
        <div className="bg-yellow-50 p-4 rounded-lg">
          <h4 className="font-medium text-yellow-800 mb-2">Please Note:</h4>
          <p className="text-yellow-700 text-sm">
            The compression level affects both the file size and the quality of
            the output. Higher compression results in smaller files but may
            reduce image quality. For documents with many images, the high
            compression setting may noticeably affect image clarity. For
            text-heavy documents, even high compression typically maintains good
            readability.
          </p>
        </div>
      </div>
    </div>
  );
}
