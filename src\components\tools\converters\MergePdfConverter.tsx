"use client";

import { useState } from "react";
import FileUploader from "../FileUploader";

export default function MergePdfConverter() {
  const [files, setFiles] = useState<File[]>([]);
  const [isConverting, setIsConverting] = useState(false);
  const [convertedFileUrl, setConvertedFileUrl] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [conversionProgress, setConversionProgress] = useState(0);

  const handleFileSelect = (selectedFile: File) => {
    setFiles((prevFiles) => [...prevFiles, selectedFile]);
    setConvertedFileUrl(null);
    setError(null);
    setConversionProgress(0);
  };

  const removeFile = (index: number) => {
    setFiles((prevFiles) => prevFiles.filter((_, i) => i !== index));
    if (files.length === 1) {
      setConvertedFileUrl(null);
    }
  };

  const moveFileUp = (index: number) => {
    if (index === 0) return;
    const newFiles = [...files];
    [newFiles[index - 1], newFiles[index]] = [
      newFiles[index],
      newFiles[index - 1],
    ];
    setFiles(newFiles);
  };

  const moveFileDown = (index: number) => {
    if (index === files.length - 1) return;
    const newFiles = [...files];
    [newFiles[index], newFiles[index + 1]] = [
      newFiles[index + 1],
      newFiles[index],
    ];
    setFiles(newFiles);
  };

  const handleMerge = async () => {
    if (files.length < 2) {
      setError("Please upload at least two PDF files to merge");
      return;
    }

    // Check if user is authenticated
    try {
      const res = await fetch("/api/auth/me");
      if (!res.ok) {
        // User is not authenticated, redirect to login with converting parameter
        const currentUrl = new URL(window.location.href);
        currentUrl.searchParams.set("converting", "true");
        window.location.href = `/login?callbackUrl=${encodeURIComponent(currentUrl.pathname)}&converting=true`;
        return;
      }
    } catch (error) {
      console.error("Failed to check authentication status:", error);
      setError("Failed to check authentication status. Please try again.");
      return;
    }

    setIsConverting(true);
    setError(null);
    setConversionProgress(0);

    try {
      // Simulate conversion process with progress
      const totalSteps = 10;
      for (let step = 1; step <= totalSteps; step++) {
        await new Promise((resolve) => setTimeout(resolve, 300));
        setConversionProgress(Math.floor((step / totalSteps) * 100));
      }

      // In a real implementation, we would send the files to the server for merging
      // For now, we'll simulate a successful merge
      setConvertedFileUrl(
        URL.createObjectURL(
          new Blob(["Simulated merged PDF document content"], {
            type: "application/pdf",
          }),
        ),
      );
    } catch (err) {
      setError("An error occurred during merging. Please try again.");
      console.error(err);
    } finally {
      setIsConverting(false);
      setConversionProgress(100);
    }
  };

  const handleDownload = () => {
    if (convertedFileUrl) {
      const link = document.createElement("a");
      link.href = convertedFileUrl;
      link.download = "merged_document.pdf";
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  return (
    <div className="space-y-6">
      <div className="bg-blue-50 p-4 rounded-lg">
        <h2 className="text-lg font-semibold mb-2">How to Merge PDF Files</h2>
        <ol className="list-decimal list-inside space-y-2 text-gray-700">
          <li>Upload two or more PDF files using the uploader below.</li>
          <li>
            Arrange the files in the desired order using the up/down arrows.
          </li>
          <li>Click the "Merge PDFs" button to combine the files.</li>
          <li>Download your merged PDF document when ready.</li>
        </ol>
      </div>

      <div className="space-y-4">
        <FileUploader
          acceptedFileTypes=".pdf,application/pdf"
          maxFileSizeMB={10}
          onFileSelect={handleFileSelect}
          multiple={true}
        />

        {files.length > 0 && (
          <div className="space-y-2">
            <h3 className="text-sm font-medium text-gray-700">
              Selected PDF Files ({files.length})
            </h3>
            <div className="max-h-60 overflow-y-auto space-y-2 border rounded-md p-2">
              {files.map((file, index) => (
                <div
                  key={index}
                  className="flex items-center space-x-2 p-2 bg-gray-50 rounded-lg"
                >
                  <div className="flex-shrink-0 text-gray-500">
                    {index + 1}.
                  </div>
                  <svg
                    className="w-6 h-6 text-gray-500 flex-shrink-0"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                    ></path>
                  </svg>
                  <span className="flex-1 truncate">{file.name}</span>
                  <span className="text-sm text-gray-500 flex-shrink-0">
                    {(file.size / (1024 * 1024)).toFixed(2)} MB
                  </span>
                  <div className="flex space-x-1">
                    <button
                      onClick={() => moveFileUp(index)}
                      disabled={index === 0}
                      className={`text-gray-500 ${index === 0 ? "opacity-50 cursor-not-allowed" : "hover:text-gray-700"}`}
                      aria-label="Move up"
                    >
                      <svg
                        className="w-5 h-5"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth="2"
                          d="M5 15l7-7 7 7"
                        ></path>
                      </svg>
                    </button>
                    <button
                      onClick={() => moveFileDown(index)}
                      disabled={index === files.length - 1}
                      className={`text-gray-500 ${index === files.length - 1 ? "opacity-50 cursor-not-allowed" : "hover:text-gray-700"}`}
                      aria-label="Move down"
                    >
                      <svg
                        className="w-5 h-5"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth="2"
                          d="M19 9l-7 7-7-7"
                        ></path>
                      </svg>
                    </button>
                    <button
                      onClick={() => removeFile(index)}
                      className="text-red-500 hover:text-red-700"
                      aria-label="Remove file"
                    >
                      <svg
                        className="w-5 h-5"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth="2"
                          d="M6 18L18 6M6 6l12 12"
                        ></path>
                      </svg>
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {files.length > 0 && (
          <button
            onClick={handleMerge}
            disabled={isConverting || files.length < 2}
            className={`w-full py-2 px-4 rounded-md font-medium ${isConverting || files.length < 2 ? "bg-gray-400 cursor-not-allowed" : "bg-blue-600 hover:bg-blue-700 text-white"}`}
          >
            {isConverting ? "Merging..." : "Merge PDFs"}
          </button>
        )}

        {isConverting && (
          <div className="space-y-2">
            <div className="w-full bg-gray-200 rounded-full h-2.5">
              <div
                className="bg-blue-600 h-2.5 rounded-full"
                style={{ width: `${conversionProgress}%` }}
              ></div>
            </div>
            <p className="text-sm text-gray-600 text-center">
              {conversionProgress}% complete
            </p>
          </div>
        )}

        {error && (
          <div className="p-4 bg-red-50 text-red-700 rounded-lg">{error}</div>
        )}

        {convertedFileUrl && (
          <div className="p-4 bg-green-50 rounded-lg space-y-4">
            <div className="flex items-center text-green-700">
              <svg
                className="w-5 h-5 mr-2"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M5 13l4 4L19 7"
                ></path>
              </svg>
              <span>Merge completed successfully!</span>
            </div>
            <button
              onClick={handleDownload}
              className="w-full py-2 px-4 bg-green-600 hover:bg-green-700 text-white rounded-md font-medium flex items-center justify-center"
            >
              <svg
                className="w-5 h-5 mr-2"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"
                ></path>
              </svg>
              Download Merged PDF
            </button>
          </div>
        )}
      </div>

      <div className="mt-8 border-t pt-6">
        <h3 className="text-lg font-semibold mb-4">About PDF Merging</h3>
        <p className="text-gray-700 mb-4">
          Our PDF merger combines multiple PDF files into a single document
          while preserving the quality and formatting of each page. This is
          useful for creating comprehensive reports, combining related
          documents, or organizing scattered information into a single file.
        </p>
        <div className="bg-yellow-50 p-4 rounded-lg">
          <h4 className="font-medium text-yellow-800 mb-2">Please Note:</h4>
          <p className="text-yellow-700 text-sm">
            The PDFs will be merged in the order shown in the list above. You
            can rearrange them using the up and down arrows. The merged document
            will maintain all text, images, and formatting from the original
            files, but some interactive elements like form fields or JavaScript
            may not function in the merged document.
          </p>
        </div>
      </div>
    </div>
  );
}
