"use client";

import { useState } from "react";
import FileUploader from "../FileUploader";

export default function WordToPdfConverter() {
  const [file, setFile] = useState<File | null>(null);
  const [isConverting, setIsConverting] = useState(false);
  const [convertedFileUrl, setConvertedFileUrl] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [conversionProgress, setConversionProgress] = useState(0);

  const handleFileSelect = (selectedFile: File) => {
    setFile(selectedFile);
    setConvertedFileUrl(null);
    setError(null);
    setConversionProgress(0);
  };

  const handleConvert = async () => {
    if (!file) return;

    // Check if user is authenticated
    try {
      const res = await fetch("/api/auth/me");
      if (!res.ok) {
        // User is not authenticated, redirect to login with converting parameter
        const currentUrl = new URL(window.location.href);
        currentUrl.searchParams.set("converting", "true");
        window.location.href = `/login?callbackUrl=${encodeURIComponent(currentUrl.pathname)}&converting=true`;
        return;
      }
    } catch (error) {
      console.error("Failed to check authentication status:", error);
      setError("Failed to check authentication status. Please try again.");
      return;
    }

    setIsConverting(true);
    setError(null);
    setConversionProgress(0);

    try {
      // Simulate conversion process with progress
      const totalSteps = 10;
      for (let step = 1; step <= totalSteps; step++) {
        await new Promise((resolve) => setTimeout(resolve, 300));
        setConversionProgress(Math.floor((step / totalSteps) * 100));
      }

      // In a real implementation, we would send the file to the server for conversion
      // For now, we'll simulate a successful conversion
      setConvertedFileUrl(
        URL.createObjectURL(
          new Blob(["Simulated PDF document content"], {
            type: "application/pdf",
          }),
        ),
      );
    } catch (err) {
      setError("An error occurred during conversion. Please try again.");
      console.error(err);
    } finally {
      setIsConverting(false);
      setConversionProgress(100);
    }
  };

  const handleDownload = () => {
    if (convertedFileUrl) {
      const link = document.createElement("a");
      link.href = convertedFileUrl;
      link.download = file
        ? file.name.replace(/\.docx?$/, ".pdf")
        : "converted-document.pdf";
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  return (
    <div className="space-y-6">
      <div className="bg-blue-50 p-4 rounded-lg">
        <h2 className="text-lg font-semibold mb-2">
          How to Convert Word to PDF
        </h2>
        <ol className="list-decimal list-inside space-y-2 text-gray-700">
          <li>Upload your Word document using the uploader below.</li>
          <li>Click the "Convert to PDF" button to start the conversion.</li>
          <li>Download your converted PDF document when ready.</li>
        </ol>
      </div>

      <div className="space-y-4">
        <FileUploader
          acceptedFileTypes=".doc,.docx,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document"
          maxFileSizeMB={10}
          onFileSelect={handleFileSelect}
        />

        {file && (
          <div className="flex items-center space-x-2 p-3 bg-gray-50 rounded-lg">
            <svg
              className="w-6 h-6 text-gray-500"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
              ></path>
            </svg>
            <span className="flex-1 truncate">{file.name}</span>
            <span className="text-sm text-gray-500">
              {(file.size / (1024 * 1024)).toFixed(2)} MB
            </span>
            <button
              onClick={() => setFile(null)}
              className="text-red-500 hover:text-red-700"
              aria-label="Remove file"
            >
              <svg
                className="w-5 h-5"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M6 18L18 6M6 6l12 12"
                ></path>
              </svg>
            </button>
          </div>
        )}

        {file && (
          <button
            onClick={handleConvert}
            disabled={isConverting}
            className={`w-full py-2 px-4 rounded-md font-medium ${isConverting ? "bg-gray-400 cursor-not-allowed" : "bg-blue-600 hover:bg-blue-700 text-white"}`}
          >
            {isConverting ? "Converting..." : "Convert to PDF"}
          </button>
        )}

        {isConverting && (
          <div className="space-y-2">
            <div className="w-full bg-gray-200 rounded-full h-2.5">
              <div
                className="bg-blue-600 h-2.5 rounded-full"
                style={{ width: `${conversionProgress}%` }}
              ></div>
            </div>
            <p className="text-sm text-gray-600 text-center">
              {conversionProgress}% complete
            </p>
          </div>
        )}

        {error && (
          <div className="p-4 bg-red-50 text-red-700 rounded-lg">{error}</div>
        )}

        {convertedFileUrl && (
          <div className="p-4 bg-green-50 rounded-lg space-y-4">
            <div className="flex items-center text-green-700">
              <svg
                className="w-5 h-5 mr-2"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M5 13l4 4L19 7"
                ></path>
              </svg>
              <span>Conversion completed successfully!</span>
            </div>
            <button
              onClick={handleDownload}
              className="w-full py-2 px-4 bg-green-600 hover:bg-green-700 text-white rounded-md font-medium flex items-center justify-center"
            >
              <svg
                className="w-5 h-5 mr-2"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"
                ></path>
              </svg>
              Download PDF Document
            </button>
          </div>
        )}
      </div>

      <div className="mt-8 border-t pt-6">
        <h3 className="text-lg font-semibold mb-4">
          About Word to PDF Conversion
        </h3>
        <p className="text-gray-700 mb-4">
          Our Word to PDF converter transforms Microsoft Word documents (.doc or
          .docx) into PDF files while preserving the original formatting,
          images, tables, and text. This is useful when you need to share
          documents in a format that can't be easily edited or when you need to
          ensure your document looks the same on any device.
        </p>
        <div className="bg-yellow-50 p-4 rounded-lg">
          <h4 className="font-medium text-yellow-800 mb-2">Please Note:</h4>
          <p className="text-yellow-700 text-sm">
            The conversion maintains all formatting including fonts, colors,
            images, and layout. However, some complex features like macros,
            animations, or certain embedded objects may not be preserved in the
            PDF output.
          </p>
        </div>
      </div>
    </div>
  );
}
