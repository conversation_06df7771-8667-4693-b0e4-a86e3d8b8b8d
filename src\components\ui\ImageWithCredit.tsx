'use client';

import React from 'react';
import Image from 'next/image';
import { Camera, ExternalLink } from 'lucide-react';

interface ImageWithCreditProps {
  src: string;
  alt: string;
  creditText?: string;
  creditLink?: string;
  width?: number;
  height?: number;
  className?: string;
  creditClassName?: string;
  priority?: boolean;
  fill?: boolean;
  sizes?: string;
  quality?: number;
}

/**
 * Reusable component that renders an image with optional credit information
 * Provides a consistent way to display image attribution across the application
 */
export function ImageWithCredit({
  src,
  alt,
  creditText,
  creditLink,
  width,
  height,
  className = "",
  creditClassName = "",
  priority = false,
  fill = false,
  sizes,
  quality = 75,
}: ImageWithCreditProps) {
  const imageProps = {
    src,
    alt,
    className: `object-cover ${className}`,
    priority,
    quality,
    ...(fill ? { fill: true, sizes } : { width, height }),
  };

  return (
    <div className="relative">
      {/* Main Image */}
      <Image {...imageProps} />
      
      {/* Image Credit */}
      {creditText && (
        <div 
          className={`
            absolute bottom-2 right-2 
            flex items-center gap-1.5 
            px-2 py-1 
            bg-black/60 backdrop-blur-sm 
            rounded-md 
            text-white/90 text-xs 
            transition-opacity duration-200
            hover:bg-black/70
            ${creditClassName}
          `}
          role="img"
          aria-label={`Image credit: ${creditText}`}
        >
          <Camera className="h-3 w-3 flex-shrink-0" />
          {creditLink ? (
            <a
              href={creditLink}
              target="_blank"
              rel="noopener noreferrer"
              className="flex items-center gap-1 hover:text-white transition-colors"
              aria-label={`Image credit link: ${creditText}`}
            >
              <span className="truncate max-w-24">{creditText}</span>
              <ExternalLink className="h-2.5 w-2.5 flex-shrink-0" />
            </a>
          ) : (
            <span className="truncate max-w-24">{creditText}</span>
          )}
        </div>
      )}
    </div>
  );
}

/**
 * Variant for hero images with larger credit display
 */
export function HeroImageWithCredit({
  src,
  alt,
  creditText,
  creditLink,
  className = "",
  creditClassName = "",
  priority = true,
  fill = true,
  sizes = "100vw",
  quality = 85,
}: Omit<ImageWithCreditProps, 'width' | 'height'>) {
  return (
    <div className="relative w-full h-full">
      <Image
        src={src}
        alt={alt}
        fill={fill}
        sizes={sizes}
        className={`object-cover ${className}`}
        priority={priority}
        quality={quality}
      />
      
      {/* Hero Image Credit */}
      {creditText && (
        <div 
          className={`
            absolute bottom-4 right-4 
            flex items-center gap-2 
            px-3 py-2 
            bg-black/50 backdrop-blur-md 
            rounded-lg 
            text-white/80 text-sm 
            transition-all duration-300
            hover:bg-black/60 hover:text-white
            ${creditClassName}
          `}
          role="img"
          aria-label={`Image credit: ${creditText}`}
        >
          <Camera className="h-4 w-4 flex-shrink-0" />
          {creditLink ? (
            <a
              href={creditLink}
              target="_blank"
              rel="noopener noreferrer"
              className="flex items-center gap-1.5 hover:text-white transition-colors"
              aria-label={`Image credit link: ${creditText}`}
            >
              <span>📸 {creditText}</span>
              <ExternalLink className="h-3 w-3 flex-shrink-0" />
            </a>
          ) : (
            <span>📸 {creditText}</span>
          )}
        </div>
      )}
    </div>
  );
}

/**
 * Variant for blog card images with compact credit display
 */
export function BlogCardImageWithCredit({
  src,
  alt,
  creditText,
  creditLink,
  className = "",
  creditClassName = "",
  width = 400,
  height = 250,
  quality = 75,
}: Omit<ImageWithCreditProps, 'fill' | 'sizes' | 'priority'>) {
  return (
    <div className="relative overflow-hidden">
      <Image
        src={src}
        alt={alt}
        width={width}
        height={height}
        className={`object-cover transition-transform duration-300 hover:scale-105 ${className}`}
        quality={quality}
      />
      
      {/* Blog Card Image Credit */}
      {creditText && (
        <div 
          className={`
            absolute bottom-2 right-2 
            flex items-center gap-1 
            px-2 py-1 
            bg-black/60 backdrop-blur-sm 
            rounded-full 
            text-white/80 text-xs 
            opacity-0 group-hover:opacity-100
            transition-opacity duration-200
            ${creditClassName}
          `}
          role="img"
          aria-label={`Image credit: ${creditText}`}
        >
          <Camera className="h-2.5 w-2.5 flex-shrink-0" />
          {creditLink ? (
            <a
              href={creditLink}
              target="_blank"
              rel="noopener noreferrer"
              className="flex items-center gap-0.5 hover:text-white transition-colors"
              aria-label={`Image credit link: ${creditText}`}
            >
              <span className="truncate max-w-16">{creditText}</span>
              <ExternalLink className="h-2 w-2 flex-shrink-0" />
            </a>
          ) : (
            <span className="truncate max-w-16">{creditText}</span>
          )}
        </div>
      )}
    </div>
  );
}
