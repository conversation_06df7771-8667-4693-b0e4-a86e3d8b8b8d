'use client';

import React, { useEffect, useState, useRef } from 'react';
import { useLoading } from '@/contexts/LoadingContext';
import { motion, AnimatePresence } from 'framer-motion';

export function LoadingIndicator() {
  const { isLoading: contextLoading } = useLoading();
  const [isLoading, setIsLoading] = useState(false);
  const initializedRef = useRef(false);

  // Listen for changes to the loading meta tag
  useEffect(() => {
    // Only set the initial state once to prevent infinite loops
    if (!initializedRef.current) {
      setIsLoading(contextLoading);
      initializedRef.current = true;
    }

    // Create an observer to watch for changes to the loading meta tag
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'attributes' && mutation.attributeName === 'content') {
          const loadingMeta = document.querySelector('meta[name="loading"]');
          if (loadingMeta) {
            setIsLoading(loadingMeta.getAttribute('content') === 'true');
          }
        }
      });
    });

    // Find or create the loading meta tag
    let loadingMeta = document.querySelector('meta[name="loading"]');
    if (!loadingMeta) {
      loadingMeta = document.createElement('meta');
      loadingMeta.setAttribute('name', 'loading');
      loadingMeta.setAttribute('content', contextLoading ? 'true' : 'false');
      document.head.appendChild(loadingMeta);
    } else {
      // Check initial state
      setIsLoading(loadingMeta.getAttribute('content') === 'true');
    }

    // Start observing
    if (loadingMeta) {
      observer.observe(loadingMeta, { attributes: true });
    }

    return () => {
      observer.disconnect();
    };
    // We intentionally don't include contextLoading in the dependency array
    // to prevent infinite re-renders. We only want this effect to run once on mount.
  }, []);

  return (
    <AnimatePresence>
      {isLoading && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed top-0 left-0 right-0 z-50 h-1 bg-gradient-to-r from-blue-500 via-purple-500 to-blue-500"
        >
          <motion.div
            className="h-full bg-blue-600"
            initial={{ width: '0%' }}
            animate={{
              width: ['0%', '30%', '60%', '80%', '100%'],
              transition: {
                times: [0, 0.3, 0.6, 0.8, 1],
                duration: 1.5,
                ease: "easeInOut",
                repeat: Infinity
              }
            }}
          />
        </motion.div>
      )}
    </AnimatePresence>
  );
}
