"use client";

import React from "react";
import { motion } from "framer-motion";
import { Loader2, <PERSON>fresh<PERSON><PERSON>, <PERSON>ap, Circle } from "lucide-react";
import { cn } from "@/lib/utils";

export interface LoadingSpinnerProps {
  size?: "sm" | "md" | "lg" | "xl";
  variant?: "default" | "dots" | "pulse" | "bounce" | "spin" | "bars";
  color?: "primary" | "secondary" | "muted" | "destructive" | "success";
  text?: string;
  className?: string;
  fullScreen?: boolean;
  overlay?: boolean;
}

const sizeClasses = {
  sm: "w-4 h-4",
  md: "w-6 h-6",
  lg: "w-8 h-8",
  xl: "w-12 h-12",
};

const colorClasses = {
  primary: "text-primary",
  secondary: "text-secondary",
  muted: "text-muted-foreground",
  destructive: "text-destructive",
  success: "text-green-500",
};

// Default spinning loader
const SpinLoader = ({ size, color, className }: { size: string; color: string; className?: string }) => (
  <Loader2 className={cn(size, color, "animate-spin", className)} />
);

// Dots loader
const DotsLoader = ({ size, color, className }: { size: string; color: string; className?: string }) => {
  const dotSize = size === "w-4 h-4" ? "w-1 h-1" : size === "w-6 h-6" ? "w-1.5 h-1.5" : size === "w-8 h-8" ? "w-2 h-2" : "w-3 h-3";
  
  return (
    <div className={cn("flex space-x-1", className)}>
      {[0, 1, 2].map((i) => (
        <motion.div
          key={i}
          className={cn(dotSize, "rounded-full bg-current", color)}
          animate={{
            scale: [1, 1.2, 1],
            opacity: [0.7, 1, 0.7],
          }}
          transition={{
            duration: 0.6,
            repeat: Infinity,
            delay: i * 0.2,
          }}
        />
      ))}
    </div>
  );
};

// Pulse loader
const PulseLoader = ({ size, color, className }: { size: string; color: string; className?: string }) => (
  <motion.div
    className={cn(size, "rounded-full border-2 border-current", color, className)}
    animate={{
      scale: [1, 1.2, 1],
      opacity: [1, 0.5, 1],
    }}
    transition={{
      duration: 1,
      repeat: Infinity,
    }}
  />
);

// Bounce loader
const BounceLoader = ({ size, color, className }: { size: string; color: string; className?: string }) => {
  const ballSize = size === "w-4 h-4" ? "w-2 h-2" : size === "w-6 h-6" ? "w-3 h-3" : size === "w-8 h-8" ? "w-4 h-4" : "w-6 h-6";
  
  return (
    <div className={cn("flex space-x-1", className)}>
      {[0, 1, 2].map((i) => (
        <motion.div
          key={i}
          className={cn(ballSize, "rounded-full bg-current", color)}
          animate={{
            y: [0, -10, 0],
          }}
          transition={{
            duration: 0.6,
            repeat: Infinity,
            delay: i * 0.1,
          }}
        />
      ))}
    </div>
  );
};

// Bars loader
const BarsLoader = ({ size, color, className }: { size: string; color: string; className?: string }) => {
  const barHeight = size === "w-4 h-4" ? "h-4" : size === "w-6 h-6" ? "h-6" : size === "w-8 h-8" ? "h-8" : "h-12";
  
  return (
    <div className={cn("flex items-end space-x-1", className)}>
      {[0, 1, 2, 3].map((i) => (
        <motion.div
          key={i}
          className={cn("w-1 bg-current", color, barHeight)}
          animate={{
            scaleY: [1, 0.3, 1],
          }}
          transition={{
            duration: 0.8,
            repeat: Infinity,
            delay: i * 0.1,
          }}
        />
      ))}
    </div>
  );
};

// Refresh loader
const RefreshLoader = ({ size, color, className }: { size: string; color: string; className?: string }) => (
  <motion.div
    animate={{ rotate: 360 }}
    transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
    className={className}
  >
    <RefreshCw className={cn(size, color)} />
  </motion.div>
);

export function LoadingSpinner({
  size = "md",
  variant = "default",
  color = "primary",
  text,
  className,
  fullScreen = false,
  overlay = false,
}: LoadingSpinnerProps) {
  const sizeClass = sizeClasses[size];
  const colorClass = colorClasses[color];

  const renderLoader = () => {
    const props = { size: sizeClass, color: colorClass, className };
    
    switch (variant) {
      case "dots":
        return <DotsLoader {...props} />;
      case "pulse":
        return <PulseLoader {...props} />;
      case "bounce":
        return <BounceLoader {...props} />;
      case "spin":
        return <RefreshLoader {...props} />;
      case "bars":
        return <BarsLoader {...props} />;
      default:
        return <SpinLoader {...props} />;
    }
  };

  const content = (
    <div className={cn(
      "flex flex-col items-center justify-center gap-3",
      fullScreen && "min-h-screen",
      className
    )}>
      {renderLoader()}
      {text && (
        <motion.p
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.2 }}
          className={cn("text-sm", colorClass)}
        >
          {text}
        </motion.p>
      )}
    </div>
  );

  if (overlay) {
    return (
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-background/80 backdrop-blur-sm z-50 flex items-center justify-center"
      >
        {content}
      </motion.div>
    );
  }

  return content;
}

// Skeleton loader components
export function SkeletonLoader({ className, ...props }: React.HTMLAttributes<HTMLDivElement>) {
  return (
    <div
      className={cn("animate-pulse rounded-md bg-muted", className)}
      {...props}
    />
  );
}

export function SkeletonText({ lines = 3, className }: { lines?: number; className?: string }) {
  return (
    <div className={cn("space-y-2", className)}>
      {Array.from({ length: lines }).map((_, i) => (
        <SkeletonLoader
          key={i}
          className={cn(
            "h-4",
            i === lines - 1 ? "w-3/4" : "w-full"
          )}
        />
      ))}
    </div>
  );
}

export function SkeletonCard({ className }: { className?: string }) {
  return (
    <div className={cn("space-y-3", className)}>
      <SkeletonLoader className="h-48 w-full" />
      <div className="space-y-2">
        <SkeletonLoader className="h-4 w-3/4" />
        <SkeletonLoader className="h-4 w-1/2" />
      </div>
    </div>
  );
}

// Loading states for specific components
export function BlogPostSkeleton() {
  return (
    <div className="space-y-6">
      <SkeletonLoader className="h-64 w-full" />
      <div className="space-y-4">
        <SkeletonLoader className="h-8 w-3/4" />
        <SkeletonLoader className="h-4 w-1/4" />
        <SkeletonText lines={5} />
      </div>
    </div>
  );
}

export function CalculatorSkeleton() {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      <div className="space-y-4">
        {Array.from({ length: 4 }).map((_, i) => (
          <div key={i} className="space-y-2">
            <SkeletonLoader className="h-4 w-1/3" />
            <SkeletonLoader className="h-10 w-full" />
          </div>
        ))}
      </div>
      <div className="space-y-4">
        <SkeletonCard />
      </div>
    </div>
  );
}

export default LoadingSpinner;
