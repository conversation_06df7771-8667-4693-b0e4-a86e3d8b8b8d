"use client";

import * as React from "react";
import { format } from "date-fns";
import { Calendar as CalendarIcon } from "lucide-react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface DateTimePickerProps {
  value?: Date | null;
  onChange: (date: Date | null) => void;
}

export function DateTimePicker({ value, onChange }: DateTimePickerProps) {
  const [date, setDate] = React.useState<Date | undefined>(value || undefined);

  // Update internal state when prop changes
  React.useEffect(() => {
    setDate(value || undefined);
  }, [value]);

  // Handle date change from calendar
  const handleDateChange = (newDate: Date | undefined) => {
    if (!newDate) return;

    const newDateTime = date ? new Date(date) : new Date();
    newDateTime.setFullYear(newDate.getFullYear());
    newDateTime.setMonth(newDate.getMonth());
    newDateTime.setDate(newDate.getDate());

    setDate(newDateTime);
    onChange(newDateTime);
  };

  // Handle hour change
  const handleHourChange = (hour: string) => {
    if (!date) return;

    const newDate = new Date(date);
    newDate.setHours(parseInt(hour));

    setDate(newDate);
    onChange(newDate);
  };

  // Handle minute change
  const handleMinuteChange = (minute: string) => {
    if (!date) return;

    const newDate = new Date(date);
    newDate.setMinutes(parseInt(minute));

    setDate(newDate);
    onChange(newDate);
  };

  // Generate hours options
  const hours = Array.from({ length: 24 }, (_, i) => i);

  // Generate minutes options
  const minutes = Array.from({ length: 60 }, (_, i) => i);

  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          className={cn(
            "w-full justify-start text-left font-normal",
            !date && "text-muted-foreground"
          )}
        >
          <CalendarIcon className="mr-2 h-4 w-4" />
          {date ? format(date, "PPP p") : "Select date and time"}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-auto p-0" align="start">
        <Calendar
          mode="single"
          selected={date}
          onSelect={handleDateChange}
        />
        <div className="flex items-center p-3 border-t border-border">
          <div className="space-y-1">
            <p className="text-sm font-medium">Time</p>
            <div className="flex items-center space-x-2">
              <Select
                value={date ? date.getHours().toString() : undefined}
                onValueChange={handleHourChange}
              >
                <SelectTrigger className="w-[70px]">
                  <SelectValue placeholder="Hour" />
                </SelectTrigger>
                <SelectContent>
                  {hours.map((hour) => (
                    <SelectItem key={hour} value={hour.toString()}>
                      {hour.toString().padStart(2, "0")}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <span className="text-sm">:</span>
              <Select
                value={date ? date.getMinutes().toString() : undefined}
                onValueChange={handleMinuteChange}
              >
                <SelectTrigger className="w-[70px]">
                  <SelectValue placeholder="Min" />
                </SelectTrigger>
                <SelectContent>
                  {minutes.map((minute) => (
                    <SelectItem key={minute} value={minute.toString()}>
                      {minute.toString().padStart(2, "0")}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );
}