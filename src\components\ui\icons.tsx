// components/ui/icons.tsx
import type { LucideIcon } from "lucide-react";

// 1. Define the icon props type
interface IconProps extends React.SVGProps<SVGSVGElement> {
  size?: number;
  className?: string;
}

// 2. Create your custom Tool icon component
export const Tool = ({ size = 24, className, ...props }: IconProps) => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
    className={className}
    {...props}
  >
    {/* Custom tool icon path */}
    <path d="M12 2v4M4.93 4.93l2.83 2.83M2 12h4M4.93 19.07l2.83-2.83M12 22v-4M19.07 4.93l-2.83 2.83M22 12h-4M19.07 19.07l-2.83-2.83" />
  </svg>
);

// 3. Add TypeScript type assertion
export const ToolIcon = Tool as LucideIcon;