'use client';

import React, { createContext, useContext, useState, ReactNode, useEffect, useCallback } from 'react';
import { usePathname, useSearchParams } from 'next/navigation';
import { Progress } from '@/components/ui/nprogress';

interface LoadingContextType {
  isLoading: boolean;
  setLoading: (loading: boolean) => void;
  startLoading: () => void;
  stopLoading: () => void;
  pageTitle: string;
  setPageTitle: (title: string) => void;
}

const LoadingContext = createContext<LoadingContextType | undefined>(undefined);

export function LoadingProvider({ children }: { children: ReactNode }) {
  const [isLoading, setIsLoading] = useState(false);
  const [pageTitle, setPageTitle] = useState('ToolCrush');
  const pathname = usePathname();
  const searchParams = useSearchParams();

  // Update meta tag for loading state
  const updateLoadingMeta = useCallback((loading: boolean) => {
    if (typeof document !== 'undefined') {
      // Find or create the loading meta tag
      let meta = document.querySelector('meta[name="loading"]');
      if (!meta) {
        meta = document.createElement('meta');
        meta.setAttribute('name', 'loading');
        document.head.appendChild(meta);
      }
      meta.setAttribute('content', loading ? 'true' : 'false');
    }
  }, []);

  // Enhanced loading functions that update both state and meta tag
  const startLoading = useCallback(() => {
    setIsLoading(true);
    updateLoadingMeta(true);
    Progress.start();
  }, [updateLoadingMeta]);

  const stopLoading = useCallback(() => {
    setIsLoading(false);
    updateLoadingMeta(false);
    Progress.done();
  }, [updateLoadingMeta]);

  // Set loading state directly with meta tag update
  const setLoading = useCallback((loading: boolean) => {
    setIsLoading(loading);
    updateLoadingMeta(loading);
    if (loading) {
      Progress.start();
    } else {
      Progress.done();
    }
  }, [updateLoadingMeta]);

  // Reset loading state and update page title on route change
  useEffect(() => {
    // Start loading when route changes
    startLoading();

    // Set a timeout to stop loading after a short delay
    const timer = setTimeout(() => {
      stopLoading();
    }, 300); // Reduced from 500ms for better UX

    // Update page title based on current route
    updatePageTitle(pathname);

    return () => clearTimeout(timer);
  }, [pathname, searchParams, startLoading, stopLoading]);

  // Helper function to update page title based on current route
  const updatePageTitle = useCallback((path: string) => {
    let title = 'ToolCrush';

    if (path.startsWith('/tools')) {
      title = 'Tools | ToolCrush';
    } else if (path.startsWith('/calculators')) {
      title = 'Calculators | ToolCrush';
    } else if (path.startsWith('/blog')) {
      title = 'Blog | ToolCrush';
    } else if (path.startsWith('/admin')) {
      title = 'Admin Dashboard | ToolCrush';
    } else if (path === '/login') {
      title = 'Sign In | ToolCrush';
    } else if (path === '/signup') {
      title = 'Sign Up | ToolCrush';
    }

    setPageTitle(title);

    // Update document title if in browser
    if (typeof document !== 'undefined') {
      document.title = title;
    }
  }, []);

  return (
    <LoadingContext.Provider
      value={{
        isLoading,
        setLoading,
        startLoading,
        stopLoading,
        pageTitle,
        setPageTitle
      }}
    >
      {children}
    </LoadingContext.Provider>
  );
}

export function useLoading() {
  const context = useContext(LoadingContext);
  if (context === undefined) {
    throw new Error('useLoading must be used within a LoadingProvider');
  }
  return context;
}
