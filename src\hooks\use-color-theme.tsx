"use client";

import { createContext, useContext, useEffect, useState, ReactNode } from "react";

type ColorTheme = {
  primary: string;
  secondary: string;
  accent: string;
};

interface ColorThemeContextType {
  colors: ColorTheme;
  setColor: (key: keyof ColorTheme, value: string) => void;
  resetColors: () => void;
}

const defaultColors: ColorTheme = {
  primary: "#0d6efd", // Blue (Bootstrap primary)
  secondary: "#6c757d", // Gray (Bootstrap secondary)
  accent: "#198754", // Green (Bootstrap success)
};

const ColorThemeContext = createContext<ColorThemeContextType | undefined>(undefined);

export function ColorThemeProvider({ children }: { children: ReactNode }) {
  const [colors, setColors] = useState<ColorTheme>(defaultColors);
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);

    // Load colors from localStorage on mount
    try {
      const savedColors = localStorage.getItem("colorTheme");
      if (savedColors) {
        setColors(JSON.parse(savedColors));
      }
    } catch (error) {
      console.error("Error loading color theme:", error);
    }
  }, []);

  useEffect(() => {
    if (!mounted) return;

    try {
      // Save colors to localStorage when they change
      localStorage.setItem("colorTheme", JSON.stringify(colors));

      // Convert hex colors to RGB and apply to CSS variables
      Object.entries(colors).forEach(([key, value]) => {
        // Convert hex to RGB
        const hexToRgb = (hex: string): string => {
          // Remove # if present
          hex = hex.replace('#', '');

          // Parse the hex values
          const r = parseInt(hex.substring(0, 2), 16);
          const g = parseInt(hex.substring(2, 4), 16);
          const b = parseInt(hex.substring(4, 6), 16);

          return `${r}, ${g}, ${b}`;
        };

        // Apply RGB values to CSS variables
        document.documentElement.style.setProperty(`--${key}`, hexToRgb(value));
      });
    } catch (error) {
      console.error("Error saving color theme:", error);
    }
  }, [colors, mounted]);

  const setColor = (key: keyof ColorTheme, value: string) => {
    setColors(prev => ({ ...prev, [key]: value }));
  };

  const resetColors = () => {
    setColors(defaultColors);
  };

  return (
    <ColorThemeContext.Provider value={{ colors, setColor, resetColors }}>
      {children}
    </ColorThemeContext.Provider>
  );
}

export function useColorTheme() {
  const context = useContext(ColorThemeContext);
  if (context === undefined) {
    throw new Error("useColorTheme must be used within a ColorThemeProvider");
  }
  return context;
}