'use client';

import { useState, useEffect, useCallback } from 'react';
import { fetchWithCache, clearCacheEntry } from '@/lib/apiCache';
import logger from '@/lib/secureLogger';

interface UseApiCacheOptions<T> {
  url: string;
  initialData?: T;
  ttl?: number;
  enabled?: boolean;
  onSuccess?: (data: T) => void;
  onError?: (error: Error) => void;
  fetchOptions?: RequestInit;
  cacheKey?: string;
}

/**
 * Custom hook for fetching API data with caching
 *
 * @param options - Hook options
 * @returns Object with data, loading state, error, and refetch function
 */
export function useApiCache<T>({
  url,
  initialData,
  ttl = 5 * 60 * 1000, // 5 minutes default
  enabled = true,
  onSuccess,
  onError,
  fetchOptions = {},
  cacheKey,
}: UseApiCacheOptions<T>) {
  const [data, setData] = useState<T | undefined>(initialData);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<Error | null>(null);
  const [lastFetchTime, setLastFetchTime] = useState<number | null>(null);

  // Use the URL as the cache key if not provided
  const effectiveCacheKey = cacheKey || url;

  // Fetch data function
  const fetchData = useCallback(async (bypassCache = false) => {
    // Don't show loading state if we already have data (prevents UI flashing)
    const hadExistingData = !!data;

    try {
      if (!hadExistingData) {
        setLoading(true);
      }
      setError(null);

      const result = await fetchWithCache<T>(
        url,
        fetchOptions,
        {
          ttl,
          bypassCache,
          cacheKey: effectiveCacheKey,
        }
      );

      setData(result);
      setLastFetchTime(Date.now());

      if (onSuccess) {
        onSuccess(result);
      }

      return result;
    } catch (err) {
      const error = err instanceof Error ? err : new Error(String(err));
      setError(error);

      // Don't call onError if we have existing data and are just refreshing
      if (onError && (!hadExistingData || bypassCache)) {
        onError(error);
      }

      logger.error(`API fetch error: ${url}`);
      return undefined;
    } finally {
      setLoading(false);
    }
  }, [url, fetchOptions, ttl, effectiveCacheKey, onSuccess, onError, data]);

  // Refetch data function (bypass cache)
  const refetch = useCallback(() => {
    return fetchData(true);
  }, [fetchData]);

  // Clear cache and refetch
  const invalidateAndRefetch = useCallback(async () => {
    clearCacheEntry(effectiveCacheKey);
    return fetchData(true);
  }, [effectiveCacheKey, fetchData]);

  // Initial fetch
  useEffect(() => {
    if (enabled) {
      fetchData();
    }
  }, [enabled, fetchData]);

  return {
    data,
    loading,
    error,
    refetch,
    invalidateAndRefetch,
    lastFetchTime,
  };
}

/**
 * Custom hook for fetching API data with caching and automatic refetching
 *
 * @param options - Hook options
 * @returns Object with data, loading state, error, and refetch function
 */
export function useApiCacheWithRefresh<T>(
  options: UseApiCacheOptions<T> & { refreshInterval?: number }
) {
  const { refreshInterval = 0, ...restOptions } = options;
  const { data, loading, error, refetch, invalidateAndRefetch, lastFetchTime } = useApiCache<T>(restOptions);

  // Set up refresh interval
  useEffect(() => {
    if (!refreshInterval || refreshInterval <= 0) {
      return;
    }

    const intervalId = setInterval(() => {
      refetch();
    }, refreshInterval);

    return () => {
      clearInterval(intervalId);
    };
  }, [refreshInterval, refetch]);

  return {
    data,
    loading,
    error,
    refetch,
    invalidateAndRefetch,
    lastFetchTime,
  };
}

export default useApiCache;
