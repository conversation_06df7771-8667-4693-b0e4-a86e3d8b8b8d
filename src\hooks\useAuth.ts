"use client";

import { useSession, signOut } from "next-auth/react";
import { useAppSelector, useAppDispatch } from "@/redux/hooks";
import { clearUser } from "@/redux/slices/authSlice";

/**
 * Hook to manage authentication state and actions
 * Combines NextAuth session data with Redux state for compatibility
 */
export function useAuth() {
  const { data: session, status } = useSession();
  const authState = useAppSelector(state => state.auth);
  const dispatch = useAppDispatch();

  // Determine authentication status
  const isAuthenticated = status === "authenticated" && !!session;
  const isLoading = status === "loading";

  // User information
  const user = session?.user || null;

  // Role checks
  const isAdmin = user?.role === "admin";
  const isUser = user?.role === "user";

  // Logout function
  const logout = async () => {
    try {
      // Clear Redux state
      dispatch(clearUser());
      
      // Sign out with NextAuth
      await signOut({ 
        callbackUrl: "/login",
        redirect: true 
      });
    } catch (error) {
      console.error("Logout error:", error);
    }
  };

  return {
    // Authentication state
    isAuthenticated,
    isLoading,
    session,
    
    // User info
    user,
    
    // Role checks
    isAdmin,
    isUser,
    
    // Actions
    logout,
    
    // Redux state (for compatibility with existing code)
    authState,
  };
}
