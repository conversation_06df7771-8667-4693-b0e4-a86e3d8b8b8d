"use client";

import { useState, useEffect, useCallback } from "react";

interface AutoSaveOptions {
  key: string;
  interval?: number;
  onSave?: (data: any) => void;
}

export function useAutoSave<T>(
  data: T,
  { key, interval = 5000, onSave }: AutoSaveOptions
) {
  const [lastSaved, setLastSaved] = useState<Date | null>(null);
  const [isRestored, setIsRestored] = useState(false);

  // Save data to sessionStorage
  const saveData = useCallback(() => {
    if (typeof window === "undefined") return;

    try {
      sessionStorage.setItem(key, JSON.stringify(data));
      setLastSaved(new Date());
      onSave?.(data);
    } catch (error) {
      console.error("Error saving data to sessionStorage:", error);
    }
  }, [data, key, onSave]);

  // Restore data from sessionStorage
  const restoreData = useCallback((): T | null => {
    if (typeof window === "undefined") return null;

    try {
      const savedData = sessionStorage.getItem(key);
      if (savedData) {
        return JSON.parse(savedData);
      }
    } catch (error) {
      console.error("Error restoring data from sessionStorage:", error);
    }
    return null;
  }, [key]);

  // Clear saved data
  const clearSavedData = useCallback(() => {
    if (typeof window === "undefined") return;

    try {
      sessionStorage.removeItem(key);
      setLastSaved(null);
    } catch (error) {
      console.error("Error clearing data from sessionStorage:", error);
    }
  }, [key]);

  // Set up auto-save interval
  useEffect(() => {
    if (typeof window === "undefined") return;

    const timer = setInterval(() => {
      saveData();
    }, interval);

    return () => clearInterval(timer);
  }, [saveData, interval]);

  // Restore data on initial load
  useEffect(() => {
    if (typeof window === "undefined" || isRestored) return;

    const savedData = restoreData();
    if (savedData) {
      setIsRestored(true);
    }
  }, [restoreData, isRestored]);

  return {
    lastSaved,
    saveData,
    restoreData,
    clearSavedData,
    isRestored,
  };
}
