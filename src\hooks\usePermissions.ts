"use client";

import { useSession } from "next-auth/react";
import { useAppSelector } from "@/redux/hooks";
import { UserRole, hasRouteAccess, isProtectedUser } from "@/lib/auth/roles";

/**
 * Hook to manage user permissions and role-based access
 * Combines NextAuth session data with Redux state
 */
export function usePermissions() {
  const { data: session, status } = useSession();
  const authState = useAppSelector(state => state.auth);

  // Determine authentication status
  const isAuthenticated = status === "authenticated" && !!session;
  const isLoading = status === "loading" || authState.loading;

  // Determine user role
  const userRole = session?.user?.role || "user";
  const isAdmin = userRole === "admin";
  const isUser = userRole === "user";

  // Permission checks
  const canViewAnalytics = isAdmin;
  const canManageUsers = isAdmin;
  const canManageSettings = isAdmin;
  const canManageBlog = isAdmin;
  const canManageTools = isAdmin;

  // Route access check
  const canAccessRoute = (route: string): boolean => {
    if (!isAuthenticated) return false;
    return hasRouteAccess(userRole as UserRole, route);
  };

  // Check if current user account is protected
  const isCurrentUserProtected = session?.user?.email 
    ? isProtectedUser(session.user.email) 
    : false;

  return {
    // Authentication state
    isAuthenticated,
    isLoading,
    session,
    
    // User info
    user: session?.user || null,
    userRole: userRole as UserRole,
    
    // Role checks
    isAdmin,
    isUser,
    
    // Permission checks
    canViewAnalytics,
    canManageUsers,
    canManageSettings,
    canManageBlog,
    canManageTools,
    
    // Utility functions
    canAccessRoute,
    isCurrentUserProtected,
    
    // Redux state (for compatibility)
    authState,
  };
}
