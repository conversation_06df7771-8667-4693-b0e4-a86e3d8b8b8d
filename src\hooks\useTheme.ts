"use client";

import { useState, useEffect } from 'react';

export type Theme = 'light' | 'dark';

export function useTheme() {
  const [theme, setTheme] = useState<Theme>('light');

  // Initialize theme from localStorage on component mount
  useEffect(() => {
    // Only run on client-side
    if (typeof window === 'undefined') return;

    try {
      // Check if this is the first visit (no theme preference saved yet)
      const isFirstVisit = !localStorage.getItem('themeInitialized');

      // For first-time visitors, ensure we start with light mode
      if (isFirstVisit) {
        localStorage.setItem('theme', 'light');
        localStorage.setItem('themeInitialized', 'true');
      }

      const savedTheme = localStorage.getItem('theme') as Theme | null;

      // Always default to light mode unless explicitly set to dark in localStorage
      const themeToApply = savedTheme || 'light';

      // Update state
      setTheme(themeToApply);

      // Apply theme to document
      applyThemeToDocument(themeToApply);

      console.log(`Theme initialized to: ${themeToApply}`);

      // Listen for theme changes from other components
      const handleThemeChange = (event: any) => {
        const newTheme = event.detail.theme;
        setTheme(newTheme);
        applyThemeToDocument(newTheme);
      };

      window.addEventListener('themeChanged', handleThemeChange);

      // Clean up event listener
      return () => {
        window.removeEventListener('themeChanged', handleThemeChange);
      };
    } catch (error) {
      console.error('Error initializing theme:', error);
    }
  }, []);

  // Helper function to apply theme to document
  const applyThemeToDocument = (theme: Theme) => {
    // Remove both classes first
    document.documentElement.classList.remove('light', 'dark');

    // Add the appropriate class
    document.documentElement.classList.add(theme);

    // Also add a data attribute to the body for additional styling hooks
    document.body.setAttribute('data-theme', theme);

    // Set CSS variables for theme colors
    if (theme === 'dark') {
      document.documentElement.style.setProperty('--bg-primary', '#111827'); // gray-900
      document.documentElement.style.setProperty('--bg-secondary', '#1f2937'); // gray-800
      document.documentElement.style.setProperty('--text-primary', '#f9fafb'); // gray-50
      document.documentElement.style.setProperty('--text-secondary', '#e5e7eb'); // gray-200
    } else {
      document.documentElement.style.setProperty('--bg-primary', '#ffffff'); // white
      document.documentElement.style.setProperty('--bg-secondary', '#f9fafb'); // gray-50
      document.documentElement.style.setProperty('--text-primary', '#111827'); // gray-900
      document.documentElement.style.setProperty('--text-secondary', '#4b5563'); // gray-600
    }

    // Removed forced reflow which could cause performance issues
  };

  // This is the only way users can switch to dark mode - by explicitly clicking the toggle
  const toggleTheme = () => {
    const newTheme = theme === 'light' ? 'dark' : 'light';
    setTheme(newTheme);

    // Save to localStorage
    localStorage.setItem('theme', newTheme);

    // Apply theme to document using our helper function
    applyThemeToDocument(newTheme);

    console.log(`Theme switched to: ${newTheme}`);

    // Dispatch a custom event that other components can listen for
    const event = new CustomEvent('themeChanged', { detail: { theme: newTheme } });
    window.dispatchEvent(event);
  };

  return { theme, toggleTheme };
}

