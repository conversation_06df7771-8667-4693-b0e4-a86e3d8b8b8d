'use server';
import connectToDatabase from '@/lib/db';
import Tool from '@/models/ToolModel';
import { revalidatePath } from 'next/cache';

export async function getTools() {
  try {
    await connectToDatabase();
    const tools = await Tool.find().lean();
    return JSON.parse(JSON.stringify(tools));
  } catch (error) {
    console.error('Error fetching tools:', error);
    return [];
  }
}

export async function getToolById(id: string) {
  try {
    await connectToDatabase();
    const tool = await Tool.findById(id).lean();
    return JSON.parse(JSON.stringify(tool));
  } catch (error) {
    console.error('Error fetching tool:', error);
    return null;
  }
}

export async function createUpdateTool(formData: FormData) {
  try {
    await connectToDatabase();
    
    const toolData = {
      name: formData.get('name'),
      description: formData.get('description'),
      category: formData.get('category'),
    };

    let tool;
    if (formData.get('_id')) {
      tool = await Tool.findByIdAndUpdate(formData.get('_id'), toolData, { new: true });
    } else {
      tool = await Tool.create(toolData);
    }

    revalidatePath('/admin/tools');
    return JSON.parse(JSON.stringify(tool));
  } catch (error) {
    console.error('Error saving tool:', error);
    return { error: 'Failed to save tool' };
  }
}

export async function deleteTool(id: string) {
  try {
    await connectToDatabase();
    await Tool.findByIdAndDelete(id);
    
    revalidatePath('/admin/tools');
    return { success: true };
  } catch (error) {
    console.error('Error deleting tool:', error);
    return { error: 'Failed to delete tool' };
  }
}