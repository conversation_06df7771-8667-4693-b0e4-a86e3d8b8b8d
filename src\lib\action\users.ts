'use server';
import connectToDatabase from '@/lib/db';
import User from '@/models/User';
import { revalidatePath } from 'next/cache';

export async function getUsers() {
  try {
    await connectToDatabase();
    const users = await User.find().select('-password').lean();
    return JSON.parse(JSON.stringify(users));
  } catch (error) {
    console.error('Error fetching users:', error);
    return [];
  }
}

export async function getUserById(id: string) {
  try {
    await connectToDatabase();
    const user = await User.findById(id).select('-password').lean();
    return JSON.parse(JSON.stringify(user));
  } catch (error) {
    console.error('Error fetching user:', error);
    return null;
  }
}

export async function createUpdateUser(formData: FormData) {
  try {
    await connectToDatabase();
    
    const userData = {
      name: formData.get('name'),
      email: formData.get('email'),
      role: formData.get('role') || 'user',
    };

    let user;
    if (formData.get('_id')) {
      user = await User.findByIdAndUpdate(formData.get('_id'), userData, { new: true });
    } else {
      user = await User.create(userData);
    }

    revalidatePath('/admin/users');
    return JSON.parse(JSON.stringify(user));
  } catch (error) {
    console.error('Error saving user:', error);
    return { error: 'Failed to save user' };
  }
}

export async function deleteUser(id: string) {
  try {
    await connectToDatabase();
    await User.findByIdAndDelete(id);
    
    revalidatePath('/admin/users');
    return { success: true };
  } catch (error) {
    console.error('Error deleting user:', error);
    return { error: 'Failed to delete user' };
  }
}