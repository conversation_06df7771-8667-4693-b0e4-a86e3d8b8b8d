"use server";

export async function handleExcelToPdfFileName(fileName: string) {
  return fileName.replace(/\.xlsx?$/, ".pdf");
}

export async function handlePdfToWordFileName(fileName: string) {
  return fileName.replace(".pdf", ".docx");
}

export async function handlePdfToExcelFileName(fileName: string) {
  return fileName.replace(".pdf", ".xlsx");
}

export async function handlePdfToPowerPointFileName(fileName: string) {
  return fileName.replace(".pdf", ".pptx");
}

export async function handlePdfToPdfaFileName(fileName: string) {
  return fileName.replace(".pdf", "_pdfa.pdf");
}

export async function handleWordToPdfFileName(fileName: string) {
  return fileName.replace(/\.docx?$/, ".pdf");
}

export async function handlePowerPointToPdfFileName(fileName: string) {
  return fileName.replace(/\.pptx?$/, ".pdf");
}

export async function handleCompressPdfFileName(fileName: string) {
  return fileName.replace(".pdf", "_compressed.pdf");
}

export async function handleRotatePdfFileName(fileName: string) {
  return fileName.replace(".pdf", "_rotated.pdf");
}

// Add other filename handlers as needed