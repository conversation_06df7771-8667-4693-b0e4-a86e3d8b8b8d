// lib/auth.ts
import { type NextAuthOptions, type DefaultSession } from "next-auth";
import Credential<PERSON><PERSON>rovider from "next-auth/providers/credentials";
import { connectToDatabase } from "./mongo";
import bcrypt from "bcryptjs";

// Type extensions for NextAuth
declare module "next-auth" {
  interface Session extends DefaultSession {
    user: {
      id: string;
      role: "admin" | "user";
    } & DefaultSession["user"];
  }

  interface User {
    role?: "admin" | "user";
  }
}

declare module "next-auth/jwt" {
  interface JWT {
    role?: "admin" | "user";
    sub: string;
  }
}

export const authOptions: NextAuthOptions = {
  // Use JWT strategy for sessions
  session: {
    strategy: "jwt",
    maxAge: 30 * 24 * 60 * 60, // 30 days
  },

  // Authentication providers - only credentials
  providers: [
    CredentialsProvider({
      name: "credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" }
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          return null;
        }

        try {
          const { db } = await connectToDatabase();
          const normalizedEmail = credentials.email.toLowerCase();

          // Find user in MongoDB
          const user = await db.collection("users").findOne({
            email: normalizedEmail
          });

          if (!user) {
            return null;
          }

          // Verify password
          const isValid = await bcrypt.compare(credentials.password, user.password);

          if (!isValid) {
            return null;
          }

          // Return user object for NextAuth
          return {
            id: user._id.toString(),
            name: user.name,
            email: user.email,
            role: user.role || "user",
          };
        } catch (error) {
          console.error("Authentication error:", error);
          return null;
        }
      }
    })
  ],

  // Callbacks to add role to JWT and session
  callbacks: {
    jwt({ token, user }) {
      if (user) {
        token.role = user.role;
      }
      return token;
    },
    session({ session, token }) {
      if (session.user) {
        session.user.id = token.sub!;
        session.user.role = token.role ?? "user";
      }
      return session;
    }
  },

  // Custom pages
  pages: {
    signIn: "/login",
    error: "/login",
  },

  // Security
  secret: process.env.NEXTAUTH_SECRET!,
};
