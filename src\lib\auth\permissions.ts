import { UserRole, hasRouteAccess, isProtectedUser } from './roles';

/**
 * Utility functions for checking user permissions
 */

// Check if a user can access a specific route
export function canAccessRoute(userRole: string, route: string): boolean {
  return hasRouteAccess(userRole as UserRole, route);
}

// Check if a user can modify another user's role
export function canModifyUserRole(
  currentUserId: string,
  currentUserRole: string,
  targetUserId: string,
  targetUserRole: string,
  newRole: string
): boolean {
  // Only admins can modify roles
  if (currentUserRole !== UserRole.ADMIN) {
    return false;
  }

  // Check if the target user is protected
  if (isProtectedUser(targetUserId)) {
    return false;
  }

  // Admins can't downgrade other admins
  if (targetUserRole === UserRole.ADMIN && newRole !== UserRole.ADMIN) {
    return false;
  }

  // Admins can't modify their own role
  if (currentUserId === targetUserId) {
    return false;
  }

  return true;
}

// Check if a user can edit a blog post
export function canEditBlogPost(
  userRole: string,
  userId: string,
  postAuthorId: string
): boolean {
  // Only admins can edit posts
  return userRole === UserRole.ADMIN;
}

// Check if a user can delete a blog post
export function canDeleteBlogPost(
  userRole: string,
  userId: string,
  postAuthorId: string
): boolean {
  // Only admins can delete posts
  return userRole === UserRole.ADMIN;
}

// Check if a user can create a blog post
export function canCreateBlogPost(userRole: string): boolean {
  // Only admins can create posts
  return userRole === UserRole.ADMIN;
}

// Check if a user can access the admin dashboard
export function canAccessAdminDashboard(userRole: string): boolean {
  return userRole === UserRole.ADMIN;
}

// Check if a user can manage users
export function canManageUsers(userRole: string): boolean {
  return userRole === UserRole.ADMIN;
}

// Check if a user can manage tools
export function canManageTools(userRole: string): boolean {
  return userRole === UserRole.ADMIN;
}

// Check if a user can view analytics
export function canViewAnalytics(userRole: string): boolean {
  return userRole === UserRole.ADMIN;
}

// Check if a user can manage settings
export function canManageSettings(userRole: string): boolean {
  return userRole === UserRole.ADMIN;
}
