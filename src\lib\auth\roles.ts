// User roles enum
export enum UserRole {
  ADMIN = "admin",
  USER = "user",
}

// Route access configuration
const ROUTE_ACCESS: Record<string, UserRole[]> = {
  "/admin": [UserRole.ADMIN],
  "/admin/*": [UserRole.ADMIN],
  "/dashboard": [UserRole.USER, UserRole.ADMIN],
  "/dashboard/*": [UserRole.USER, UserRole.ADMIN],
};

// Protected user accounts that cannot have their roles changed
const PROTECTED_USERS = [
  "<EMAIL>",
  "<EMAIL>",
];

/**
 * Check if a user role has access to a specific route
 */
export function hasRouteAccess(userRole: UserRole, route: string): boolean {
  // Check exact match first
  if (ROUTE_ACCESS[route]) {
    return ROUTE_ACCESS[route].includes(userRole);
  }

  // Check wildcard matches
  for (const [routePattern, allowedRoles] of Object.entries(ROUTE_ACCESS)) {
    if (routePattern.endsWith("/*")) {
      const baseRoute = routePattern.slice(0, -2);
      if (route.startsWith(baseRoute)) {
        return allowedRoles.includes(userRole);
      }
    }
  }

  // Default: allow access for non-protected routes
  return true;
}

/**
 * Check if a user account is protected from role changes
 */
export function isProtectedUser(email: string): boolean {
  return PROTECTED_USERS.includes(email.toLowerCase());
}

/**
 * Get display name for a role
 */
export function getRoleDisplayName(role: UserRole): string {
  switch (role) {
    case UserRole.ADMIN:
      return "Administrator";
    case UserRole.USER:
      return "User";
    default:
      return "Unknown";
  }
}

/**
 * Get all available roles
 */
export function getAllRoles(): UserRole[] {
  return Object.values(UserRole);
}

/**
 * Check if a role string is valid
 */
export function isValidRole(role: string): role is UserRole {
  return Object.values(UserRole).includes(role as UserRole);
}
