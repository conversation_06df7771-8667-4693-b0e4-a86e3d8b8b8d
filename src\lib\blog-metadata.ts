import { Metadata } from "next";

// This would typically come from a database
const blogPosts = {
  "pdf-management-tips": {
    title: "5 Tips for Efficient PDF Management",
    category: "Tips & Tricks",
    description: "Learn how to organize, compress, and manage your PDF documents effectively.",
    image: "https://images.unsplash.com/photo-1606857521015-7f9fcf423740?w=800&q=80",
    author: "<PERSON>",
    date: "June 15, 2023",
  },
  "word-to-pdf-benefits": {
    title: "When to Convert Word to PDF",
    category: "Conversion",
    description: "Discover the advantages of converting your Word documents to PDF format.",
    image: "https://images.unsplash.com/photo-1586281380349-632531db7ed4?w=800&q=80",
    author: "<PERSON>",
    date: "July 2, 2023",
  },
  "pdf-security-guide": {
    title: "Securing Your PDF Documents",
    category: "Security",
    description: "Learn how to protect your PDF documents with passwords, encryption, and more.",
    image: "https://images.unsplash.com/photo-1563013544-824ae1b704d3?w=800&q=80",
    author: "<PERSON>",
    date: "July 18, 2023",
  },
  "pdf-accessibility": {
    title: "Making PDFs Accessible for Everyone",
    category: "Accessibility",
    description: "Discover how to create accessible PDF documents for users with disabilities.",
    image: "https://images.unsplash.com/photo-1586282391129-76a6df230234?w=800&q=80",
    author: "<PERSON>",
    date: "August 5, 2023",
  },
  "pdf-vs-other-formats": {
    title: "PDF vs. Other Document Formats",
    category: "Comparison",
    description: "A comprehensive comparison of PDF with other document formats.",
    image: "https://images.unsplash.com/photo-1568667256549-094345857637?w=800&q=80",
    author: "Chris Wilson",
    date: "August 22, 2023",
  },
  "pdf-compression-techniques": {
    title: "Advanced PDF Compression Techniques",
    category: "Optimization",
    description: "Learn advanced techniques for compressing PDF files without losing quality.",
    image: "https://images.unsplash.com/photo-1618044733300-9472054094ee?w=800&q=80",
    author: "Pat Rivera",
    date: "September 10, 2023",
  },
};

// app/blog/[slug]/page.tsx
interface PageProps {
  params: { slug: string };
  searchParams?: { [key: string]: string | string[] | undefined };
}

export async function generateMetadata({ params }: PageProps): Promise<Metadata> {
  const post = blogPosts[params.slug as keyof typeof blogPosts];

  if (!post) {
    return {
      title: "Post Not Found - PDF Tools Blog",
      description: "The requested blog post could not be found.",
    };
  }

  return {
    title: `${post.title} - PDF Tools Blog`,
    description: post.description,
    keywords: `PDF tools, ${post.category.toLowerCase()}, PDF management, PDF tips`,
    authors: [{ name: post.author }],
    openGraph: {
      title: post.title,
      description: post.description,
      images: [
        {
          url: post.image,
          width: 800,
          height: 600,
          alt: post.title,
        },
      ],
      type: "article",
      publishedTime: post.date,
      authors: [post.author],
      tags: [post.category],
    },
    twitter: {
      card: "summary_large_image",
      title: post.title,
      description: post.description,
      images: [post.image],
    },
  };
}
