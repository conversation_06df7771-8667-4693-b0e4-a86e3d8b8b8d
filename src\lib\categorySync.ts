/**
 * Utility functions for category post count synchronization
 */

import connectToDatabase from '@/lib/db';
import Category from '@/models/Category';
import BlogPost from '@/models/BlogPost';

/**
 * Update post count for a specific category
 * @param categoryId - The category ID to update
 * @returns Updated category with new count
 */
export async function updateCategoryPostCount(categoryId: string) {
  try {
    await connectToDatabase();

    // Count published posts for this category
    const count = await BlogPost.countDocuments({
      categoryId: categoryId,
      status: 'published',
      visibility: 'public'
    });

    // Update the category count
    const updatedCategory = await Category.findByIdAndUpdate(
      categoryId,
      { count },
      { new: true }
    );

    return updatedCategory;
  } catch (error) {
    console.error('Error updating category post count:', error);
    throw error;
  }
}

/**
 * Update post counts for multiple categories
 * @param categoryIds - Array of category IDs to update
 * @returns Array of updated categories
 */
export async function updateMultipleCategoryPostCounts(categoryIds: string[]) {
  try {
    await connectToDatabase();

    const updatedCategories = [];

    for (const categoryId of categoryIds) {
      if (categoryId) {
        const updated = await updateCategoryPostCount(categoryId);
        if (updated) {
          updatedCategories.push(updated);
        }
      }
    }

    return updatedCategories;
  } catch (error) {
    console.error('Error updating multiple category post counts:', error);
    throw error;
  }
}

/**
 * Recalculate all category post counts
 * @returns Array of all updated categories
 */
export async function recalculateAllCategoryPostCounts() {
  try {
    await connectToDatabase();

    // Get all categories
    const categories = await Category.find();
    const updatedCategories = [];

    for (const category of categories) {
      const count = await BlogPost.countDocuments({
        categoryId: category._id,
        status: 'published',
        visibility: 'public'
      });

      const updated = await Category.findByIdAndUpdate(
        category._id,
        { count },
        { new: true }
      );

      if (updated) {
        updatedCategories.push(updated);
      }
    }

    return updatedCategories;
  } catch (error) {
    console.error('Error recalculating all category post counts:', error);
    throw error;
  }
}

/**
 * Handle category changes when a blog post is created/updated/deleted
 * @param oldCategoryId - Previous category ID (for updates)
 * @param newCategoryId - New category ID
 * @param operation - Type of operation: 'create', 'update', 'delete'
 */
export async function handleBlogPostCategoryChange(
  oldCategoryId: string | null,
  newCategoryId: string | null,
  operation: 'create' | 'update' | 'delete'
) {
  try {
    const categoriesToUpdate = new Set<string>();

    switch (operation) {
      case 'create':
        if (newCategoryId) {
          categoriesToUpdate.add(newCategoryId);
        }
        break;

      case 'update':
        if (oldCategoryId) {
          categoriesToUpdate.add(oldCategoryId);
        }
        if (newCategoryId && newCategoryId !== oldCategoryId) {
          categoriesToUpdate.add(newCategoryId);
        }
        break;

      case 'delete':
        if (oldCategoryId) {
          categoriesToUpdate.add(oldCategoryId);
        }
        break;
    }

    // Update all affected categories
    if (categoriesToUpdate.size > 0) {
      await updateMultipleCategoryPostCounts(Array.from(categoriesToUpdate));
    }
  } catch (error) {
    console.error('Error handling blog post category change:', error);
    throw error;
  }
}

/**
 * Get category with post count
 * @param categoryId - The category ID
 * @returns Category with updated post count
 */
export async function getCategoryWithPostCount(categoryId: string) {
  try {
    await connectToDatabase();

    const category = await Category.findById(categoryId);
    if (!category) {
      return null;
    }

    const count = await BlogPost.countDocuments({
      categoryId: categoryId,
      status: 'published',
      visibility: 'public'
    });

    return {
      ...category.toObject(),
      count
    };
  } catch (error) {
    console.error('Error getting category with post count:', error);
    throw error;
  }
}
