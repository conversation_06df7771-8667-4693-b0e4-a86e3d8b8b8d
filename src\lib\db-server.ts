// lib/db-server.ts - Server-only database utilities
import { cache } from 'react';

// This file should only be imported on the server side
if (typeof window !== 'undefined') {
  throw new Error('db-server.ts should only be imported on the server side');
}

// Dynamic import to prevent client-side bundling
const getDatabase = cache(async () => {
  const connectToDatabase = (await import('./db')).default;
  return connectToDatabase();
});

// Dynamic import for BlogPost model
const getBlogPostModel = cache(async () => {
  const BlogPost = (await import('../models/BlogPost')).default;
  return BlogPost;
});

// Server-only function to fetch blog posts
export const fetchBlogPosts = cache(async (options: {
  page?: number;
  limit?: number;
  status?: string;
  category?: string;
  search?: string;
} = {}) => {
  try {
    await getDatabase();
    const BlogPost = await getBlogPostModel();

    const {
      page = 1,
      limit = 9,
      status = 'published',
      category,
      search
    } = options;

    const skip = (page - 1) * limit;
    
    // Build query
    const query: any = {};
    
    if (status) {
      query.status = status;
    }
    
    if (category && category !== 'all') {
      query.categories = { $in: [category] };
    }
    
    if (search) {
      query.$or = [
        { title: { $regex: search, $options: 'i' } },
        { content: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } }
      ];
    }

    // Execute query
    const [posts, total] = await Promise.all([
      BlogPost.find(query)
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit)
        .lean(),
      BlogPost.countDocuments(query)
    ]);

    return {
      posts: posts.map(post => ({
        ...post,
        _id: post._id.toString(),
        createdAt: post.createdAt?.toISOString(),
        updatedAt: post.updatedAt?.toISOString(),
      })),
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit)
    };
  } catch (error) {
    console.error('Error fetching blog posts:', error);
    return {
      posts: [],
      total: 0,
      page: 1,
      limit: 9,
      totalPages: 0
    };
  }
});

// Server-only function to fetch a single blog post
export const fetchBlogPost = cache(async (slug: string) => {
  try {
    await getDatabase();
    const BlogPost = await getBlogPostModel();

    const post = await BlogPost.findOne({ 
      slug,
      status: 'published'
    }).lean();

    if (!post) {
      return null;
    }

    return {
      ...post,
      _id: post._id.toString(),
      createdAt: post.createdAt?.toISOString(),
      updatedAt: post.updatedAt?.toISOString(),
    };
  } catch (error) {
    console.error('Error fetching blog post:', error);
    return null;
  }
});

// Server-only function to fetch recent blog posts
export const fetchRecentBlogPosts = cache(async (limit: number = 3) => {
  try {
    await getDatabase();
    const BlogPost = await getBlogPostModel();

    const posts = await BlogPost.find({ status: 'published' })
      .sort({ createdAt: -1 })
      .limit(limit)
      .lean();

    return posts.map(post => ({
      ...post,
      _id: post._id.toString(),
      createdAt: post.createdAt?.toISOString(),
      updatedAt: post.updatedAt?.toISOString(),
    }));
  } catch (error) {
    console.error('Error fetching recent blog posts:', error);
    return [];
  }
});

// Server-only function to fetch categories
export const fetchCategoriesFromDB = cache(async () => {
  try {
    await getDatabase();
    const BlogPost = await getBlogPostModel();

    // Aggregate categories from blog posts
    const categoryAggregation = await BlogPost.aggregate([
      { $match: { status: 'published' } },
      { $unwind: '$categories' },
      {
        $group: {
          _id: '$categories',
          count: { $sum: 1 }
        }
      },
      {
        $project: {
          _id: 1,
          name: '$_id',
          slug: {
            $toLower: {
              $replaceAll: {
                input: {
                  $replaceAll: {
                    input: '$_id',
                    find: ' ',
                    replacement: '-'
                  }
                },
                find: /[^\w-]/g,
                replacement: ''
              }
            }
          },
          count: 1
        }
      },
      { $sort: { count: -1, name: 1 } }
    ]);

    return categoryAggregation.map(cat => ({
      _id: cat._id,
      name: cat.name,
      slug: cat.slug,
      count: cat.count,
      description: `${cat.count} ${cat.count === 1 ? 'post' : 'posts'} in ${cat.name}`
    }));
  } catch (error) {
    console.error('Error fetching categories from database:', error);
    return [];
  }
});

// Server-only function to get related blog posts
export const fetchRelatedBlogPosts = cache(async (
  currentSlug: string,
  category: string,
  tags: string[] = [],
  limit: number = 6
) => {
  try {
    await getDatabase();
    const BlogPost = await getBlogPostModel();

    const query: any = {
      slug: { $ne: currentSlug },
      status: 'published',
      $or: [
        { categories: { $in: [category] } },
        { tags: { $in: tags } }
      ]
    };

    const posts = await BlogPost.find(query)
      .sort({ createdAt: -1 })
      .limit(limit)
      .lean();

    return posts.map(post => ({
      ...post,
      _id: post._id.toString(),
      createdAt: post.createdAt?.toISOString(),
      updatedAt: post.updatedAt?.toISOString(),
    }));
  } catch (error) {
    console.error('Error fetching related blog posts:', error);
    return [];
  }
});
