// lib/db.ts
import mongoose, { Connection, ConnectOptions } from "mongoose";

// Global augmentation for Mongoose connection cache
declare global {
  // eslint-disable-next-line no-var
  var mongoose: {
    conn: Connection | null;
    promise: Promise<Connection> | null;
  };
  // eslint-disable-next-line no-var
  var mongoClient: {
    client: any | null;
    promise: Promise<any> | null;
  };
}

const MONGODB_URI = process.env.MONGODB_URI!;

if (!MONGODB_URI) {
  throw new Error(
    "Please define the MONGODB_URI environment variable inside .env.local"
  );
}

// Initialize cached connection
let cached = global.mongoose;
let cachedClient = global.mongoClient;

if (!cached) {
  cached = global.mongoose = { conn: null, promise: null };
}

if (!cachedClient) {
  cachedClient = global.mongoClient = { client: null, promise: null };
}

async function connectToDatabase(): Promise<Connection> {
  if (cached.conn) {
    // Completely suppress cached connection logs to avoid spam
    return cached.conn;
  }

  if (!cached.promise) {
    console.log("Creating new database connection");
    const opts: ConnectOptions = {
      bufferCommands: false,
    };

    cached.promise = mongoose.connect(MONGODB_URI, opts).then((mongoose) => {
      console.log("MongoDB connection established");
      return mongoose.connection;
    });
  }

  try {
    cached.conn = await cached.promise;
    console.log("Database connection successful");
  } catch (e) {
    console.error("Database connection error:", e);
    cached.promise = null;
    throw e;
  }

  return cached.conn;
}

// Separate function for NextAuth MongoDB adapter
export async function getMongoClient() {
  if (cachedClient.client) {
    return cachedClient.client;
  }

  if (!cachedClient.promise) {
    const { MongoClient } = await import("mongodb");
    cachedClient.promise = new MongoClient(MONGODB_URI).connect();
  }

  try {
    cachedClient.client = await cachedClient.promise;
  } catch (e) {
    cachedClient.promise = null;
    throw e;
  }

  return cachedClient.client;
}

// For NextAuth MongoDB adapter
export const mongoClientPromise = getMongoClient();

// For regular Mongoose operations
export const connectMongoose = async () => {
  const conn = await connectToDatabase();
  return conn;
};

export default connectToDatabase;