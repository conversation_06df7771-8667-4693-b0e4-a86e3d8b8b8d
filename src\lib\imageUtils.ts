/**
 * Utility functions for image processing and featured image extraction
 */

/**
 * Extract the first image URL from HTML content
 * @param htmlContent - The HTML content to parse
 * @returns The URL of the first image found, or null if no image exists
 */
export function extractFeaturedImageFromContent(htmlContent: string): string | null {
  if (!htmlContent || typeof htmlContent !== 'string') {
    return null;
  }

  try {
    // Create a temporary DOM element to parse HTML
    if (typeof window !== 'undefined') {
      // Client-side parsing
      const tempDiv = document.createElement('div');
      tempDiv.innerHTML = htmlContent;
      const firstImg = tempDiv.querySelector('img');
      return firstImg?.src || null;
    } else {
      // Server-side parsing using regex (more reliable for server environments)
      const imgRegex = /<img[^>]+src\s*=\s*["']([^"']+)["'][^>]*>/i;
      const match = htmlContent.match(imgRegex);
      return match ? match[1] : null;
    }
  } catch (error) {
    console.error('Error extracting featured image:', error);
    return null;
  }
}

/**
 * Validate if a URL is a valid image URL
 * @param url - The URL to validate
 * @returns True if the URL appears to be a valid image URL
 */
export function isValidImageUrl(url: string): boolean {
  if (!url || typeof url !== 'string') {
    return false;
  }

  try {
    const urlObj = new URL(url);
    const pathname = urlObj.pathname.toLowerCase();
    const validExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.svg', '.bmp'];
    
    return validExtensions.some(ext => pathname.endsWith(ext)) || 
           pathname.includes('/uploads/images/') || // Our local uploads
           url.includes('data:image/'); // Base64 images
  } catch {
    return false;
  }
}

/**
 * Clean and normalize image URL
 * @param url - The image URL to clean
 * @returns Cleaned URL or null if invalid
 */
export function cleanImageUrl(url: string): string | null {
  if (!url || typeof url !== 'string') {
    return null;
  }

  const trimmed = url.trim();
  
  if (!isValidImageUrl(trimmed)) {
    return null;
  }

  // Convert relative URLs to absolute if they're local uploads
  if (trimmed.startsWith('/uploads/')) {
    return trimmed;
  }

  return trimmed;
}

/**
 * Extract all image URLs from HTML content
 * @param htmlContent - The HTML content to parse
 * @returns Array of image URLs found in the content
 */
export function extractAllImagesFromContent(htmlContent: string): string[] {
  if (!htmlContent || typeof htmlContent !== 'string') {
    return [];
  }

  try {
    const imgRegex = /<img[^>]+src\s*=\s*["']([^"']+)["'][^>]*>/gi;
    const matches = [];
    let match;

    while ((match = imgRegex.exec(htmlContent)) !== null) {
      const url = cleanImageUrl(match[1]);
      if (url) {
        matches.push(url);
      }
    }

    return matches;
  } catch (error) {
    console.error('Error extracting images from content:', error);
    return [];
  }
}
