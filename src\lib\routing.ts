// Centralized routing configuration for user-side routes
import { ALL_TOOLS } from '@/data/tools';
import { ALL_CALCULATORS } from '@/data/calculators';

// Route types
export interface RouteConfig {
  path: string;
  component?: string;
  title: string;
  description: string;
  keywords?: string[];
  dynamic?: boolean;
}

// Static routes configuration
export const STATIC_ROUTES: Record<string, RouteConfig> = {
  home: {
    path: '/',
    title: 'ToolCrush - All-in-one PDF & Calculator Tools',
    description: 'Free online PDF tools and calculators. Convert, merge, compress PDFs and calculate mortgages, BMI, tips and more.',
    keywords: ['PDF tools', 'calculators', 'online tools', 'free tools']
  },
  tools: {
    path: '/tools',
    title: 'PDF Tools - Convert, Merge, Compress PDFs',
    description: 'Free online PDF tools to convert, merge, compress, and edit PDF files. Fast, secure, and easy to use.',
    keywords: ['PDF tools', 'convert PDF', 'merge PDF', 'compress PDF']
  },
  calculators: {
    path: '/calculators',
    title: 'Online Calculators - Financial, Math, Health & More',
    description: 'Free online calculators for finance, math, health, and conversions. Calculate mortgages, BMI, tips, and more.',
    keywords: ['calculators', 'financial calculator', 'math calculator', 'health calculator']
  },
  calculatorsAll: {
    path: '/calculators/all',
    title: 'All Calculators - Complete Collection',
    description: 'Browse our complete collection of online calculators organized by category.',
    keywords: ['all calculators', 'calculator collection', 'online calculators']
  },
  blog: {
    path: '/blog',
    title: 'Blog - Tips, Tutorials & Guides',
    description: 'Learn tips, tutorials, and best practices for PDF management and using online tools.',
    keywords: ['blog', 'tutorials', 'PDF tips', 'guides']
  },
  blogAll: {
    path: '/blog/all',
    title: 'All Articles - Blog',
    description: 'Browse all our articles, tutorials, and guides.',
    keywords: ['all articles', 'blog posts', 'tutorials']
  },
  login: {
    path: '/login',
    title: 'Login - ToolCrush',
    description: 'Login to your ToolCrush account to access premium features.',
    keywords: ['login', 'sign in', 'account']
  },
  register: {
    path: '/register',
    title: 'Sign Up - ToolCrush',
    description: 'Create a free ToolCrush account to access all features.',
    keywords: ['sign up', 'register', 'create account']
  }
};

// Dynamic route generators
export const generateToolRoutes = (): RouteConfig[] => {
  return ALL_TOOLS.map(tool => ({
    path: `/tools/${tool.id}`,
    title: `${tool.title} - Free Online Tool`,
    description: tool.description,
    keywords: [tool.title.toLowerCase(), tool.category, 'online tool', 'free'],
    dynamic: true
  }));
};

export const generateCalculatorRoutes = (): RouteConfig[] => {
  return ALL_CALCULATORS.map(calculator => ({
    path: `/calculators/${calculator.id}`,
    title: `${calculator.title} - Free Online Calculator`,
    description: calculator.description,
    keywords: [calculator.title.toLowerCase(), calculator.category, 'calculator', 'free'],
    dynamic: true
  }));
};

// Route helpers
export const getToolRoute = (toolId: string): string => `/tools/${toolId}`;
export const getCalculatorRoute = (calculatorId: string): string => `/calculators/${calculatorId}`;
export const getBlogRoute = (slug: string): string => `/blog/${slug}`;

// Navigation helpers
export const isActiveRoute = (currentPath: string, targetPath: string): boolean => {
  if (targetPath === '/') {
    return currentPath === '/';
  }
  return currentPath.startsWith(targetPath);
};

// Route validation
export const isValidToolRoute = (slug: string): boolean => {
  return ALL_TOOLS.some(tool => tool.id === slug);
};

export const isValidCalculatorRoute = (slug: string): boolean => {
  return ALL_CALCULATORS.some(calculator => calculator.id === slug);
};

// Get all user-side routes (excluding admin)
export const getAllUserRoutes = (): RouteConfig[] => {
  const staticRoutes = Object.values(STATIC_ROUTES);
  const toolRoutes = generateToolRoutes();
  const calculatorRoutes = generateCalculatorRoutes();

  return [...staticRoutes, ...toolRoutes, ...calculatorRoutes];
};

// Slug redirects mapping for handling old URLs
export const SLUG_REDIRECTS: Record<string, string> = {
  // Calculator redirects
  'old-mortgage-calc': 'mortgage-calculator',
  'bmi-calc': 'bmi-calculator',
  'tip-calc': 'tip-calculator',

  // Tool redirects
  'pdf-compress': 'compress-pdf',
  'pdf-merge': 'merge-pdf',
  'word-pdf': 'word-to-pdf',

  // Blog redirects
  'pdf-tips': 'pdf-management-tips',
  'word-pdf-guide': 'word-to-pdf-benefits',
};

// Content type definitions
export type ContentType = 'calculators' | 'tools' | 'blogs';

// Unified page data interface
export interface PageData {
  id: string;
  title: string;
  description: string;
  icon?: string;
  category?: string;
  popular?: boolean;
  comingSoon?: boolean;
  // Tool-specific
  inputFormat?: string;
  outputFormat?: string;
  hasConfig?: boolean;
  // Blog-specific
  author?: string;
  date?: string;
  image?: string;
  content?: string;
  tags?: string[];
  _id?: string;
}

// Get page data by type and slug
export const getPageData = (type: ContentType, slug: string): PageData | null => {
  // Handle slug redirects
  const actualSlug = SLUG_REDIRECTS[slug] || slug;

  switch (type) {
    case 'calculators':
      const { ALL_CALCULATORS } = require('@/data/calculators');
      return ALL_CALCULATORS.find((calc: any) => calc.id === actualSlug) || null;

    case 'tools':
      const { ALL_TOOLS } = require('@/data/tools');
      return ALL_TOOLS.find((tool: any) => tool.id === actualSlug) || null;

    case 'blogs':
      const { blogPosts } = require('@/data/blog-posts');
      const blogData = blogPosts[actualSlug];
      return blogData ? { id: actualSlug, ...blogData } : null;

    default:
      return null;
  }
};

// Validate if a route exists
export const isValidRoute = (type: ContentType, slug: string): boolean => {
  return getPageData(type, slug) !== null;
};

// Get metadata for dynamic pages
export const getDynamicMetadata = (type: ContentType, slug: string, data?: PageData) => {
  const pageData = data || getPageData(type, slug);

  if (!pageData) {
    return {
      title: 'Page Not Found - ToolCrush',
      description: 'The requested page could not be found.',
      robots: { index: false, follow: false }
    };
  }

  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://toolcrush.com';
  const canonicalUrl = `${baseUrl}/${type}/${slug}`;

  // Type-specific metadata
  const typeLabels = {
    calculators: 'Calculator',
    tools: 'Tool',
    blogs: 'Article'
  };

  const title = `${pageData.title} - Free Online ${typeLabels[type]} | ToolCrush`;
  const description = pageData.description || `Use our free ${pageData.title.toLowerCase()} online. Fast, secure, and easy to use.`;

  return {
    title,
    description,
    keywords: [
      pageData.title.toLowerCase(),
      type.slice(0, -1), // Remove 's' from type
      pageData.category || 'online',
      'free',
      'toolcrush'
    ].join(', '),
    alternates: {
      canonical: canonicalUrl,
    },
    robots: {
      index: true,
      follow: true,
    },
    openGraph: {
      title,
      description,
      type: 'website',
      url: canonicalUrl,
      siteName: 'ToolCrush',
      images: pageData.image ? [
        {
          url: pageData.image,
          width: 1200,
          height: 630,
          alt: pageData.title,
        }
      ] : undefined,
    },
    twitter: {
      card: 'summary_large_image',
      title,
      description,
      images: pageData.image ? [pageData.image] : undefined,
    },
    // Custom metadata
    other: {
      [`${type.slice(0, -1)}-category`]: pageData.category || 'general',
      [`${type.slice(0, -1)}-popular`]: pageData.popular ? 'true' : 'false',
      ...(type === 'tools' && pageData.inputFormat && pageData.outputFormat && {
        'tool-formats': `${pageData.inputFormat} to ${pageData.outputFormat}`,
      }),
      ...(type === 'blogs' && pageData.author && {
        'article-author': pageData.author,
        'article-date': pageData.date || '',
      }),
    },
  };
};

// Generate static params for unified dynamic routes
export const generateUnifiedStaticParams = () => {
  const params: Array<{ type: string; slug: string }> = [];

  // Add calculator params
  const { ALL_CALCULATORS } = require('@/data/calculators');
  ALL_CALCULATORS.forEach((calc: any) => {
    params.push({ type: 'calculators', slug: calc.id });
  });

  // Add tool params
  const { ALL_TOOLS } = require('@/data/tools');
  ALL_TOOLS.forEach((tool: any) => {
    params.push({ type: 'tools', slug: tool.id });
  });

  // Add blog params
  const { blogPosts } = require('@/data/blog-posts');
  Object.keys(blogPosts).forEach((slug) => {
    params.push({ type: 'blogs', slug });
  });

  return params;
};

// Get all slugs for a specific content type
export const getSlugsByType = (type: ContentType): string[] => {
  switch (type) {
    case 'calculators':
      const { ALL_CALCULATORS } = require('@/data/calculators');
      return ALL_CALCULATORS.map((calc: any) => calc.id);

    case 'tools':
      const { ALL_TOOLS } = require('@/data/tools');
      return ALL_TOOLS.map((tool: any) => tool.id);

    case 'blogs':
      const { blogPosts } = require('@/data/blog-posts');
      return Object.keys(blogPosts);

    default:
      return [];
  }
};

// Check if slug needs redirection
export const getRedirectSlug = (slug: string): string | null => {
  return SLUG_REDIRECTS[slug] || null;
};

// Route metadata generator (existing function, enhanced)
export const generateRouteMetadata = (path: string) => {
  const allRoutes = getAllUserRoutes();
  const route = allRoutes.find(r => r.path === path);

  if (!route) {
    return {
      title: 'Page Not Found - ToolCrush',
      description: 'The page you are looking for could not be found.'
    };
  }

  return {
    title: route.title,
    description: route.description,
    keywords: route.keywords?.join(', '),
    openGraph: {
      title: route.title,
      description: route.description,
      type: 'website',
      url: route.path,
    },
    twitter: {
      card: 'summary_large_image',
      title: route.title,
      description: route.description,
    },
  };
};
