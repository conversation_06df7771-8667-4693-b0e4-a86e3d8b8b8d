/**
 * Secure Logger Service
 * 
 * This service provides a way to log messages without exposing sensitive information.
 * It automatically masks sensitive data and provides consistent log formatting.
 */

// Define log levels
export enum LogLevel {
  DEBUG = 'debug',
  INFO = 'info',
  SUCCESS = 'success',
  WARN = 'warn',
  ERROR = 'error'
}

// Define sensitive patterns to mask
const SENSITIVE_PATTERNS = [
  // JWT tokens
  /eyJ[a-zA-Z0-9_-]+\.[a-zA-Z0-9_-]+\.[a-zA-Z0-9_-]+/g,
  // Email addresses
  /[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/g,
  // User IDs (assuming MongoDB ObjectId format)
  /[0-9a-fA-F]{24}/g,
  // IP addresses
  /\b\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}\b/g,
  // Database connection strings
  /mongodb(\+srv)?:\/\/[^\s]+/g
];

/**
 * Mask sensitive information in a string
 * 
 * @param message - The message to mask
 * @returns The masked message
 */
function maskSensitiveInfo(message: string): string {
  let maskedMessage = message;
  
  // Apply each pattern
  SENSITIVE_PATTERNS.forEach(pattern => {
    maskedMessage = maskedMessage.replace(pattern, '[REDACTED]');
  });
  
  return maskedMessage;
}

/**
 * Log a message with the specified level
 * 
 * @param level - The log level
 * @param message - The message to log
 * @param data - Optional data to log
 */
function log(level: LogLevel, message: string, data?: any): void {
  // Skip logging in production unless it's an error
  if (process.env.NODE_ENV === 'production' && level !== LogLevel.ERROR) {
    return;
  }
  
  // Mask sensitive information in the message
  const maskedMessage = maskSensitiveInfo(message);
  
  // Format the message based on log level
  let formattedMessage = maskedMessage;
  
  // Add emoji based on log level
  switch (level) {
    case LogLevel.SUCCESS:
      formattedMessage = `✔️ ${maskedMessage}`;
      break;
    case LogLevel.INFO:
      formattedMessage = `ℹ️ ${maskedMessage}`;
      break;
    case LogLevel.WARN:
      formattedMessage = `⚠️ ${maskedMessage}`;
      break;
    case LogLevel.ERROR:
      formattedMessage = `❌ ${maskedMessage}`;
      break;
    case LogLevel.DEBUG:
      formattedMessage = `🔍 ${maskedMessage}`;
      break;
  }
  
  // Log the message with the appropriate console method
  switch (level) {
    case LogLevel.DEBUG:
      console.debug(formattedMessage);
      break;
    case LogLevel.INFO:
    case LogLevel.SUCCESS:
      console.info(formattedMessage);
      break;
    case LogLevel.WARN:
      console.warn(formattedMessage);
      break;
    case LogLevel.ERROR:
      console.error(formattedMessage);
      break;
  }
  
  // Log additional data if provided
  if (data) {
    // Mask sensitive information in data
    const maskedData = JSON.parse(maskSensitiveInfo(JSON.stringify(data)));
    
    switch (level) {
      case LogLevel.DEBUG:
        console.debug(maskedData);
        break;
      case LogLevel.INFO:
      case LogLevel.SUCCESS:
        console.info(maskedData);
        break;
      case LogLevel.WARN:
        console.warn(maskedData);
        break;
      case LogLevel.ERROR:
        console.error(maskedData);
        break;
    }
  }
}

// Export individual log methods
export const logger = {
  debug: (message: string, data?: any) => log(LogLevel.DEBUG, message, data),
  info: (message: string, data?: any) => log(LogLevel.INFO, message, data),
  success: (message: string, data?: any) => log(LogLevel.SUCCESS, message, data),
  warn: (message: string, data?: any) => log(LogLevel.WARN, message, data),
  error: (message: string, data?: any) => log(LogLevel.ERROR, message, data)
};

// Export default logger
export default logger;
