import { GenericConverterProps } from "@/components/tools/GenericConverter";
import {
  handleExcelToPdfFileName,
  handlePdfToWordFileName,
  handlePdfToExcelFileName,
  handlePdfToPowerPointFileName,
  handlePdfToPdfaFileName,
  handleWordToPdfFileName,
  handlePowerPointToPdfFileName,
  handleCompressPdfFileName,
  handleRotatePdfFileName
} from './actions';

export const toolConfigs: Record<string, GenericConverterProps> = {
  "pdf-to-word": {
    title: "How to Convert PDF to Word",
    description: "Convert PDF documents to editable Word files",
    steps: [
      "Upload your PDF file using the uploader below.",
      'Click the "Convert to Word" button to start the conversion.',
      "Download your converted Word document when ready.",
    ],
    acceptedFileTypes: ".pdf,application/pdf",
    maxFileSizeMB: 50,
    convertButtonText: "Convert to Word",
    downloadButtonText: "Download Word Document",
    aboutTitle: "About PDF to Word Conversion",
    aboutDescription:
      "Our PDF to Word converter transforms PDF documents into editable Microsoft Word files while preserving the original formatting, images, and text. This makes it easy to edit and modify content that was previously locked in a PDF format.",
    noteDescription:
      "While our converter strives to maintain the original layout, some complex formatting or special elements might not convert perfectly. For best results, use PDFs with clear text and simple layouts.",
    fileNameHandler: handlePdfToWordFileName,
    conversionType: "file-to-file",
  },
  "pdf-to-powerpoint": {
    title: "How to Convert PDF to PowerPoint",
    description: "Convert PDF to PowerPoint presentations",
    steps: [
      "Upload your PDF file using the uploader below.",
      'Click the "Convert to PowerPoint" button to start the conversion.',
      "Download your converted PowerPoint presentation when ready.",
    ],
    acceptedFileTypes: ".pdf,application/pdf",
    maxFileSizeMB: 50,
    convertButtonText: "Convert to PowerPoint",
    downloadButtonText: "Download PowerPoint Presentation",
    aboutTitle: "About PDF to PowerPoint Conversion",
    aboutDescription:
      "Our PDF to PowerPoint converter transforms PDF documents into editable Microsoft PowerPoint presentations while preserving the original formatting, images, and text. This makes it easy to edit and modify content that was previously locked in a PDF format.",
    noteDescription:
      "While our converter strives to maintain the original layout, some complex formatting or special elements might not convert perfectly. For best results, use PDFs with clear text and simple layouts.",
    fileNameHandler: handlePdfToPowerPointFileName,
    conversionType: "file-to-file",
  },
  "pdf-to-excel": {
    title: "How to Convert PDF to Excel",
    description: "Convert PDF to Excel spreadsheets",
    steps: [
      "Upload your PDF file using the uploader below.",
      'Click the "Convert to Excel" button to start the conversion.',
      "Download your converted Excel spreadsheet when ready.",
    ],
    acceptedFileTypes: ".pdf,application/pdf",
    maxFileSizeMB: 50,
    convertButtonText: "Convert to Excel",
    downloadButtonText: "Download Excel Spreadsheet",
    aboutTitle: "About PDF to Excel Conversion",
    aboutDescription:
      "Our PDF to Excel converter extracts tables and data from PDF documents and converts them into editable Microsoft Excel spreadsheets. This is particularly useful for financial documents, reports, and any PDF containing tabular data.",
    noteDescription:
      "The conversion works best with PDFs that have well-defined tables. Complex layouts or scanned documents may require additional formatting after conversion.",
    fileNameHandler: handlePdfToExcelFileName,
    conversionType: "file-to-file",
  },
  "pdf-to-jpg": {
    title: "How to Convert PDF to JPG",
    description: "Convert PDF pages to JPG images",
    steps: [
      "Upload your PDF file using the uploader below.",
      'Click the "Convert to JPG" button to start the conversion.',
      "Download your converted JPG images as a ZIP archive when ready.",
    ],
    acceptedFileTypes: ".pdf,application/pdf",
    maxFileSizeMB: 50,
    convertButtonText: "Convert to JPG",
    downloadButtonText: "Download JPG Images (ZIP)",
    aboutTitle: "About PDF to JPG Conversion",
    aboutDescription:
      "Our PDF to JPG converter transforms each page of your PDF document into a separate high-quality JPG image. This is useful when you need to use PDF content in image editors, presentations, or websites that don't support PDF embedding.",
    noteDescription:
      "The conversion creates one JPG image per PDF page. For multi-page PDFs, all images are packaged in a ZIP file for convenient downloading. The image quality is set to high resolution by default to preserve details.",
    conversionType: "file-to-files",
  },
  "pdf-to-pdf-a": {
    title: "How to Convert PDF to PDF/A",
    description: "Convert PDF to PDF/A for long-term archiving",
    steps: [
      "Upload your PDF file using the uploader below.",
      'Click the "Convert to PDF/A" button to start the conversion.',
      "Download your converted PDF/A document when ready.",
    ],
    acceptedFileTypes: ".pdf,application/pdf",
    maxFileSizeMB: 50,
    convertButtonText: "Convert to PDF/A",
    downloadButtonText: "Download PDF/A Document",
    aboutTitle: "About PDF to PDF/A Conversion",
    aboutDescription:
      "PDF/A is a specialized version of the PDF format designed for long-term archiving of electronic documents. Our converter transforms standard PDF files into PDF/A-compliant documents, ensuring they will remain accessible and readable for years to come, even as technology changes.",
    noteDescription:
      "The PDF/A standard requires that all fonts be embedded, external content references be removed, and certain features like JavaScript and encryption be disabled. This ensures the document can be viewed exactly as intended regardless of the software or system used to open it in the future.",
    fileNameHandler: handlePdfToPdfaFileName,
    conversionType: "file-to-file",
  },
  "word-to-pdf": {
    title: "How to Convert Word to PDF",
    description: "Convert Word documents to PDF",
    steps: [
      "Upload your Word document using the uploader below.",
      'Click the "Convert to PDF" button to start the conversion.',
      "Download your converted PDF document when ready.",
    ],
    acceptedFileTypes:
      ".doc,.docx,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    maxFileSizeMB: 50,
    convertButtonText: "Convert to PDF",
    downloadButtonText: "Download PDF Document",
    aboutTitle: "About Word to PDF Conversion",
    aboutDescription:
      "Our Word to PDF converter transforms Microsoft Word documents (.doc or .docx) into PDF files while preserving the original formatting, images, tables, and text. This is useful when you need to share documents in a format that can't be easily edited or when you need to ensure your document looks the same on any device.",
    noteDescription:
      "The conversion maintains all formatting including fonts, colors, images, and layout. However, some complex features like macros, animations, or certain embedded objects may not be preserved in the PDF output.",
    fileNameHandler: handleWordToPdfFileName,
    conversionType: "file-to-file",
  },
  "powerpoint-to-pdf": {
    title: "How to Convert PowerPoint to PDF",
    description: "Convert PowerPoint presentations to PDF",
    steps: [
      "Upload your PowerPoint presentation using the uploader below.",
      'Click the "Convert to PDF" button to start the conversion.',
      "Download your converted PDF document when ready.",
    ],
    acceptedFileTypes:
      ".ppt,.pptx,application/vnd.ms-powerpoint,application/vnd.openxmlformats-officedocument.presentationml.presentation",
    maxFileSizeMB: 50,
    convertButtonText: "Convert to PDF",
    downloadButtonText: "Download PDF Document",
    aboutTitle: "About PowerPoint to PDF Conversion",
    aboutDescription:
      "Our PowerPoint to PDF converter transforms Microsoft PowerPoint presentations (.ppt or .pptx) into PDF files while preserving the original formatting, images, animations, and text. This is useful when you need to share presentations in a format that can be viewed on any device without PowerPoint installed.",
    noteDescription:
      "The conversion maintains all slide content including fonts, colors, images, and layout. However, animations, transitions, and some interactive elements will be converted to static images in the PDF output. Each slide in the presentation becomes a separate page in the PDF document.",
    fileNameHandler: handlePowerPointToPdfFileName,
    conversionType: "file-to-file",
  },
  "excel-to-pdf": {
    title: "How to Convert Excel to PDF",
    description: "Convert Excel spreadsheets to PDF",
    steps: [
      "Upload your Excel spreadsheet using the uploader below.",
      'Click the "Convert to PDF" button to start the conversion.',
      "Download your converted PDF document when ready.",
    ],
    acceptedFileTypes:
      ".xls,.xlsx,application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    maxFileSizeMB: 50,
    convertButtonText: "Convert to PDF",
    downloadButtonText: "Download PDF Document",
    aboutTitle: "About Excel to PDF Conversion",
    aboutDescription:
      "Our Excel to PDF converter transforms Microsoft Excel spreadsheets (.xls or .xlsx) into PDF files while preserving the original formatting, tables, charts, and data. This is useful when you need to share financial data or reports in a format that can't be easily edited.",
    noteDescription:
      "The conversion maintains all cell formatting, formulas (as values), charts, and tables. For multi-sheet workbooks, each sheet will be converted to a separate page in the PDF document. Very large spreadsheets may be scaled to fit the PDF page size, which could affect readability of small text.",
    fileNameHandler: handleExcelToPdfFileName,
    conversionType: "file-to-file",
  },
  "jpg-to-pdf": {
    title: "How to Convert JPG to PDF",
    description: "Convert JPG images to PDF",
    steps: [
      "Upload one or more JPG images using the uploader below.",
      'Click the "Convert to PDF" button to start the conversion.',
      "Download your converted PDF document when ready.",
    ],
    acceptedFileTypes: ".jpg,.jpeg,.png,image/jpeg,image/png",
    maxFileSizeMB: 50,
    convertButtonText: "Convert to PDF",
    downloadButtonText: "Download PDF Document",
    aboutTitle: "About JPG to PDF Conversion",
    aboutDescription:
      "Our JPG to PDF converter allows you to combine multiple JPG images into a single PDF document. Each image will be converted to a separate page in the PDF, maintaining the original quality and dimensions. This is useful for creating documentation, reports, or photo albums from your image files.",
    noteDescription:
      "The images will be arranged in the PDF in the order they were uploaded. You can upload JPG, JPEG, and PNG files. The conversion preserves the original image quality, but very large images may be slightly compressed to optimize the PDF file size.",
    multiple: true,
    conversionType: "files-to-file",
  },
  "html-to-pdf": {
    title: "How to Convert HTML to PDF",
    description: "Convert HTML pages to PDF",
    steps: [
      "Enter a URL or paste HTML content using the tabs below.",
      'Click the "Convert to PDF" button to start the conversion.',
      "Download your converted PDF document when ready.",
    ],
    acceptedFileTypes: "",
    convertButtonText: "Convert to PDF",
    downloadButtonText: "Download PDF Document",
    aboutTitle: "About HTML to PDF Conversion",
    aboutDescription:
      "Our HTML to PDF converter transforms web pages or HTML code into PDF documents, preserving the layout, formatting, images, and styling. This is useful for archiving web content, creating documentation, or generating reports from HTML templates.",
    noteDescription:
      "The conversion process captures the current state of the webpage. Dynamic content that requires user interaction or JavaScript execution might not be fully captured. For best results with URL conversion, ensure the website is publicly accessible.",
    conversionType: "url-or-html",
  },
  "merge-pdf": {
    title: "How to Merge PDF Files",
    description: "Combine multiple PDFs into one document",
    steps: [
      "Upload two or more PDF files using the uploader below.",
      "Arrange the files in the desired order using the up/down arrows.",
      'Click the "Merge PDFs" button to combine the files.',
      "Download your merged PDF document when ready.",
    ],
    acceptedFileTypes: ".pdf,application/pdf",
    maxFileSizeMB: 50,
    convertButtonText: "Merge PDFs",
    downloadButtonText: "Download Merged PDF",
    aboutTitle: "About PDF Merging",
    aboutDescription:
      "Our PDF merger combines multiple PDF files into a single document while preserving the quality and formatting of each page. This is useful for creating comprehensive reports, combining related documents, or organizing scattered information into a single file.",
    noteDescription:
      "The PDFs will be merged in the order shown in the list above. You can rearrange them using the up and down arrows. The merged document will maintain all text, images, and formatting from the original files, but some interactive elements like form fields or JavaScript may not function in the merged document.",
    multiple: true,
    conversionType: "files-to-file",
  },
  "split-pdf": {
    title: "How to Split PDF Files",
    description: "Split a PDF into multiple documents",
    steps: [
      "Upload your PDF file using the uploader below.",
      "Choose your preferred splitting method.",
      'Click the "Split PDF" button to process the file.',
      "Download your split PDF files as a ZIP archive when ready.",
    ],
    acceptedFileTypes: ".pdf,application/pdf",
    maxFileSizeMB: 50,
    convertButtonText: "Split PDF",
    downloadButtonText: "Download Split PDF Files (ZIP)",
    aboutTitle: "About PDF Splitting",
    aboutDescription:
      "Our PDF splitter allows you to divide a PDF document into multiple smaller files. You can split a PDF into individual pages, extract specific pages or page ranges, or split the document at designated points. This is useful for extracting specific sections from large reports, sharing only relevant pages, or breaking down large documents into manageable parts.",
    noteDescription:
      "The split PDF files will maintain the same quality and formatting as the original document. All split files are packaged in a ZIP archive for convenient downloading. For large PDFs, the splitting process may take longer to complete.",
    conversionType: "file-to-files",
  },
  "compress-pdf": {
    title: "How to Compress PDF Files",
    description: "Reduce PDF file size while maintaining quality",
    steps: [
      "Upload your PDF file using the uploader below.",
      "Select your desired compression level.",
      'Click the "Compress PDF" button to start the process.',
      "Download your compressed PDF document when ready.",
    ],
    acceptedFileTypes: ".pdf,application/pdf",
    maxFileSizeMB: 50,
    convertButtonText: "Compress PDF",
    downloadButtonText: "Download Compressed PDF",
    aboutTitle: "About PDF Compression",
    aboutDescription:
      "Our PDF compressor reduces the file size of your PDF documents while maintaining readability and quality. This is useful for sharing documents via email, uploading to websites with file size limits, or saving storage space. The compression process optimizes images, removes redundant information, and applies efficient compression algorithms.",
    noteDescription:
      "The compression level affects both the file size and the quality of the output. Higher compression results in smaller files but may reduce image quality. For documents with many images, the high compression setting may noticeably affect image clarity. For text-heavy documents, even high compression typically maintains good readability.",
    fileNameHandler: handleCompressPdfFileName,
    conversionType: "file-to-file",
  },
  "rotate-pdf": {
    title: "How to Rotate PDF Pages",
    description: "Rotate PDF pages to the correct orientation",
    steps: [
      "Upload your PDF file using the uploader below.",
      "Choose which pages to rotate and the rotation angle.",
      'Click the "Rotate PDF" button to process the file.',
      "Download your rotated PDF document when ready.",
    ],
    acceptedFileTypes: ".pdf,application/pdf",
    maxFileSizeMB: 50,
    convertButtonText: "Rotate PDF",
    downloadButtonText: "Download Rotated PDF",
    aboutTitle: "About PDF Rotation",
    aboutDescription:
      "Our PDF rotation tool allows you to correct the orientation of pages in your PDF documents. You can rotate all pages or select specific pages to rotate at different angles. This is useful for fixing scanned documents or preparing PDFs for printing or viewing.",
    noteDescription:
      "The rotation is applied permanently to the PDF file. The quality of the document remains unchanged during the rotation process. You can rotate pages by 90°, 180°, or 270° as needed.",
    fileNameHandler: handleRotatePdfFileName,
    conversionType: "file-to-file",
  },
};
