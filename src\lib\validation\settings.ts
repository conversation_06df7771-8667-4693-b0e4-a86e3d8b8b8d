import { z } from "zod";

export const SettingsSchema = z.object({
  siteName: z.string().min(3, "Site name must be at least 3 characters"),
  siteDescription: z.string().min(10, "Site description must be at least 10 characters"),
  siteUrl: z.string().url("Must be a valid URL"),

  // SEO Metadata
  metaTitle: z.string().min(5, "Meta title must be at least 5 characters"),
  metaDescription: z.string().min(10, "Meta description must be at least 10 characters").max(160, "Meta description should not exceed 160 characters"),
  metaKeywords: z.array(z.string()).optional(),

  // Social Media & OG Tags
  ogTitle: z.string().optional(),
  ogDescription: z.string().optional(),
  ogImage: z.string().url("Must be a valid URL").optional(),
  twitterHandle: z.string().optional(),

  // Logo & Favicon
  logoUrl: z.string().url("Must be a valid URL").optional(),
  faviconUrl: z.string().url("Must be a valid URL").optional(),

  // Analytics & Tracking
  googleAnalyticsId: z.string().optional(),
  facebookPixelId: z.string().optional(),

  // Maintenance Mode
  maintenanceMode: z.boolean(),
  maintenanceMessage: z.string().optional(),
});