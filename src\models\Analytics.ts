import mongoose, { Schema, model, models, Types } from "mongoose";

// Page View Analytics
export interface IPageView {
  _id?: Types.ObjectId;
  path: string;
  userId?: Types.ObjectId;
  sessionId: string;
  userAgent?: string;
  referrer?: string;
  timestamp: Date;
  duration?: number;
  ipAddress?: string;
}

const pageViewSchema = new Schema<IPageView>({
  path: {
    type: String,
    required: true,
    index: true,
  },
  userId: {
    type: Schema.Types.ObjectId,
    ref: "User",
    index: true,
  },
  sessionId: {
    type: String,
    required: true,
    index: true,
  },
  userAgent: String,
  referrer: String,
  timestamp: {
    type: Date,
    default: Date.now,
    index: true,
  },
  duration: Number,
  ipAddress: String,
});

// User Activity
export interface IUserActivity {
  _id?: Types.ObjectId;
  userId: Types.ObjectId;
  action: string;
  entityType?: string;
  entityId?: string;
  details?: Record<string, any>;
  timestamp: Date;
}

const userActivitySchema = new Schema<IUserActivity>({
  userId: {
    type: Schema.Types.ObjectId,
    ref: "User",
    required: true,
    index: true,
  },
  action: {
    type: String,
    required: true,
    index: true,
  },
  entityType: {
    type: String,
    index: true,
  },
  entityId: {
    type: String,
    index: true,
  },
  details: Schema.Types.Mixed,
  timestamp: {
    type: Date,
    default: Date.now,
    index: true,
  },
});

// Tool Usage
export interface IToolUsage {
  _id?: Types.ObjectId;
  toolId: string;
  toolName: string;
  userId?: Types.ObjectId;
  sessionId: string;
  timestamp: Date;
  duration?: number;
  status: "started" | "completed" | "failed";
  details?: Record<string, any>;
}

const toolUsageSchema = new Schema<IToolUsage>({
  toolId: {
    type: String,
    required: true,
    index: true,
  },
  toolName: {
    type: String,
    required: true,
    index: true,
  },
  userId: {
    type: Schema.Types.ObjectId,
    ref: "User",
    index: true,
  },
  sessionId: {
    type: String,
    required: true,
    index: true,
  },
  timestamp: {
    type: Date,
    default: Date.now,
    index: true,
  },
  duration: Number,
  status: {
    type: String,
    enum: ["started", "completed", "failed"],
    default: "started",
    index: true,
  },
  details: Schema.Types.Mixed,
});

// Create models
export const PageView = mongoose.models.PageView || mongoose.model<IPageView>("PageView", pageViewSchema);
export const UserActivity = mongoose.models.UserActivity || mongoose.model<IUserActivity>("UserActivity", userActivitySchema);
export const ToolUsage = mongoose.models.ToolUsage || mongoose.model<IToolUsage>("ToolUsage", toolUsageSchema);
