import mongoose, { Schema, model, models, Types } from "mongoose";
import { IBlogPost } from "./BlogPost";
import { IUser } from "./User";

export interface IComment {
  _id?: Types.ObjectId;
  content: string;
  postId: Types.ObjectId | IBlogPost;
  userId?: Types.ObjectId | IUser;
  authorName: string;
  authorEmail: string;
  authorWebsite?: string;
  parentId?: Types.ObjectId | IComment;
  status: "pending" | "approved" | "spam" | "trash";
  isAdmin: boolean;
  role?: string;
  createdAt: Date;
  updatedAt: Date;
  likes: number;
  replies?: IComment[];
}

const commentSchema = new Schema<IComment>(
  {
    content: {
      type: String,
      required: [true, "Comment content is required"],
      trim: true,
    },
    postId: {
      type: Schema.Types.ObjectId,
      ref: "BlogPost",
      required: [true, "Blog post ID is required"],
      index: true,
    },
    userId: {
      type: Schema.Types.ObjectId,
      ref: "User",
      index: true,
    },
    authorName: {
      type: String,
      required: [true, "Author name is required"],
      trim: true,
    },
    authorEmail: {
      type: String,
      required: [true, "Author email is required"],
      trim: true,
      lowercase: true,
    },
    authorWebsite: {
      type: String,
      trim: true,
    },
    parentId: {
      type: Schema.Types.ObjectId,
      ref: "Comment",
      index: true,
    },
    status: {
      type: String,
      enum: ["pending", "approved", "spam", "trash"],
      default: "pending",
      index: true,
    },
    isAdmin: {
      type: Boolean,
      default: false,
    },
    role: {
      type: String,
      default: 'user',
    },
    likes: {
      type: Number,
      default: 0,
    },
  },
  { timestamps: true }
);

// Create text index for search
commentSchema.index(
  { content: "text", authorName: "text", authorEmail: "text" },
  { weights: { content: 3, authorName: 2, authorEmail: 1 } }
);

// Virtual for getting replies
commentSchema.virtual("replies", {
  ref: "Comment",
  localField: "_id",
  foreignField: "parentId",
});

const Comment = mongoose.models.Comment || mongoose.model<IComment>("Comment", commentSchema);

export default Comment;
