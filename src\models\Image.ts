import mongoose, { Schema, Document, Types } from 'mongoose';

export interface IImage extends Document {
  _id: Types.ObjectId;
  url: string;
  publicId?: string; // For Cloudinary images
  fileName?: string; // For local images
  uploadedBy: Types.ObjectId;
  uploadedAt: Date;
  location: 'local' | 'cloud';
  type: 'file' | 'url';
  originalUrl?: string; // For URL uploads
  width?: number;
  height?: number;
  format?: string;
  bytes?: number;
  alt?: string;
  title?: string;
  tags?: string[];
  folder?: string;
}

const ImageSchema = new Schema<IImage>({
  url: {
    type: String,
    required: [true, 'Image URL is required'],
    trim: true,
  },
  publicId: {
    type: String,
    trim: true,
  },
  fileName: {
    type: String,
    trim: true,
  },
  uploadedBy: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'Uploader ID is required'],
  },
  uploadedAt: {
    type: Date,
    default: Date.now,
  },
  location: {
    type: String,
    enum: ['local', 'cloud'],
    required: [true, 'Upload location is required'],
  },
  type: {
    type: String,
    enum: ['file', 'url'],
    required: [true, 'Upload type is required'],
  },
  originalUrl: {
    type: String,
    trim: true,
  },
  width: {
    type: Number,
    min: 0,
  },
  height: {
    type: Number,
    min: 0,
  },
  format: {
    type: String,
    trim: true,
  },
  bytes: {
    type: Number,
    min: 0,
  },
  alt: {
    type: String,
    trim: true,
    maxlength: [200, 'Alt text cannot exceed 200 characters'],
  },
  title: {
    type: String,
    trim: true,
    maxlength: [200, 'Title cannot exceed 200 characters'],
  },
  tags: [{
    type: String,
    trim: true,
  }],
  folder: {
    type: String,
    trim: true,
  },
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true },
});

// Indexes for better query performance
ImageSchema.index({ uploadedBy: 1, uploadedAt: -1 });
ImageSchema.index({ location: 1, type: 1 });
ImageSchema.index({ tags: 1 });
ImageSchema.index({ folder: 1 });

// Virtual for formatted upload date
ImageSchema.virtual('formattedUploadDate').get(function() {
  return this.uploadedAt.toLocaleDateString();
});

// Static method to find images by user
ImageSchema.statics.findByUser = function(userId: string) {
  return this.find({ uploadedBy: userId }).sort({ uploadedAt: -1 });
};

// Static method to find images by location
ImageSchema.statics.findByLocation = function(location: 'local' | 'cloud') {
  return this.find({ location }).sort({ uploadedAt: -1 });
};

// Static method to find recent images
ImageSchema.statics.findRecent = function(limit: number = 20) {
  return this.find().sort({ uploadedAt: -1 }).limit(limit);
};

// Pre-save middleware to ensure required fields based on location
ImageSchema.pre('save', function(next) {
  if (this.location === 'local' && !this.fileName) {
    // For local uploads, try to extract fileName from URL
    const urlParts = this.url.split('/');
    this.fileName = urlParts[urlParts.length - 1];
  }

  next();
});

// Ensure model is only compiled once
const Image = mongoose.models.Image || mongoose.model<IImage>('Image', ImageSchema);

export default Image;
