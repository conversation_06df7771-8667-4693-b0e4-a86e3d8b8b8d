import mongoose, { Schema, model, models, Types } from "mongoose";

export interface ISiteSettings {
  _id?: Types.ObjectId;
  siteName: string;
  siteDescription: string;
  siteUrl: string;
  
  // SEO Metadata
  metaTitle: string;
  metaDescription: string;
  metaKeywords: string[];
  
  // Social Media & OG Tags
  ogTitle: string;
  ogDescription: string;
  ogImage: string;
  twitterHandle: string;
  
  // Logo & Favicon
  logoUrl: string;
  faviconUrl: string;
  
  // Analytics & Tracking
  googleAnalyticsId?: string;
  facebookPixelId?: string;
  
  // Maintenance Mode
  maintenanceMode: boolean;
  maintenanceMessage?: string;
  
  // Updated timestamp
  updatedAt: Date;
}

const siteSettingsSchema = new Schema<ISiteSettings>({
  siteName: {
    type: String,
    required: [true, "Site name is required"],
    trim: true,
  },
  siteDescription: {
    type: String,
    required: [true, "Site description is required"],
    trim: true,
  },
  siteUrl: {
    type: String,
    required: [true, "Site URL is required"],
    trim: true,
  },
  
  // SEO Metadata
  metaTitle: {
    type: String,
    required: [true, "Meta title is required"],
    trim: true,
  },
  metaDescription: {
    type: String,
    required: [true, "Meta description is required"],
    trim: true,
  },
  metaKeywords: [{
    type: String,
    trim: true,
  }],
  
  // Social Media & OG Tags
  ogTitle: {
    type: String,
    trim: true,
  },
  ogDescription: {
    type: String,
    trim: true,
  },
  ogImage: {
    type: String,
    trim: true,
  },
  twitterHandle: {
    type: String,
    trim: true,
  },
  
  // Logo & Favicon
  logoUrl: {
    type: String,
    trim: true,
  },
  faviconUrl: {
    type: String,
    trim: true,
  },
  
  // Analytics & Tracking
  googleAnalyticsId: {
    type: String,
    trim: true,
  },
  facebookPixelId: {
    type: String,
    trim: true,
  },
  
  // Maintenance Mode
  maintenanceMode: {
    type: Boolean,
    default: false,
  },
  maintenanceMessage: {
    type: String,
    trim: true,
  },
}, { timestamps: true });

// There should only be one site settings document
siteSettingsSchema.statics.findOneOrCreate = async function(query: any, doc: any) {
  const result = await this.findOne(query);
  return result || this.create(doc);
};

const SiteSettings = mongoose.models.SiteSettings || mongoose.model<ISiteSettings>("SiteSettings", siteSettingsSchema);

export default SiteSettings;
