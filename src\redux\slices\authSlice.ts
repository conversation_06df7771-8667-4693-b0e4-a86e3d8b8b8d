import { createSlice, PayloadAction, createAsyncThunk } from '@reduxjs/toolkit';

// Simple user interface for Redux state
interface User {
  id: string;
  name?: string | null;
  email?: string | null;
  role: "admin" | "user";
}

// Enhanced auth state with additional properties needed by admin components
interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isAdmin: boolean;
  adminVerified: boolean;
  loading: boolean;
  error: string | null;
  lastAuthCheck: number | null;
}

const initialState: AuthState = {
  user: null,
  isAuthenticated: false,
  isAdmin: false,
  adminVerified: false,
  loading: false,
  error: null,
  lastAuthCheck: null,
};

// Async thunk to check auth status if needed
export const checkAuthIfNeeded = createAsyncThunk(
  'auth/checkAuthIfNeeded',
  async (_, { getState }) => {
    const state = getState() as { auth: AuthState };
    const now = Date.now();
    const fiveMinutes = 5 * 60 * 1000;

    // Skip if we checked recently
    if (state.auth.lastAuthCheck && (now - state.auth.lastAuthCheck) < fiveMinutes) {
      return state.auth.user;
    }

    try {
      const response = await fetch('/api/auth/me', {
        credentials: 'include',
        cache: 'no-store',
      });

      if (response.ok) {
        const data = await response.json();
        return data.user;
      }
      return null;
    } catch (error) {
      console.error('Auth check failed:', error);
      return null;
    }
  }
);

// Async thunk to verify admin status
export const verifyAdminStatus = createAsyncThunk(
  'auth/verifyAdminStatus',
  async (_, { getState }) => {
    const state = getState() as { auth: AuthState };

    if (!state.auth.isAuthenticated || !state.auth.user) {
      return false;
    }

    // Check if user role is admin
    return state.auth.user.role === 'admin';
  }
);

// Enhanced slice with additional functionality
const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    // Set user from NextAuth session
    setUser: (state, action: PayloadAction<User>) => {
      state.user = action.payload;
      state.isAuthenticated = true;
      state.isAdmin = action.payload.role === 'admin';
      state.adminVerified = true;
      state.lastAuthCheck = Date.now();
      state.error = null;
    },
    // Clear user on logout
    clearUser: (state) => {
      state.user = null;
      state.isAuthenticated = false;
      state.isAdmin = false;
      state.adminVerified = false;
      state.lastAuthCheck = null;
      state.error = null;
    },
    // Set loading state
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    // Set error
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
    // Reset admin verification (useful for forcing re-check)
    resetAdminVerification: (state) => {
      state.adminVerified = false;
    },
  },
  extraReducers: (builder) => {
    builder
      // Handle checkAuthIfNeeded
      .addCase(checkAuthIfNeeded.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(checkAuthIfNeeded.fulfilled, (state, action) => {
        state.loading = false;
        state.lastAuthCheck = Date.now();
        if (action.payload) {
          state.user = action.payload;
          state.isAuthenticated = true;
          state.isAdmin = action.payload.role === 'admin';
        } else {
          state.user = null;
          state.isAuthenticated = false;
          state.isAdmin = false;
        }
      })
      .addCase(checkAuthIfNeeded.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Auth check failed';
      })
      // Handle verifyAdminStatus
      .addCase(verifyAdminStatus.pending, (state) => {
        state.loading = true;
      })
      .addCase(verifyAdminStatus.fulfilled, (state, action) => {
        state.loading = false;
        state.adminVerified = true;
        state.isAdmin = action.payload;
      })
      .addCase(verifyAdminStatus.rejected, (state, action) => {
        state.loading = false;
        state.adminVerified = true;
        state.isAdmin = false;
        state.error = action.error.message || 'Admin verification failed';
      });
  },
});

export const {
  setUser,
  clearUser,
  setLoading,
  setError,
  resetAdminVerification
} = authSlice.actions;

export default authSlice.reducer;

