import { createSlice, PayloadAction } from '@reduxjs/toolkit';

export type Theme = 'light' | 'dark';

interface ThemeState {
  theme: Theme;
}

// Initial state
const initialState: ThemeState = {
  theme: 'light',
};

// Create theme slice
const themeSlice = createSlice({
  name: 'theme',
  initialState,
  reducers: {
    setTheme: (state, action: PayloadAction<Theme>) => {
      state.theme = action.payload;
    },
    toggleTheme: (state) => {
      state.theme = state.theme === 'light' ? 'dark' : 'light';
    },
  },
});

// Export actions and reducer
export const { setTheme, toggleTheme } = themeSlice.actions;
export default themeSlice.reducer;

// Selector functions
export const selectTheme = (state: { theme: ThemeState }) => state.theme.theme;
