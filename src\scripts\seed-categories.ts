import connectToDatabase from "@/lib/db";
import Category from "@/models/Category";

const defaultCategories = [
  {
    name: "Technology",
    description: "Latest technology trends, software development, and digital innovations"
  },
  {
    name: "Food",
    description: "Recipes, cooking tips, restaurant reviews, and culinary adventures"
  },
  {
    name: "Automotive",
    description: "Car reviews, automotive technology, and transportation trends"
  },
  {
    name: "Design",
    description: "UI/UX design, graphic design, and creative inspiration"
  },
  {
    name: "AI",
    description: "Artificial intelligence, machine learning, and automation"
  },
  {
    name: "Web",
    description: "Web development, frameworks, and online technologies"
  },
  {
    name: "General",
    description: "General topics and miscellaneous content"
  }
];

export async function seedCategories() {
  try {
    await connectToDatabase();
    
    console.log("Starting category seeding...");
    
    // Check if categories already exist
    const existingCategories = await Category.find();
    
    if (existingCategories.length > 0) {
      console.log(`Found ${existingCategories.length} existing categories. Skipping seed.`);
      return existingCategories;
    }
    
    // Create default categories
    const createdCategories = [];
    
    for (const categoryData of defaultCategories) {
      try {
        const category = await Category.create(categoryData);
        createdCategories.push(category);
        console.log(`Created category: ${category.name}`);
      } catch (error: any) {
        if (error.code === 11000) {
          console.log(`Category ${categoryData.name} already exists, skipping...`);
          const existing = await Category.findOne({ name: categoryData.name });
          if (existing) {
            createdCategories.push(existing);
          }
        } else {
          console.error(`Error creating category ${categoryData.name}:`, error);
        }
      }
    }
    
    console.log(`Successfully seeded ${createdCategories.length} categories`);
    return createdCategories;
    
  } catch (error) {
    console.error("Error seeding categories:", error);
    throw error;
  }
}

// Run if called directly
if (require.main === module) {
  seedCategories()
    .then(() => {
      console.log("Category seeding completed");
      process.exit(0);
    })
    .catch((error) => {
      console.error("Category seeding failed:", error);
      process.exit(1);
    });
}
