import { blogPosts } from "@/data/blog-posts";

export interface BlogPost {
  id: string;
  title: string;
  date: string;
  author: string;
  category: string;
  description?: string;
  image: string;
  content: string;
  tags?: string[];
  status?: "draft" | "published" | "scheduled" | "archived";
  visibility?: "public" | "private" | "draft";
  featuredImage?: string;
  imageCredit?: string;
  slug?: string;
}

export interface Category {
  name: string;
  count: number;
}

export interface Tag {
  name: string;
  count: number;
}

export interface BlogPostResponse {
  success: boolean;
  data: BlogPost[];
  pagination?: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
  error?: string;
}

// Convert the blog posts object to an array with IDs
export const getAllPosts = (): BlogPost[] => {
  return Object.entries(blogPosts).map(([id, post]) => ({
    id,
    title: post.title,
    date: post.date,
    author: post.author,
    category: post.category,
    description: post.description,
    image: post.image,
    content: post.content,
    // Extract tags from content or add manually
    tags: extractTagsFromPost(post)
  }));
};

// Get recent posts
export const getRecentPosts = (count: number = 3): { title: string; slug: string }[] => {
  const posts = getAllPosts();

  // Sort by date (newest first)
  const sortedPosts = posts.sort((a, b) => {
    return new Date(b.date).getTime() - new Date(a.date).getTime();
  });

  return sortedPosts.slice(0, count).map(post => ({
    title: post.title,
    slug: post.id
  }));
};

// Get all categories with post counts
export const getCategories = (): Category[] => {
  const posts = getAllPosts();
  const categoryMap = new Map<string, number>();

  posts.forEach(post => {
    const category = post.category;
    categoryMap.set(category, (categoryMap.get(category) || 0) + 1);
  });

  return Array.from(categoryMap.entries()).map(([name, count]) => ({
    name,
    count
  }));
};

// Get all tags with post counts
export const getTags = (): Tag[] => {
  const posts = getAllPosts();
  const tagMap = new Map<string, number>();

  posts.forEach(post => {
    const tags = post.tags || [];
    tags.forEach(tag => {
      tagMap.set(tag, (tagMap.get(tag) || 0) + 1);
    });
  });

  return Array.from(tagMap.entries()).map(([name, count]) => ({
    name,
    count
  }));
};

// Search posts by query
export const searchPosts = (query: string): BlogPost[] => {
  if (!query) return [];

  const posts = getAllPosts();
  const lowerQuery = query.toLowerCase();

  return posts.filter(post =>
    post.title.toLowerCase().includes(lowerQuery) ||
    post.content.toLowerCase().includes(lowerQuery) ||
    post.description?.toLowerCase().includes(lowerQuery) ||
    post.category.toLowerCase().includes(lowerQuery) ||
    post.author.toLowerCase().includes(lowerQuery) ||
    (post.tags || []).some(tag => tag.toLowerCase().includes(lowerQuery))
  );
};

// Helper function to extract tags from post content or add manually
function extractTagsFromPost(post: any): string[] {
  // For now, we'll manually assign tags based on categories and content
  const tags: string[] = [];

  // Add category as a tag
  tags.push(post.category);

  // Add some common tags based on content
  if (post.content.toLowerCase().includes('pdf')) tags.push('PDF');
  if (post.content.toLowerCase().includes('word')) tags.push('Word');
  if (post.content.toLowerCase().includes('convert')) tags.push('Conversion');
  if (post.content.toLowerCase().includes('compress')) tags.push('Compression');
  if (post.content.toLowerCase().includes('security')) tags.push('Security');
  if (post.content.toLowerCase().includes('password')) tags.push('Password Protection');

  // Return unique tags
  return Array.from(new Set(tags));
}

// Enhanced API functions for database integration
export const fetchBlogPosts = async (params?: {
  page?: number;
  limit?: number;
  status?: string;
  search?: string;
  category?: string;
}): Promise<BlogPostResponse> => {
  try {
    const searchParams = new URLSearchParams();

    if (params?.page) searchParams.set('page', params.page.toString());
    if (params?.limit) searchParams.set('limit', params.limit.toString());
    if (params?.status) searchParams.set('status', params.status);
    if (params?.search) searchParams.set('search', params.search);
    if (params?.category) searchParams.set('category', params.category);

    // Add admin=false to get only published posts for public view
    if (params?.status === 'published') {
      searchParams.set('admin', 'false');
    }

    const response = await fetch(`/api/blog?${searchParams.toString()}`);

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();

    // Transform the response to match expected format
    return {
      success: true,
      data: data.posts || [],
      pagination: data.pagination ? {
        total: data.pagination.total,
        page: data.pagination.page,
        limit: data.pagination.limit,
        totalPages: data.pagination.pages
      } : undefined
    };
  } catch (error) {
    console.error('Error fetching blog posts:', error);
    return {
      success: false,
      data: [],
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
};

export const fetchRecentPosts = async (limit: number = 3): Promise<BlogPost[]> => {
  try {
    const response = await fetch(`/api/blog/recent?limit=${limit}`);

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return data.success ? data.data : [];
  } catch (error) {
    console.error('Error fetching recent posts:', error);
    return [];
  }
};

export const createBlogPost = async (postData: Partial<BlogPost>): Promise<{ success: boolean; data?: any; error?: string }> => {
  try {
    const response = await fetch('/api/blog', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(postData),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return { success: true, data };
  } catch (error) {
    console.error('Error creating blog post:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
};

export const updateBlogPost = async (id: string, postData: Partial<BlogPost>): Promise<{ success: boolean; data?: any; error?: string }> => {
  try {
    const response = await fetch(`/api/blog/${id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(postData),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return { success: true, data };
  } catch (error) {
    console.error('Error updating blog post:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
};

export const deleteBlogPost = async (id: string): Promise<{ success: boolean; error?: string }> => {
  try {
    const response = await fetch(`/api/blog/${id}`, {
      method: 'DELETE',
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
    }

    return { success: true };
  } catch (error) {
    console.error('Error deleting blog post:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
};
