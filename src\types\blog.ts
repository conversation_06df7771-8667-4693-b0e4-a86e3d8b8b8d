// types/blog.ts
export interface BlogPost {
  _id: string;
  title: string;
  content: string;
  slug: string;
  status: 'draft' | 'published' | 'scheduled' | 'archived';
  authorId: string;
  scheduledAt: Date; // Add this
  createdAt: Date;
  updatedAt: Date;
}

export interface BlogSearchParams {
  page?: string;
  limit?: string;
  status?: string;
  search?: string;
}

export interface BlogPagination {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
}