// User type definition
export interface User {
  id: string;
  name: string;
  email: string;
  role: string;
  image?: string | null;
}

// JWT Payload type
export interface JWTPayload {
  userId: string;
  email: string;
  name: string;
  role: string;
  exp: number;
}

// Auth state type
export interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  lastAuthCheck: number | null;
}

// Tool type
export interface Tool {
  id: string;
  title: string;
  description: string;
  icon: string;
  category: 'pdf' | 'office' | 'image';
  inputFormat?: string;
  outputFormat?: string;
  componentName: string;
}
