// // types/tools.ts
// export type Tool = {
//     _id: string;
//     name: string;
//     icon: React.ReactNode;
//     path: string;
//     description: string;
//     color: string;
//     slug: string;
//     category: string;
//     createdAt: Date;
//     updatedAt: Date;
//   };

  
// types/tool.ts (should match your export statement)
export type Tool = {
    _id: string;
    name: string;
    icon: React.ReactNode;
    path: string;
    description: string;
    color: string;
    slug: string;
    category: string;
    createdAt: Date;
    updatedAt: Date;
    // Consider adding an ID alias if needed
    id?: string; // Add this if you want both _id and id
  };
  