import { cardVariants } from "@/components/New-UI/Card";
import { inputVariants } from "@/components/New-UI/Input";
import { toastVariants } from "@/components/New-UI/Toast";
import { buttonVariants } from "@/components/ui/button";
import { VariantProps } from "class-variance-authority";

// Button types
export type ButtonVariantProps = VariantProps<typeof buttonVariants>;

// Input types
export type InputVariantProps = VariantProps<typeof inputVariants>;

// Card types
export type CardVariantProps = VariantProps<typeof cardVariants>;

// Toast types
export type ToastVariantProps = VariantProps<typeof toastVariants>;
export type ToastProps = {
  id: string;
  title?: string;
  description?: string;
  action?: React.ReactNode;
  variant?: "default" | "destructive" | "success";
  duration?: number;
  category: string;
  createdAt: Date;
  updatedAt: Date;
  slug:string;
};

// Form types
export type FormStatus = "idle" | "loading" | "success" | "error";

// Table types
export interface TableProps extends React.HTMLAttributes<HTMLTableElement> {}
export interface TableHeaderProps
  extends React.HTMLAttributes<HTMLTableSectionElement> {}
export interface TableBodyProps
  extends React.HTMLAttributes<HTMLTableSectionElement> {}
export interface TableFooterProps
  extends React.HTMLAttributes<HTMLTableSectionElement> {}
export interface TableRowProps
  extends React.HTMLAttributes<HTMLTableRowElement> {}
export interface TableHeadProps
  extends React.ThHTMLAttributes<HTMLTableCellElement> {}
export interface TableCellProps
  extends React.TdHTMLAttributes<HTMLTableCellElement> {}

// Sidebar types
export interface SidebarProps extends React.HTMLAttributes<HTMLDivElement> {
  isOpen?: boolean;
  onClose?: () => void;
}
