// // types/user.ts
// export type User = {
//   id: string;
//   name?: string | null;
//   email?: string | null;
//   image?: string | null;
//   role: "user" | "admin";
//   createdAt: Date;
//   updatedAt: Date;
// };

// types/user.ts
export interface User {
  id: string;
  name: string;
  email: string;
  role: "admin" | "user";
  isProtected?: boolean;
  image?: string | null;
  createdAt?: string;
  updatedAt?: string;
}
