{"compilerOptions": {"target": "es5", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@components/*": ["./src/components/*"], "@styles/*": ["./src/styles/*"]}, "plugins": [{"name": "next"}], "typeRoots": ["./node_modules/@types", "./src/types"]}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", "postcss.config.js", ".next/types/**/*.ts", "src/**/*.css.d.ts", "src/types/**/*.d.ts"], "exclude": ["node_modules"]}