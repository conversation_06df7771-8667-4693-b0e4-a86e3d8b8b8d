'use client';

import { motion } from 'framer-motion';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { TrendingUp, FileText, Image, Globe, Zap, ArrowRight, ChevronLeft, ChevronRight } from 'lucide-react';
import Link from 'next/link';
import { useTheme } from '@/hooks/useTheme';
import { useState, useEffect } from 'react';

export function TrendingTopics() {
  const { theme } = useTheme();
  const isDark = theme === 'dark';
  const [currentIndex, setCurrentIndex] = useState(0);

  // Trending topics data
  const trendingTopics = [
    {
      id: 'pdf-tools',
      name: 'PDF Tools',
      icon: <FileText className="h-5 w-5" />,
      count: 24,
      color: isDark ? 'from-blue-500/20 to-blue-600/20 border-blue-500/30' : 'from-blue-50 to-blue-100 border-blue-200',
      textColor: isDark ? 'text-blue-400' : 'text-blue-600',
      description: 'Convert, merge, and edit PDFs'
    },
    {
      id: 'office-tools',
      name: 'Office Tools',
      icon: <FileText className="h-5 w-5" />,
      count: 18,
      color: isDark ? 'from-purple-500/20 to-purple-600/20 border-purple-500/30' : 'from-purple-50 to-purple-100 border-purple-200',
      textColor: isDark ? 'text-purple-400' : 'text-purple-600',
      description: 'Word, Excel, PowerPoint tools'
    },
    {
      id: 'image-tools',
      name: 'Image Tools',
      icon: <Image className="h-5 w-5" />,
      count: 15,
      color: isDark ? 'from-green-500/20 to-green-600/20 border-green-500/30' : 'from-green-50 to-green-100 border-green-200',
      textColor: isDark ? 'text-green-400' : 'text-green-600',
      description: 'Image conversion and editing'
    },
    {
      id: 'web-tools',
      name: 'Web Tools',
      icon: <Globe className="h-5 w-5" />,
      count: 12,
      color: isDark ? 'from-orange-500/20 to-orange-600/20 border-orange-500/30' : 'from-orange-50 to-orange-100 border-orange-200',
      textColor: isDark ? 'text-orange-400' : 'text-orange-600',
      description: 'HTML and web development'
    },
    {
      id: 'ai-tools',
      name: 'AI Tools',
      icon: <Zap className="h-5 w-5" />,
      count: 9,
      color: isDark ? 'from-violet-500/20 to-violet-600/20 border-violet-500/30' : 'from-violet-50 to-violet-100 border-violet-200',
      textColor: isDark ? 'text-violet-400' : 'text-violet-600',
      description: 'AI-powered productivity tools'
    },
    {
      id: 'productivity',
      name: 'Productivity',
      icon: <TrendingUp className="h-5 w-5" />,
      count: 21,
      color: isDark ? 'from-cyan-500/20 to-cyan-600/20 border-cyan-500/30' : 'from-cyan-50 to-cyan-100 border-cyan-200',
      textColor: isDark ? 'text-cyan-400' : 'text-cyan-600',
      description: 'Boost your workflow efficiency'
    }
  ];

  // Auto-scroll functionality
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentIndex((prev) => (prev + 1) % Math.max(1, trendingTopics.length - 2));
    }, 4000);

    return () => clearInterval(interval);
  }, [trendingTopics.length]);

  const nextSlide = () => {
    setCurrentIndex((prev) => (prev + 1) % Math.max(1, trendingTopics.length - 2));
  };

  const prevSlide = () => {
    setCurrentIndex((prev) => (prev - 1 + Math.max(1, trendingTopics.length - 2)) % Math.max(1, trendingTopics.length - 2));
  };

  const visibleTopics = trendingTopics.slice(currentIndex, currentIndex + 3);
  if (visibleTopics.length < 3) {
    visibleTopics.push(...trendingTopics.slice(0, 3 - visibleTopics.length));
  }

  return (
    <section className={`py-16 ${isDark ? 'bg-black/20' : 'bg-white'}`}>
      <div className="container mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-12"
        >
          <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-primary/10 text-primary text-sm font-medium mb-4">
            <TrendingUp className="h-4 w-4" />
            Trending Topics
          </div>
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            📈 Trending Topics Slider
          </h2>
          <p className="text-muted-foreground text-lg max-w-2xl mx-auto">
            Explore the most popular categories and discover what's trending in our community
          </p>
        </motion.div>

        {/* Slider Container */}
        <div className="relative">
          {/* Navigation Buttons */}
          <Button
            variant="outline"
            size="icon"
            onClick={prevSlide}
            className={`
              absolute left-0 top-1/2 -translate-y-1/2 z-10 rounded-full w-12 h-12
              ${isDark ? 'bg-gray-800 border-gray-700 hover:bg-gray-700' : 'bg-white border-gray-200 hover:bg-gray-50'}
              shadow-lg
            `}
          >
            <ChevronLeft className="h-5 w-5" />
          </Button>

          <Button
            variant="outline"
            size="icon"
            onClick={nextSlide}
            className={`
              absolute right-0 top-1/2 -translate-y-1/2 z-10 rounded-full w-12 h-12
              ${isDark ? 'bg-gray-800 border-gray-700 hover:bg-gray-700' : 'bg-white border-gray-200 hover:bg-gray-50'}
              shadow-lg
            `}
          >
            <ChevronRight className="h-5 w-5" />
          </Button>

          {/* Slider Content */}
          <motion.div
            key={currentIndex}
            initial={{ opacity: 0, x: 50 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -50 }}
            transition={{ duration: 0.5 }}
            className="grid grid-cols-1 md:grid-cols-3 gap-6 mx-16"
          >
            {visibleTopics.map((topic, index) => (
              <motion.div
                key={`${topic.id}-${currentIndex}-${index}`}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                whileHover={{ 
                  y: -8,
                  transition: { duration: 0.2 }
                }}
                className="group cursor-pointer"
              >
                <Link href={`/blog?category=${topic.id}`}>
                  <div className={`
                    p-6 rounded-2xl border transition-all duration-300 group-hover:shadow-xl
                    bg-gradient-to-br ${topic.color}
                    group-hover:border-primary/30 h-full
                  `}>
                    {/* Icon and Badge */}
                    <div className="flex items-center justify-between mb-4">
                      <motion.div
                        whileHover={{ 
                          scale: 1.1,
                          rotate: 5
                        }}
                        transition={{ duration: 0.2 }}
                        className={`
                          w-12 h-12 rounded-xl bg-white/10 backdrop-blur-sm flex items-center justify-center
                          ${topic.textColor}
                        `}
                      >
                        {topic.icon}
                      </motion.div>
                      
                      <Badge 
                        variant="secondary" 
                        className={`
                          ${isDark ? 'bg-gray-700 text-gray-300' : 'bg-gray-200 text-gray-700'}
                          font-medium
                        `}
                      >
                        {topic.count} posts
                      </Badge>
                    </div>

                    {/* Content */}
                    <h3 className={`
                      text-xl font-bold mb-2 group-hover:text-primary transition-colors duration-300
                      ${isDark ? 'text-gray-100' : 'text-gray-900'}
                    `}>
                      {topic.name}
                    </h3>
                    
                    <p className={`
                      text-sm mb-4
                      ${isDark ? 'text-gray-300' : 'text-gray-600'}
                    `}>
                      {topic.description}
                    </p>

                    {/* Arrow */}
                    <motion.div
                      className={`flex items-center gap-2 text-sm font-medium ${topic.textColor}`}
                      whileHover={{ x: 4 }}
                      transition={{ duration: 0.2 }}
                    >
                      <span>Explore</span>
                      <ArrowRight className="h-4 w-4" />
                    </motion.div>
                  </div>
                </Link>
              </motion.div>
            ))}
          </motion.div>

          {/* Dots Indicator */}
          <div className="flex justify-center gap-2 mt-8">
            {Array.from({ length: Math.max(1, trendingTopics.length - 2) }).map((_, index) => (
              <button
                key={index}
                onClick={() => setCurrentIndex(index)}
                className={`
                  w-2 h-2 rounded-full transition-all duration-300
                  ${currentIndex === index 
                    ? 'bg-primary w-8' 
                    : isDark ? 'bg-gray-600' : 'bg-gray-300'
                  }
                `}
              />
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}
